推理数据数量: 100
每个序列任务数量: 500
星座卫星数量: 3
星座模式: cooperative
模型: gpn+indrnn
种子: 12348
内存总量: 0.3
电量总量: 5
检查点路径: constellation_smp/constellation_smp100/multi_constellation_comparison_2025_08_25_17_02_29/constellation_gpnindrnn_cooperative_2025_08_25_17_02_29

批次 1:
  奖励值: 94.6136
  收益率: 0.4561
  距离: 22.6608
  内存使用: 0.4062
  能量使用: 0.7057
  推理时间: 2.1018秒

批次 2:
  奖励值: 97.9994
  收益率: 0.4799
  距离: 24.8780
  内存使用: 0.5421
  能量使用: 0.7812
  推理时间: 2.1828秒

批次 3:
  奖励值: 102.5646
  收益率: 0.5140
  距离: 27.8336
  内存使用: 0.5854
  能量使用: 0.8518
  推理时间: 2.2998秒

批次 4:
  奖励值: 93.2573
  收益率: 0.4673
  距离: 25.4189
  内存使用: 0.5028
  能量使用: 0.7638
  推理时间: 2.1122秒

批次 5:
  奖励值: 91.5724
  收益率: 0.4692
  距离: 25.6162
  内存使用: 0.4621
  能量使用: 0.7768
  推理时间: 2.0781秒

批次 6:
  奖励值: 95.9800
  收益率: 0.4971
  距离: 27.1011
  内存使用: 0.5221
  能量使用: 0.8169
  推理时间: 2.2258秒

批次 7:
  奖励值: 95.9373
  收益率: 0.4893
  距离: 26.6339
  内存使用: 0.5068
  能量使用: 0.7847
  推理时间: 2.2602秒

批次 8:
  奖励值: 101.5801
  收益率: 0.5212
  距离: 26.4404
  内存使用: 0.6121
  能量使用: 0.7874
  推理时间: 2.2756秒

批次 9:
  奖励值: 92.8127
  收益率: 0.4862
  距离: 24.6828
  内存使用: 0.4868
  能量使用: 0.8052
  推理时间: 2.1133秒

批次 10:
  奖励值: 99.7322
  收益率: 0.4918
  距离: 26.1758
  内存使用: 0.5464
  能量使用: 0.8134
  推理时间: 2.2250秒

批次 11:
  奖励值: 106.2216
  收益率: 0.5127
  距离: 24.9197
  内存使用: 0.6051
  能量使用: 0.8319
  推理时间: 2.3561秒

批次 12:
  奖励值: 100.2792
  收益率: 0.5061
  距离: 28.8352
  内存使用: 0.5945
  能量使用: 0.8119
  推理时间: 2.3399秒

批次 13:
  奖励值: 89.2823
  收益率: 0.4710
  距离: 26.5661
  内存使用: 0.4917
  能量使用: 0.7777
  推理时间: 2.0967秒

批次 14:
  奖励值: 91.4943
  收益率: 0.4605
  距离: 26.6167
  内存使用: 0.5065
  能量使用: 0.6881
  推理时间: 2.0749秒

批次 15:
  奖励值: 95.7894
  收益率: 0.4858
  距离: 28.7615
  内存使用: 0.4755
  能量使用: 0.7840
  推理时间: 2.1702秒

批次 16:
  奖励值: 96.8318
  收益率: 0.4924
  距离: 26.0349
  内存使用: 0.4914
  能量使用: 0.8100
  推理时间: 2.1985秒

批次 17:
  奖励值: 99.7812
  收益率: 0.5031
  距离: 29.1561
  内存使用: 0.4987
  能量使用: 0.7827
  推理时间: 2.2401秒

批次 18:
  奖励值: 89.8104
  收益率: 0.4736
  距离: 25.7276
  内存使用: 0.4927
  能量使用: 0.7589
  推理时间: 2.1313秒

批次 19:
  奖励值: 104.0818
  收益率: 0.5118
  距离: 28.5451
  内存使用: 0.6377
  能量使用: 0.8536
  推理时间: 2.3351秒

批次 20:
  奖励值: 97.4268
  收益率: 0.4942
  距离: 26.8231
  内存使用: 0.5368
  能量使用: 0.7864
  推理时间: 2.2156秒

批次 21:
  奖励值: 98.4071
  收益率: 0.4971
  距离: 26.9344
  内存使用: 0.5713
  能量使用: 0.8800
  推理时间: 2.2988秒

批次 22:
  奖励值: 97.6381
  收益率: 0.5070
  距离: 27.1421
  内存使用: 0.5549
  能量使用: 0.8269
  推理时间: 2.2702秒

批次 23:
  奖励值: 95.4187
  收益率: 0.4821
  距离: 29.0322
  内存使用: 0.5200
  能量使用: 0.8104
  推理时间: 2.2338秒

批次 24:
  奖励值: 100.6945
  收益率: 0.5330
  距离: 29.5984
  内存使用: 0.5043
  能量使用: 0.9082
  推理时间: 2.2701秒

批次 25:
  奖励值: 94.2326
  收益率: 0.4691
  距离: 25.8792
  内存使用: 0.4928
  能量使用: 0.7629
  推理时间: 2.1534秒

批次 26:
  奖励值: 101.1810
  收益率: 0.5129
  距离: 30.4615
  内存使用: 0.5829
  能量使用: 0.7936
  推理时间: 2.2864秒

批次 27:
  奖励值: 102.8066
  收益率: 0.5214
  距离: 28.8127
  内存使用: 0.6537
  能量使用: 0.9204
  推理时间: 2.3479秒

批次 28:
  奖励值: 96.6647
  收益率: 0.4932
  距离: 29.2988
  内存使用: 0.5091
  能量使用: 0.8626
  推理时间: 2.2150秒

批次 29:
  奖励值: 96.0828
  收益率: 0.4852
  距离: 25.7586
  内存使用: 0.4870
  能量使用: 0.7888
  推理时间: 2.1400秒

批次 30:
  奖励值: 101.7248
  收益率: 0.5213
  距离: 30.4899
  内存使用: 0.5568
  能量使用: 0.9037
  推理时间: 2.3561秒

批次 31:
  奖励值: 108.3026
  收益率: 0.5469
  距离: 30.2156
  内存使用: 0.6177
  能量使用: 0.9614
  推理时间: 2.5404秒

批次 32:
  奖励值: 92.5967
  收益率: 0.4616
  距离: 23.0118
  内存使用: 0.4549
  能量使用: 0.7397
  推理时间: 2.1405秒

批次 33:
  奖励值: 102.9840
  收益率: 0.5177
  距离: 29.0283
  内存使用: 0.5345
  能量使用: 0.8692
  推理时间: 2.3779秒

批次 34:
  奖励值: 102.8613
  收益率: 0.5140
  距离: 24.2821
  内存使用: 0.5741
  能量使用: 0.8213
  推理时间: 2.3803秒

批次 35:
  奖励值: 93.2269
  收益率: 0.4799
  距离: 24.2951
  内存使用: 0.4568
  能量使用: 0.7944
  推理时间: 2.1396秒

批次 36:
  奖励值: 93.7784
  收益率: 0.4621
  距离: 23.6019
  内存使用: 0.4629
  能量使用: 0.7783
  推理时间: 2.1021秒

批次 37:
  奖励值: 99.4461
  收益率: 0.4989
  距离: 25.3014
  内存使用: 0.5725
  能量使用: 0.8338
  推理时间: 2.2985秒

批次 38:
  奖励值: 104.2271
  收益率: 0.5314
  距离: 32.1893
  内存使用: 0.6392
  能量使用: 0.9174
  推理时间: 2.4250秒

批次 39:
  奖励值: 99.1820
  收益率: 0.4904
  距离: 26.0895
  内存使用: 0.5316
  能量使用: 0.8149
  推理时间: 2.2063秒

批次 40:
  奖励值: 91.9960
  收益率: 0.4852
  距离: 23.9494
  内存使用: 0.4512
  能量使用: 0.8075
  推理时间: 2.2472秒

批次 41:
  奖励值: 85.2548
  收益率: 0.4366
  距离: 25.9461
  内存使用: 0.4365
  能量使用: 0.7077
  推理时间: 1.9513秒

批次 42:
  奖励值: 106.0855
  收益率: 0.5303
  距离: 29.4173
  内存使用: 0.5414
  能量使用: 0.8704
  推理时间: 2.4114秒

批次 43:
  奖励值: 105.7827
  收益率: 0.5212
  距离: 27.4056
  内存使用: 0.5597
  能量使用: 0.8692
  推理时间: 2.4020秒

批次 44:
  奖励值: 93.7797
  收益率: 0.4798
  距离: 27.1877
  内存使用: 0.5163
  能量使用: 0.7366
  推理时间: 2.0757秒

批次 45:
  奖励值: 103.3797
  收益率: 0.4960
  距离: 27.7784
  内存使用: 0.6231
  能量使用: 0.8390
  推理时间: 2.3571秒

批次 46:
  奖励值: 96.2764
  收益率: 0.4800
  距离: 24.7709
  内存使用: 0.4667
  能量使用: 0.8097
  推理时间: 2.1872秒

批次 47:
  奖励值: 100.3684
  收益率: 0.5155
  距离: 27.1607
  内存使用: 0.4900
  能量使用: 0.8124
  推理时间: 2.3020秒

批次 48:
  奖励值: 105.1220
  收益率: 0.5395
  距离: 32.6780
  内存使用: 0.5810
  能量使用: 0.8970
  推理时间: 2.4732秒

批次 49:
  奖励值: 96.4071
  收益率: 0.4755
  距离: 25.1259
  内存使用: 0.5115
  能量使用: 0.7485
  推理时间: 2.1655秒

批次 50:
  奖励值: 102.6019
  收益率: 0.5028
  距离: 26.6259
  内存使用: 0.5488
  能量使用: 0.8216
  推理时间: 2.2441秒

批次 51:
  奖励值: 93.6404
  收益率: 0.4836
  距离: 26.2599
  内存使用: 0.4984
  能量使用: 0.7609
  推理时间: 2.1949秒

批次 52:
  奖励值: 94.9779
  收益率: 0.4900
  距离: 28.2389
  内存使用: 0.5133
  能量使用: 0.8919
  推理时间: 2.2778秒

批次 53:
  奖励值: 94.9568
  收益率: 0.4964
  距离: 31.9193
  内存使用: 0.5535
  能量使用: 0.8135
  推理时间: 2.2377秒

批次 54:
  奖励值: 103.8390
  收益率: 0.5152
  距离: 28.8349
  内存使用: 0.5974
  能量使用: 0.8897
  推理时间: 2.3845秒

批次 55:
  奖励值: 97.7257
  收益率: 0.4954
  距离: 27.2780
  内存使用: 0.4750
  能量使用: 0.7955
  推理时间: 2.2374秒

批次 56:
  奖励值: 97.3913
  收益率: 0.4906
  距离: 27.3083
  内存使用: 0.5256
  能量使用: 0.7833
  推理时间: 2.1686秒

批次 57:
  奖励值: 94.0475
  收益率: 0.4801
  距离: 28.2034
  内存使用: 0.4930
  能量使用: 0.8087
  推理时间: 2.1731秒

批次 58:
  奖励值: 93.4063
  收益率: 0.4635
  距离: 25.1551
  内存使用: 0.4780
  能量使用: 0.7543
  推理时间: 2.1199秒

批次 59:
  奖励值: 110.8538
  收益率: 0.5571
  距离: 27.4835
  内存使用: 0.6369
  能量使用: 0.9720
  推理时间: 2.5129秒

批次 60:
  奖励值: 101.1065
  收益率: 0.5007
  距离: 27.4729
  内存使用: 0.4995
  能量使用: 0.8145
  推理时间: 2.2545秒

批次 61:
  奖励值: 95.1245
  收益率: 0.4769
  距离: 25.2981
  内存使用: 0.4525
  能量使用: 0.8005
  推理时间: 2.1251秒

批次 62:
  奖励值: 106.9417
  收益率: 0.5527
  距离: 28.8010
  内存使用: 0.5706
  能量使用: 0.9450
  推理时间: 2.3915秒

批次 63:
  奖励值: 99.6651
  收益率: 0.4900
  距离: 30.5926
  内存使用: 0.6060
  能量使用: 0.8712
  推理时间: 2.3096秒

批次 64:
  奖励值: 99.2448
  收益率: 0.4978
  距离: 26.3750
  内存使用: 0.4900
  能量使用: 0.8453
  推理时间: 2.2356秒

批次 65:
  奖励值: 96.3413
  收益率: 0.4857
  距离: 27.3415
  内存使用: 0.5661
  能量使用: 0.8098
  推理时间: 2.1791秒

批次 66:
  奖励值: 105.5489
  收益率: 0.5342
  距离: 29.2192
  内存使用: 0.5871
  能量使用: 0.8960
  推理时间: 2.4091秒

批次 67:
  奖励值: 104.7724
  收益率: 0.5246
  距离: 30.4079
  内存使用: 0.5471
  能量使用: 0.9002
  推理时间: 2.4211秒

批次 68:
  奖励值: 102.1108
  收益率: 0.5102
  距离: 29.0148
  内存使用: 0.5645
  能量使用: 0.7992
  推理时间: 2.3048秒

批次 69:
  奖励值: 99.1448
  收益率: 0.4987
  距离: 22.7318
  内存使用: 0.5387
  能量使用: 0.8398
  推理时间: 2.2388秒

批次 70:
  奖励值: 105.7181
  收益率: 0.5198
  距离: 25.6973
  内存使用: 0.5877
  能量使用: 0.8788
  推理时间: 2.3426秒

批次 71:
  奖励值: 91.2322
  收益率: 0.4519
  距离: 23.7626
  内存使用: 0.4505
  能量使用: 0.7224
  推理时间: 2.0038秒

批次 72:
  奖励值: 91.7327
  收益率: 0.4934
  距离: 23.8631
  内存使用: 0.4896
  能量使用: 0.7820
  推理时间: 2.1218秒

批次 73:
  奖励值: 99.9741
  收益率: 0.5040
  距离: 26.6074
  内存使用: 0.5975
  能量使用: 0.8469
  推理时间: 2.3060秒

批次 74:
  奖励值: 98.6011
  收益率: 0.4957
  距离: 29.8771
  内存使用: 0.5582
  能量使用: 0.7625
  推理时间: 2.2730秒

批次 75:
  奖励值: 103.6167
  收益率: 0.5185
  距离: 29.0568
  内存使用: 0.5872
  能量使用: 0.9226
  推理时间: 2.3801秒

批次 76:
  奖励值: 90.8871
  收益率: 0.4615
  距离: 25.5289
  内存使用: 0.5114
  能量使用: 0.7855
  推理时间: 2.0990秒

批次 77:
  奖励值: 98.1214
  收益率: 0.5011
  距离: 28.4990
  内存使用: 0.8157
  能量使用: 0.8424
  推理时间: 2.3512秒

批次 78:
  奖励值: 98.3845
  收益率: 0.4817
  距离: 29.8440
  内存使用: 0.8836
  能量使用: 0.9034
  推理时间: 2.3152秒

批次 79:
  奖励值: 96.2741
  收益率: 0.4921
  距离: 24.4921
  内存使用: 0.5015
  能量使用: 0.8582
  推理时间: 2.1925秒

批次 80:
  奖励值: 92.0971
  收益率: 0.4870
  距离: 24.3462
  内存使用: 0.4870
  能量使用: 0.7726
  推理时间: 2.0967秒

批次 81:
  奖励值: 104.0421
  收益率: 0.5221
  距离: 31.0359
  内存使用: 0.5634
  能量使用: 0.9164
  推理时间: 2.3592秒

批次 82:
  奖励值: 103.0893
  收益率: 0.5192
  距离: 29.9628
  内存使用: 0.5764
  能量使用: 0.8822
  推理时间: 2.4126秒

批次 83:
  奖励值: 99.5085
  收益率: 0.4893
  距离: 24.6309
  内存使用: 0.4866
  能量使用: 0.8655
  推理时间: 2.2507秒

批次 84:
  奖励值: 93.5019
  收益率: 0.4762
  距离: 27.7925
  内存使用: 0.7843
  能量使用: 0.8193
  推理时间: 2.2089秒

批次 85:
  奖励值: 98.8539
  收益率: 0.4969
  距离: 26.2253
  内存使用: 0.4734
  能量使用: 0.7935
  推理时间: 2.2317秒

批次 86:
  奖励值: 93.8532
  收益率: 0.4739
  距离: 28.5940
  内存使用: 0.4542
  能量使用: 0.8454
  推理时间: 2.1673秒

批次 87:
  奖励值: 89.1304
  收益率: 0.4640
  距离: 24.1025
  内存使用: 0.5394
  能量使用: 0.6973
  推理时间: 2.0502秒

批次 88:
  奖励值: 88.0857
  收益率: 0.4623
  距离: 24.8267
  内存使用: 0.4408
  能量使用: 0.7611
  推理时间: 1.9951秒

批次 89:
  奖励值: 97.5787
  收益率: 0.5013
  距离: 26.9026
  内存使用: 0.5295
  能量使用: 0.8106
  推理时间: 2.2490秒

批次 90:
  奖励值: 97.3253
  收益率: 0.4940
  距离: 24.8159
  内存使用: 0.5189
  能量使用: 0.8145
  推理时间: 2.2611秒

批次 91:
  奖励值: 97.9669
  收益率: 0.4976
  距离: 29.2345
  内存使用: 0.5253
  能量使用: 0.9064
  推理时间: 2.2661秒

批次 92:
  奖励值: 94.4652
  收益率: 0.4952
  距离: 26.3565
  内存使用: 0.5468
  能量使用: 0.7664
  推理时间: 2.1580秒

批次 93:
  奖励值: 104.1623
  收益率: 0.5337
  距离: 29.6921
  内存使用: 0.4982
  能量使用: 0.8850
  推理时间: 2.4327秒

批次 94:
  奖励值: 94.7798
  收益率: 0.5105
  距离: 28.2929
  内存使用: 0.5298
  能量使用: 0.8176
  推理时间: 2.2795秒

批次 95:
  奖励值: 105.5292
  收益率: 0.5287
  距离: 28.9220
  内存使用: 0.5571
  能量使用: 0.8625
  推理时间: 2.4849秒

批次 96:
  奖励值: 98.0003
  收益率: 0.5079
  距离: 26.8109
  内存使用: 0.5151
  能量使用: 0.8734
  推理时间: 2.2560秒

批次 97:
  奖励值: 100.8490
  收益率: 0.5050
  距离: 29.2923
  内存使用: 0.5856
  能量使用: 0.8241
  推理时间: 2.2327秒

批次 98:
  奖励值: 83.6928
  收益率: 0.4505
  距离: 25.4521
  内存使用: 0.4462
  能量使用: 0.7162
  推理时间: 2.0257秒

批次 99:
  奖励值: 99.3315
  收益率: 0.4930
  距离: 25.6624
  内存使用: 0.4896
  能量使用: 0.7851
  推理时间: 2.3053秒

批次 100:
  奖励值: 96.1159
  收益率: 0.4782
  距离: 27.6153
  内存使用: 0.5164
  能量使用: 0.8365
  推理时间: 2.2871秒


==================== 总结 ====================
平均收益率: 0.4962
平均能量使用: 0.8223
平均推理时间: 2.2460秒
