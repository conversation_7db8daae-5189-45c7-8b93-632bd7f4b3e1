Transformer配置消融实验
================================================================================
实验时间: 2025_08_28_10_36_19
设备: cuda
问题规模: 100节点, 3卫星
训练配置: 3轮, 批次大小64
将运行 8 个配置:
  1. baseline_no_transformer: 基线配置，不使用Transformer
  2. lightweight: 轻量级配置: 1层, 2头, 128维
  3. default: 默认配置: 2层, 4头, 256维
  4. deep: 深层配置: 3层, 8头, 512维
  5. ultra_deep: 超深层配置: 4层, 8头, 256维
  6. wide: 宽模型配置: 2层, 16头, 512维
  7. high_dropout: 高dropout配置: dropout=0.3
  8. relu_activation: ReLU激活函数配置
使用星座模式: hybrid

开始运行配置 1/8

================================================================================
开始运行配置: baseline_no_transformer
描述: 基线配置，不使用Transformer
================================================================================
基线配置: 不使用Transformer
