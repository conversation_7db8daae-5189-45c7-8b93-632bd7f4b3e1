推理数据数量: 100
每个序列任务数量: 1000
星座卫星数量: 3
星座模式: competitive
模型: gpn+indrnn
种子: 12348
内存总量: 0.3
电量总量: 5
检查点路径: constellation_smp/constellation_smp100/multi_constellation_comparison_2025_08_25_17_02_29/constellation_gpnindrnn_competitive_2025_08_26_05_01_59

批次 1:
  奖励值: 126.0851
  收益率: 0.3273
  距离: 33.2807
  内存使用: 0.7000
  能量使用: 1.0491
  推理时间: 2.7626秒

批次 2:
  奖励值: 130.8124
  收益率: 0.3292
  距离: 35.8385
  内存使用: 0.7158
  能量使用: 1.0209
  推理时间: 2.7449秒

批次 3:
  奖励值: 115.2674
  收益率: 0.2879
  距离: 31.5190
  内存使用: 0.8995
  能量使用: 0.8467
  推理时间: 2.5076秒

批次 4:
  奖励值: 125.6613
  收益率: 0.3123
  距离: 32.3911
  内存使用: 0.7204
  能量使用: 0.9660
  推理时间: 2.5136秒

批次 5:
  奖励值: 117.5262
  收益率: 0.2966
  距离: 30.1185
  内存使用: 0.8998
  能量使用: 0.9293
  推理时间: 2.5408秒

批次 6:
  奖励值: 122.6489
  收益率: 0.3058
  距离: 28.1365
  内存使用: 0.6171
  能量使用: 0.9487
  推理时间: 2.5871秒

批次 7:
  奖励值: 120.8199
  收益率: 0.3094
  距离: 31.3800
  内存使用: 0.6457
  能量使用: 0.9345
  推理时间: 2.5987秒

批次 8:
  奖励值: 125.6617
  收益率: 0.3197
  距离: 33.7595
  内存使用: 0.7223
  能量使用: 0.9948
  推理时间: 2.7938秒

批次 9:
  奖励值: 136.6295
  收益率: 0.3438
  距离: 34.8101
  内存使用: 0.7279
  能量使用: 1.0029
  推理时间: 2.9455秒

批次 10:
  奖励值: 129.5843
  收益率: 0.3231
  距离: 34.6200
  内存使用: 0.6183
  能量使用: 0.9930
  推理时间: 2.7424秒

批次 11:
  奖励值: 118.3282
  收益率: 0.2889
  距离: 29.2779
  内存使用: 0.5663
  能量使用: 0.8994
  推理时间: 2.5800秒

批次 12:
  奖励值: 139.9507
  收益率: 0.3523
  距离: 31.8586
  内存使用: 0.7596
  能量使用: 1.0804
  推理时间: 2.9144秒

批次 13:
  奖励值: 114.0567
  收益率: 0.2902
  距离: 31.5490
  内存使用: 0.6365
  能量使用: 0.9012
  推理时间: 2.3772秒

批次 14:
  奖励值: 122.6128
  收益率: 0.3081
  距离: 28.7963
  内存使用: 0.6692
  能量使用: 0.9089
  推理时间: 4.2388秒

批次 15:
  奖励值: 128.7306
  收益率: 0.3284
  距离: 33.8512
  内存使用: 0.7314
  能量使用: 0.9561
  推理时间: 2.5625秒

批次 16:
  奖励值: 125.0485
  收益率: 0.3181
  距离: 34.3302
  内存使用: 0.6255
  能量使用: 0.9473
  推理时间: 2.6539秒

批次 17:
  奖励值: 132.6891
  收益率: 0.3377
  距离: 36.4742
  内存使用: 0.6583
  能量使用: 1.0892
  推理时间: 2.8796秒

批次 18:
  奖励值: 131.5090
  收益率: 0.3350
  距离: 33.2666
  内存使用: 0.7109
  能量使用: 1.0044
  推理时间: 2.7535秒

批次 19:
  奖励值: 124.5099
  收益率: 0.3121
  距离: 31.0942
  内存使用: 0.7115
  能量使用: 0.9744
  推理时间: 2.6593秒

批次 20:
  奖励值: 137.6692
  收益率: 0.3377
  距离: 31.7099
  内存使用: 0.6929
  能量使用: 1.0586
  推理时间: 2.8706秒

批次 21:
  奖励值: 130.8245
  收益率: 0.3119
  距离: 31.4499
  内存使用: 0.6297
  能量使用: 1.0713
  推理时间: 2.7638秒

批次 22:
  奖励值: 129.4171
  收益率: 0.3240
  距离: 34.6470
  内存使用: 0.7454
  能量使用: 0.9735
  推理时间: 2.7125秒

批次 23:
  奖励值: 120.0392
  收益率: 0.3059
  距离: 31.5551
  内存使用: 0.6643
  能量使用: 0.9021
  推理时间: 2.5771秒

批次 24:
  奖励值: 108.0038
  收益率: 0.2720
  距离: 29.7575
  内存使用: 0.8995
  能量使用: 0.9354
  推理时间: 2.3552秒

批次 25:
  奖励值: 136.1543
  收益率: 0.3280
  距离: 32.8105
  内存使用: 0.6744
  能量使用: 1.0066
  推理时间: 2.8586秒

批次 26:
  奖励值: 120.8782
  收益率: 0.3066
  距离: 30.9356
  内存使用: 0.6842
  能量使用: 1.0238
  推理时间: 2.6212秒

批次 27:
  奖励值: 119.7206
  收益率: 0.3071
  距离: 30.9917
  内存使用: 0.6649
  能量使用: 0.9021
  推理时间: 2.8278秒

批次 28:
  奖励值: 123.2956
  收益率: 0.3080
  距离: 30.7960
  内存使用: 0.6392
  能量使用: 0.9736
  推理时间: 2.8076秒

批次 29:
  奖励值: 138.5685
  收益率: 0.3447
  距离: 33.1590
  内存使用: 0.7466
  能量使用: 1.1574
  推理时间: 2.9057秒

批次 30:
  奖励值: 126.5478
  收益率: 0.3074
  距离: 31.6929
  内存使用: 0.6344
  能量使用: 0.9770
  推理时间: 2.6632秒

批次 31:
  奖励值: 133.6019
  收益率: 0.3374
  距离: 33.9096
  内存使用: 0.6503
  能量使用: 1.0362
  推理时间: 2.8660秒

批次 32:
  奖励值: 117.4369
  收益率: 0.2963
  距离: 31.8562
  内存使用: 0.8996
  能量使用: 0.9704
  推理时间: 2.6026秒

批次 33:
  奖励值: 131.4128
  收益率: 0.3294
  距离: 34.8835
  内存使用: 0.6849
  能量使用: 1.0059
  推理时间: 3.2060秒

批次 34:
  奖励值: 121.1164
  收益率: 0.3017
  距离: 31.3787
  内存使用: 0.6512
  能量使用: 0.9372
  推理时间: 2.6433秒

批次 35:
  奖励值: 127.3891
  收益率: 0.3206
  距离: 32.8409
  内存使用: 0.7036
  能量使用: 1.0773
  推理时间: 2.7573秒

批次 36:
  奖励值: 139.2730
  收益率: 0.3535
  距离: 38.2371
  内存使用: 0.6999
  能量使用: 1.1169
  推理时间: 2.9732秒

批次 37:
  奖励值: 127.2096
  收益率: 0.3215
  距离: 32.5765
  内存使用: 0.7509
  能量使用: 1.0535
  推理时间: 2.7493秒

批次 38:
  奖励值: 119.6423
  收益率: 0.3056
  距离: 32.5384
  内存使用: 0.6573
  能量使用: 0.9196
  推理时间: 2.6768秒

批次 39:
  奖励值: 123.7680
  收益率: 0.3186
  距离: 29.9610
  内存使用: 0.8976
  能量使用: 1.0054
  推理时间: 2.4623秒

批次 40:
  奖励值: 129.3690
  收益率: 0.3209
  距离: 30.5953
  内存使用: 0.6312
  能量使用: 1.0354
  推理时间: 2.5505秒

批次 41:
  奖励值: 124.2981
  收益率: 0.3081
  距离: 27.7702
  内存使用: 0.6177
  能量使用: 1.0160
  推理时间: 2.6293秒

批次 42:
  奖励值: 123.0800
  收益率: 0.3050
  距离: 30.9668
  内存使用: 0.6543
  能量使用: 1.0190
  推理时间: 2.6166秒

批次 43:
  奖励值: 121.4748
  收益率: 0.3146
  距离: 33.5668
  内存使用: 0.6920
  能量使用: 0.9917
  推理时间: 2.6183秒

批次 44:
  奖励值: 114.9959
  收益率: 0.2956
  距离: 31.2384
  内存使用: 0.8994
  能量使用: 0.8704
  推理时间: 2.8047秒

批次 45:
  奖励值: 137.6701
  收益率: 0.3446
  距离: 33.2895
  内存使用: 0.6649
  能量使用: 1.1053
  推理时间: 2.8525秒

批次 46:
  奖励值: 127.8546
  收益率: 0.3280
  距离: 35.4170
  内存使用: 0.6407
  能量使用: 1.0331
  推理时间: 2.7653秒

批次 47:
  奖励值: 122.2612
  收益率: 0.3030
  距离: 28.6441
  内存使用: 0.6132
  能量使用: 1.0202
  推理时间: 2.6101秒

批次 48:
  奖励值: 106.9650
  收益率: 0.2799
  距离: 29.1109
  内存使用: 0.8999
  能量使用: 0.8592
  推理时间: 2.6513秒

批次 49:
  奖励值: 113.9059
  收益率: 0.2832
  距离: 27.5254
  内存使用: 0.6092
  能量使用: 0.8536
  推理时间: 2.4079秒

批次 50:
  奖励值: 123.2915
  收益率: 0.3059
  距离: 34.5567
  内存使用: 0.6666
  能量使用: 0.9937
  推理时间: 3.1023秒

批次 51:
  奖励值: 128.6658
  收益率: 0.3181
  距离: 32.6485
  内存使用: 0.6882
  能量使用: 0.9316
  推理时间: 2.6172秒

批次 52:
  奖励值: 126.1201
  收益率: 0.3146
  距离: 33.2774
  内存使用: 0.7063
  能量使用: 0.9760
  推理时间: 2.5872秒

批次 53:
  奖励值: 137.0525
  收益率: 0.3572
  距离: 37.3564
  内存使用: 0.7240
  能量使用: 1.0145
  推理时间: 2.7961秒

批次 54:
  奖励值: 124.1831
  收益率: 0.3092
  距离: 30.6788
  内存使用: 0.6740
  能量使用: 1.0810
  推理时间: 2.6120秒

批次 55:
  奖励值: 114.6850
  收益率: 0.2917
  距离: 33.1995
  内存使用: 0.8994
  能量使用: 0.9311
  推理时间: 2.5908秒

批次 56:
  奖励值: 122.8114
  收益率: 0.3126
  距离: 37.6809
  内存使用: 0.6795
  能量使用: 1.0168
  推理时间: 2.7714秒

批次 57:
  奖励值: 108.4279
  收益率: 0.2840
  距离: 29.3548
  内存使用: 0.8930
  能量使用: 0.8963
  推理时间: 2.3185秒

批次 58:
  奖励值: 115.6814
  收益率: 0.2875
  距离: 27.5522
  内存使用: 0.8996
  能量使用: 0.9853
  推理时间: 2.3241秒

批次 59:
  奖励值: 132.7665
  收益率: 0.3321
  距离: 30.7116
  内存使用: 0.7169
  能量使用: 1.0384
  推理时间: 2.8115秒

批次 60:
  奖励值: 117.8862
  收益率: 0.3026
  距离: 32.9102
  内存使用: 0.6027
  能量使用: 0.9146
  推理时间: 2.5458秒

批次 61:
  奖励值: 125.5897
  收益率: 0.3173
  距离: 32.3901
  内存使用: 0.7116
  能量使用: 0.9189
  推理时间: 2.7406秒

批次 62:
  奖励值: 131.4564
  收益率: 0.3321
  距离: 34.4858
  内存使用: 0.7660
  能量使用: 1.0355
  推理时间: 2.9468秒

批次 63:
  奖励值: 122.5901
  收益率: 0.3032
  距离: 32.0111
  内存使用: 0.6579
  能量使用: 0.9507
  推理时间: 2.5158秒

批次 64:
  奖励值: 130.3169
  收益率: 0.3288
  距离: 33.0359
  内存使用: 0.6806
  能量使用: 1.0496
  推理时间: 2.6492秒

批次 65:
  奖励值: 125.4683
  收益率: 0.3109
  距离: 29.5791
  内存使用: 0.6734
  能量使用: 1.0110
  推理时间: 2.7365秒

批次 66:
  奖励值: 124.5267
  收益率: 0.3118
  距离: 31.0117
  内存使用: 0.7346
  能量使用: 1.0002
  推理时间: 2.6328秒

批次 67:
  奖励值: 116.3796
  收益率: 0.2928
  距离: 29.5535
  内存使用: 0.8996
  能量使用: 0.9876
  推理时间: 2.3854秒

批次 68:
  奖励值: 124.1244
  收益率: 0.3138
  距离: 31.8973
  内存使用: 0.6365
  能量使用: 1.0174
  推理时间: 2.5263秒

批次 69:
  奖励值: 121.2015
  收益率: 0.3033
  距离: 30.1768
  内存使用: 0.6683
  能量使用: 0.9519
  推理时间: 2.5911秒

批次 70:
  奖励值: 116.5673
  收益率: 0.2909
  距离: 28.1447
  内存使用: 0.5991
  能量使用: 0.9216
  推理时间: 2.6062秒

批次 71:
  奖励值: 123.0417
  收益率: 0.3016
  距离: 26.9250
  内存使用: 0.6316
  能量使用: 0.9601
  推理时间: 2.4970秒

批次 72:
  奖励值: 131.0233
  收益率: 0.3306
  距离: 33.1980
  内存使用: 0.7158
  能量使用: 1.0396
  推理时间: 2.7115秒

批次 73:
  奖励值: 123.9547
  收益率: 0.3243
  距离: 36.1933
  内存使用: 0.6669
  能量使用: 1.0304
  推理时间: 2.5631秒

批次 74:
  奖励值: 130.3844
  收益率: 0.3168
  距离: 33.7395
  内存使用: 0.7172
  能量使用: 0.9745
  推理时间: 3.0037秒

批次 75:
  奖励值: 121.3742
  收益率: 0.2887
  距离: 28.4210
  内存使用: 0.6314
  能量使用: 0.9993
  推理时间: 2.5960秒

批次 76:
  奖励值: 136.7012
  收益率: 0.3448
  距离: 32.7512
  内存使用: 0.6833
  能量使用: 0.9630
  推理时间: 2.8720秒

批次 77:
  奖励值: 128.2464
  收益率: 0.3145
  距离: 33.8230
  内存使用: 0.6602
  能量使用: 1.0239
  推理时间: 2.6913秒

批次 78:
  奖励值: 131.8748
  收益率: 0.3334
  距离: 33.1180
  内存使用: 0.6804
  能量使用: 1.0519
  推理时间: 2.7114秒

批次 79:
  奖励值: 110.1390
  收益率: 0.2785
  距离: 30.5623
  内存使用: 0.6247
  能量使用: 0.9351
  推理时间: 2.4612秒

批次 80:
  奖励值: 107.8711
  收益率: 0.2683
  距离: 27.6770
  内存使用: 0.8997
  能量使用: 0.8811
  推理时间: 2.4933秒

批次 81:
  奖励值: 114.3525
  收益率: 0.2852
  距离: 29.7085
  内存使用: 0.8935
  能量使用: 0.8929
  推理时间: 2.5966秒

批次 82:
  奖励值: 125.7736
  收益率: 0.3230
  距离: 34.1051
  内存使用: 0.6992
  能量使用: 1.0485
  推理时间: 2.7802秒

批次 83:
  奖励值: 118.5275
  收益率: 0.3002
  距离: 32.0096
  内存使用: 0.6225
  能量使用: 0.9129
  推理时间: 2.4073秒

批次 84:
  奖励值: 120.4294
  收益率: 0.3058
  距离: 29.7489
  内存使用: 0.6333
  能量使用: 0.9590
  推理时间: 2.6601秒

批次 85:
  奖励值: 116.5735
  收益率: 0.2906
  距离: 28.1257
  内存使用: 0.6041
  能量使用: 0.9232
  推理时间: 2.6386秒

批次 86:
  奖励值: 119.6732
  收益率: 0.3139
  距离: 33.0317
  内存使用: 0.6302
  能量使用: 0.9916
  推理时间: 2.8007秒

批次 87:
  奖励值: 114.0796
  收益率: 0.2993
  距离: 31.3648
  内存使用: 0.8998
  能量使用: 0.9763
  推理时间: 2.5417秒

批次 88:
  奖励值: 120.5563
  收益率: 0.3089
  距离: 32.6671
  内存使用: 0.7107
  能量使用: 0.9096
  推理时间: 2.6072秒

批次 89:
  奖励值: 130.8123
  收益率: 0.3271
  距离: 34.0885
  内存使用: 0.7090
  能量使用: 0.9778
  推理时间: 2.7862秒

批次 90:
  奖励值: 137.5237
  收益率: 0.3494
  距离: 39.9096
  内存使用: 0.8320
  能量使用: 1.2111
  推理时间: 2.9599秒

批次 91:
  奖励值: 124.8313
  收益率: 0.3156
  距离: 31.1570
  内存使用: 0.6213
  能量使用: 0.9590
  推理时间: 2.6214秒

批次 92:
  奖励值: 122.3700
  收益率: 0.3086
  距离: 31.5959
  内存使用: 0.6809
  能量使用: 0.9831
  推理时间: 2.5854秒

批次 93:
  奖励值: 134.0272
  收益率: 0.3317
  距离: 34.8978
  内存使用: 0.7847
  能量使用: 1.0632
  推理时间: 2.8616秒

批次 94:
  奖励值: 109.2363
  收益率: 0.2745
  距离: 31.5054
  内存使用: 0.8997
  能量使用: 0.9223
  推理时间: 2.4076秒

批次 95:
  奖励值: 122.1485
  收益率: 0.3096
  距离: 36.2868
  内存使用: 0.6488
  能量使用: 0.9673
  推理时间: 2.6302秒

批次 96:
  奖励值: 111.0742
  收益率: 0.2789
  距离: 26.9644
  内存使用: 0.8847
  能量使用: 1.0024
  推理时间: 2.4256秒

批次 97:
  奖励值: 125.6947
  收益率: 0.3131
  距离: 31.5096
  内存使用: 0.6835
  能量使用: 1.0174
  推理时间: 2.6456秒

批次 98:
  奖励值: 126.3236
  收益率: 0.3203
  距离: 29.6762
  内存使用: 0.6752
  能量使用: 1.0013
  推理时间: 2.7306秒

批次 99:
  奖励值: 132.4085
  收益率: 0.3326
  距离: 35.1675
  内存使用: 0.7600
  能量使用: 1.0555
  推理时间: 2.8129秒

批次 100:
  奖励值: 125.9260
  收益率: 0.3180
  距离: 29.6779
  内存使用: 0.6451
  能量使用: 1.0250
  推理时间: 2.6696秒


==================== 总结 ====================
平均收益率: 0.3127
平均能量使用: 0.9864
平均推理时间: 2.6894秒
