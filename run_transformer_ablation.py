"""
快速运行Transformer配置消融实验的脚本
基于消融实验设计方案.md中的Transformer配置消融部分

使用方法:
python run_transformer_ablation.py --epochs 3 --train_size 1000
"""

import os
import sys
import subprocess
import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from hyperparameter import args

def print_experiment_info():
    """打印实验信息"""
    print("🧪 Transformer配置消融实验")
    print("=" * 60)
    print("基于消融实验设计方案.md的实验设计")
    print()
    print("实验配置:")
    print("  📊 baseline_no_transformer: 无Transformer基线")
    print("  📊 lightweight: 轻量级 (1层, 2头, 128维)")
    print("  📊 default: 默认配置 (2层, 4头, 256维)")
    print("  📊 deep: 深层配置 (3层, 8头, 512维)")
    print("  📊 ultra_deep: 超深层 (4层, 8头, 256维)")
    print("  📊 wide: 宽模型 (2层, 16头, 512维)")
    print("  📊 high_dropout: 高dropout (dropout=0.3)")
    print("  📊 relu_activation: ReLU激活函数")
    print()
    print("评估指标:")
    print("  • 任务完成率 (Task Completion Rate)")
    print("  • 平均收益率 (Average Revenue Rate)")
    print("  • 能量利用效率 (Energy Utilization Efficiency)")
    print("  • 模型复杂度 (Model Parameters)")
    print("  • 训练时间 (Training Time)")
    print()

def run_quick_ablation():
    """运行快速消融实验"""
    print_experiment_info()
    
    print(f"当前实验参数:")
    print(f"  节点数: {args.num_nodes}")
    print(f"  卫星数: {args.num_satellites}")
    print(f"  训练轮数: {args.epochs}")
    print(f"  批次大小: {args.batch_size}")
    print(f"  训练数据量: {args.train_size}")
    print(f"  验证数据量: {args.valid_size}")
    print()
    
    # 确认是否继续
    response = input("是否开始消融实验? (y/n): ")
    if response.lower() != 'y':
        print("实验已取消")
        return
    
    print("\n🚀 开始Transformer配置消融实验...")
    start_time = datetime.datetime.now()
    print(f"开始时间: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # 运行消融实验
        result = subprocess.run([
            sys.executable, 
            "transformer_ablation_study.py"
        ], capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode == 0:
            print("\n✅ 消融实验成功完成!")
            print("输出:")
            print(result.stdout)
        else:
            print("\n❌ 消融实验失败!")
            print("错误信息:")
            print(result.stderr)
            
    except Exception as e:
        print(f"\n❌ 运行消融实验时出错: {e}")
    
    end_time = datetime.datetime.now()
    duration = end_time - start_time
    print(f"\n实验总耗时: {duration}")
    print(f"结束时间: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")

def run_full_ablation_with_modes():
    """运行完整的消融实验（包含多星座模式）"""
    print("🔬 完整Transformer消融实验")
    print("=" * 60)
    print("将对每种星座模式运行Transformer配置消融")
    print()
    
    constellation_modes = ['cooperative', 'competitive', 'hybrid']
    
    print("实验计划:")
    for i, mode in enumerate(constellation_modes):
        print(f"  {i+1}. {mode.upper()} 模式 - 8种Transformer配置")
    print(f"  总计: {len(constellation_modes)} × 8 = 24 个实验")
    print()
    
    # 估算时间
    estimated_time_per_config = args.epochs * 2  # 分钟
    total_estimated_time = len(constellation_modes) * 8 * estimated_time_per_config
    print(f"预估总时间: {total_estimated_time} 分钟 ({total_estimated_time/60:.1f} 小时)")
    print()
    
    response = input("是否开始完整消融实验? (y/n): ")
    if response.lower() != 'y':
        print("实验已取消")
        return
    
    print("\n🚀 开始完整Transformer配置消融实验...")
    
    for mode in constellation_modes:
        print(f"\n{'='*60}")
        print(f"开始 {mode.upper()} 模式的消融实验")
        print(f"{'='*60}")
        
        try:
            # 为每个模式运行消融实验
            # 这里可以调用修改后的训练脚本
            result = subprocess.run([
                sys.executable,
                "train_multi_constellation_modes transformer.py",
                "--ablation",
                f"--constellation_mode={mode}"
            ], capture_output=True, text=True, encoding='utf-8')
            
            if result.returncode == 0:
                print(f"✅ {mode} 模式消融实验完成")
            else:
                print(f"❌ {mode} 模式消融实验失败")
                print(result.stderr)
                
        except Exception as e:
            print(f"❌ {mode} 模式实验出错: {e}")

def print_usage():
    """打印使用说明"""
    print("Transformer配置消融实验运行脚本")
    print("=" * 50)
    print("使用方法:")
    print("  python run_transformer_ablation.py                    # 快速消融实验")
    print("  python run_transformer_ablation.py --full             # 完整消融实验")
    print("  python run_transformer_ablation.py --epochs 5         # 自定义训练轮数")
    print("  python run_transformer_ablation.py --train_size 5000  # 自定义数据量")
    print()
    print("实验类型:")
    print("  🧪 快速实验: 单一星座模式(hybrid) × 8种Transformer配置")
    print("  🔬 完整实验: 3种星座模式 × 8种Transformer配置 = 24个实验")
    print()
    print("配置说明:")
    print("  • baseline_no_transformer: 无Transformer基线")
    print("  • lightweight: 轻量级配置")
    print("  • default: 默认配置")
    print("  • deep: 深层配置")
    print("  • ultra_deep: 超深层配置")
    print("  • wide: 宽模型配置")
    print("  • high_dropout: 高dropout配置")
    print("  • relu_activation: ReLU激活函数配置")

if __name__ == '__main__':
    # 检查命令行参数
    if len(sys.argv) > 1 and sys.argv[1] in ['-h', '--help', 'help']:
        print_usage()
        sys.exit(0)
    
    # 检查是否运行完整实验
    is_full = '--full' in sys.argv
    
    if is_full:
        run_full_ablation_with_modes()
    else:
        run_quick_ablation()
