推理数据数量: 100
每个序列任务数量: 100
星座卫星数量: 3
星座模式: cooperative
模型: gpn+indrnn
种子: 12348
内存总量: 0.3
电量总量: 5
检查点路径: constellation_smp/constellation_smp100/multi_constellation_comparison_2025_08_25_17_02_29/constellation_gpnindrnn_cooperative_2025_08_25_17_02_29

批次 1:
  奖励值: 30.1847
  收益率: 0.7694
  距离: 8.7831
  内存使用: -0.0119
  能量使用: 0.2593
  推理时间: 1.0690秒

批次 2:
  奖励值: 31.5812
  收益率: 0.7802
  距离: 9.0061
  内存使用: -0.0102
  能量使用: 0.2417
  推理时间: 0.6782秒

批次 3:
  奖励值: 32.0928
  收益率: 0.7974
  距离: 8.7256
  内存使用: 0.0030
  能量使用: 0.2858
  推理时间: 0.7167秒

批次 4:
  奖励值: 30.9344
  收益率: 0.7792
  距离: 9.8294
  内存使用: -0.0153
  能量使用: 0.3171
  推理时间: 0.7356秒

批次 5:
  奖励值: 29.5698
  收益率: 0.7568
  距离: 7.2728
  内存使用: -0.0650
  能量使用: 0.2526
  推理时间: 0.6621秒

批次 6:
  奖励值: 35.9471
  收益率: 0.8452
  距离: 10.7950
  内存使用: -0.0062
  能量使用: 0.3170
  推理时间: 0.8753秒

批次 7:
  奖励值: 27.6589
  收益率: 0.7350
  距离: 10.4110
  内存使用: -0.0288
  能量使用: 0.3000
  推理时间: 0.7598秒

批次 8:
  奖励值: 32.5288
  收益率: 0.8129
  距离: 10.2548
  内存使用: -0.0230
  能量使用: 0.3105
  推理时间: 0.8734秒

批次 9:
  奖励值: 29.9056
  收益率: 0.7570
  距离: 10.3545
  内存使用: -0.0575
  能量使用: 0.2920
  推理时间: 0.7586秒

批次 10:
  奖励值: 31.6664
  收益率: 0.7626
  距离: 8.6515
  内存使用: 0.2961
  能量使用: 0.3196
  推理时间: 0.7409秒

批次 11:
  奖励值: 32.4155
  收益率: 0.8322
  距离: 10.2953
  内存使用: -0.0443
  能量使用: 0.3181
  推理时间: 0.7490秒

批次 12:
  奖励值: 35.1296
  收益率: 0.8285
  距离: 8.6649
  内存使用: -0.0110
  能量使用: 0.2868
  推理时间: 0.8068秒

批次 13:
  奖励值: 33.1434
  收益率: 0.7934
  距离: 10.2425
  内存使用: -0.0000
  能量使用: 0.2707
  推理时间: 0.7596秒

批次 14:
  奖励值: 32.5852
  收益率: 0.8337
  距离: 9.6840
  内存使用: 0.0226
  能量使用: 0.3231
  推理时间: 0.8376秒

批次 15:
  奖励值: 32.9386
  收益率: 0.8071
  距离: 10.7823
  内存使用: 0.0205
  能量使用: 0.3201
  推理时间: 0.7932秒

批次 16:
  奖励值: 32.8745
  收益率: 0.7925
  距离: 10.3779
  内存使用: -0.0017
  能量使用: 0.2748
  推理时间: 0.8158秒

批次 17:
  奖励值: 31.0415
  收益率: 0.7725
  距离: 9.5853
  内存使用: -0.0056
  能量使用: 0.3373
  推理时间: 0.8169秒

批次 18:
  奖励值: 29.5439
  收益率: 0.7768
  距离: 10.1960
  内存使用: -0.0051
  能量使用: 0.3213
  推理时间: 0.8197秒

批次 19:
  奖励值: 33.7563
  收益率: 0.8261
  距离: 11.9204
  内存使用: 0.0076
  能量使用: 0.3595
  推理时间: 0.8629秒

批次 20:
  奖励值: 30.3661
  收益率: 0.7879
  距离: 9.6205
  内存使用: -0.0198
  能量使用: 0.2671
  推理时间: 0.9129秒

批次 21:
  奖励值: 32.3057
  收益率: 0.7957
  距离: 9.8650
  内存使用: 0.0005
  能量使用: 0.3230
  推理时间: 0.9489秒

批次 22:
  奖励值: 34.3402
  收益率: 0.8259
  距离: 8.7252
  内存使用: 0.0860
  能量使用: 0.3084
  推理时间: 0.8805秒

批次 23:
  奖励值: 31.4948
  收益率: 0.7607
  距离: 7.9103
  内存使用: 0.0068
  能量使用: 0.2933
  推理时间: 0.7928秒

批次 24:
  奖励值: 27.8956
  收益率: 0.7300
  距离: 7.7652
  内存使用: -0.0237
  能量使用: 0.2674
  推理时间: 0.8153秒

批次 25:
  奖励值: 34.1487
  收益率: 0.8186
  距离: 10.7941
  内存使用: -0.0157
  能量使用: 0.3243
  推理时间: 0.9404秒

批次 26:
  奖励值: 31.8976
  收益率: 0.8333
  距离: 12.8150
  内存使用: 0.0405
  能量使用: 0.3493
  推理时间: 1.0096秒

批次 27:
  奖励值: 32.7382
  收益率: 0.7910
  距离: 11.4149
  内存使用: 0.0164
  能量使用: 0.2923
  推理时间: 0.9637秒

批次 28:
  奖励值: 31.3715
  收益率: 0.7689
  距离: 8.1748
  内存使用: 0.0222
  能量使用: 0.2601
  推理时间: 0.9578秒

批次 29:
  奖励值: 32.2602
  收益率: 0.8085
  距离: 7.8560
  内存使用: -0.0475
  能量使用: 0.2712
  推理时间: 0.7136秒

批次 30:
  奖励值: 32.0046
  收益率: 0.7835
  距离: 8.1040
  内存使用: -0.0051
  能量使用: 0.2920
  推理时间: 0.7989秒

批次 31:
  奖励值: 34.1417
  收益率: 0.7988
  距离: 11.6191
  内存使用: 0.0018
  能量使用: 0.2958
  推理时间: 0.8913秒

批次 32:
  奖励值: 32.2063
  收益率: 0.7974
  距离: 10.0464
  内存使用: 0.0250
  能量使用: 0.3160
  推理时间: 0.8318秒

批次 33:
  奖励值: 31.4128
  收益率: 0.7821
  距离: 10.1250
  内存使用: -0.0365
  能量使用: 0.2858
  推理时间: 0.7376秒

批次 34:
  奖励值: 32.4967
  收益率: 0.8022
  距离: 9.3664
  内存使用: -0.0217
  能量使用: 0.2619
  推理时间: 0.9548秒

批次 35:
  奖励值: 30.3034
  收益率: 0.7352
  距离: 8.5474
  内存使用: -0.0244
  能量使用: 0.2702
  推理时间: 0.8401秒

批次 36:
  奖励值: 36.6221
  收益率: 0.8263
  距离: 9.2749
  内存使用: -0.0417
  能量使用: 0.3225
  推理时间: 0.9442秒

批次 37:
  奖励值: 25.6598
  收益率: 0.7101
  距离: 7.3254
  内存使用: -0.0979
  能量使用: 0.2530
  推理时间: 0.7311秒

批次 38:
  奖励值: 28.5576
  收益率: 0.7788
  距离: 10.3061
  内存使用: -0.0726
  能量使用: 0.2513
  推理时间: 0.8036秒

批次 39:
  奖励值: 31.7455
  收益率: 0.7735
  距离: 8.6762
  内存使用: -0.0422
  能量使用: 0.2750
  推理时间: 0.8109秒

批次 40:
  奖励值: 29.6493
  收益率: 0.7556
  距离: 8.4472
  内存使用: -0.0376
  能量使用: 0.2918
  推理时间: 0.8399秒

批次 41:
  奖励值: 28.9022
  收益率: 0.7489
  距离: 8.1819
  内存使用: -0.0483
  能量使用: 0.2620
  推理时间: 0.7976秒

批次 42:
  奖励值: 34.9504
  收益率: 0.8261
  距离: 9.6278
  内存使用: -0.0040
  能量使用: 0.2753
  推理时间: 0.9478秒

批次 43:
  奖励值: 32.5050
  收益率: 0.7965
  距离: 8.2810
  内存使用: 0.0239
  能量使用: 0.2851
  推理时间: 0.9305秒

批次 44:
  奖励值: 32.2241
  收益率: 0.7699
  距离: 8.9112
  内存使用: -0.0542
  能量使用: 0.2947
  推理时间: 0.9072秒

批次 45:
  奖励值: 31.7528
  收益率: 0.7965
  距离: 9.8483
  内存使用: -0.0118
  能量使用: 0.2579
  推理时间: 0.8460秒

批次 46:
  奖励值: 32.0809
  收益率: 0.7835
  距离: 8.0589
  内存使用: -0.0737
  能量使用: 0.2530
  推理时间: 0.8403秒

批次 47:
  奖励值: 30.1206
  收益率: 0.7605
  距离: 8.1115
  内存使用: -0.0394
  能量使用: 0.2867
  推理时间: 0.7518秒

批次 48:
  奖励值: 29.8769
  收益率: 0.7426
  距离: 9.5305
  内存使用: 0.2549
  能量使用: 0.2608
  推理时间: 0.9094秒

批次 49:
  奖励值: 34.6384
  收益率: 0.8110
  距离: 10.3006
  内存使用: -0.0371
  能量使用: 0.2597
  推理时间: 0.8978秒

批次 50:
  奖励值: 33.0770
  收益率: 0.7933
  距离: 9.5655
  内存使用: -0.0250
  能量使用: 0.3055
  推理时间: 0.8699秒

批次 51:
  奖励值: 31.8562
  收益率: 0.7891
  距离: 8.6273
  内存使用: -0.0166
  能量使用: 0.2768
  推理时间: 0.8472秒

批次 52:
  奖励值: 32.5466
  收益率: 0.8074
  距离: 9.2104
  内存使用: 0.0244
  能量使用: 0.2721
  推理时间: 0.8747秒

批次 53:
  奖励值: 34.1400
  收益率: 0.8288
  距离: 10.7705
  内存使用: 0.0441
  能量使用: 0.3054
  推理时间: 1.0066秒

批次 54:
  奖励值: 30.5581
  收益率: 0.7773
  距离: 9.8273
  内存使用: -0.0171
  能量使用: 0.2736
  推理时间: 0.7474秒

批次 55:
  奖励值: 35.0828
  收益率: 0.8418
  距离: 9.2907
  内存使用: 0.0259
  能量使用: 0.3179
  推理时间: 0.8767秒

批次 56:
  奖励值: 30.8242
  收益率: 0.7827
  距离: 8.7025
  内存使用: -0.0139
  能量使用: 0.2629
  推理时间: 0.8419秒

批次 57:
  奖励值: 29.6592
  收益率: 0.7738
  距离: 10.1699
  内存使用: -0.0014
  能量使用: 0.3131
  推理时间: 0.8605秒

批次 58:
  奖励值: 33.0004
  收益率: 0.8441
  距离: 9.4743
  内存使用: -0.0151
  能量使用: 0.3399
  推理时间: 0.8046秒

批次 59:
  奖励值: 35.7199
  收益率: 0.8103
  距离: 10.2640
  内存使用: 0.0011
  能量使用: 0.2951
  推理时间: 0.8807秒

批次 60:
  奖励值: 31.3592
  收益率: 0.7819
  距离: 9.3929
  内存使用: 0.0105
  能量使用: 0.2781
  推理时间: 0.8531秒

批次 61:
  奖励值: 30.5071
  收益率: 0.7860
  距离: 10.6910
  内存使用: -0.0066
  能量使用: 0.3013
  推理时间: 0.7933秒

批次 62:
  奖励值: 28.1690
  收益率: 0.7366
  距离: 6.6963
  内存使用: -0.0749
  能量使用: 0.2405
  推理时间: 0.7237秒

批次 63:
  奖励值: 30.5167
  收益率: 0.8190
  距离: 11.0563
  内存使用: 0.0089
  能量使用: 0.3015
  推理时间: 0.8768秒

批次 64:
  奖励值: 28.4864
  收益率: 0.7728
  距离: 8.7606
  内存使用: -0.0181
  能量使用: 0.2848
  推理时间: 0.7758秒

批次 65:
  奖励值: 34.4272
  收益率: 0.7996
  距离: 9.1325
  内存使用: -0.0386
  能量使用: 0.2516
  推理时间: 0.9476秒

批次 66:
  奖励值: 32.8634
  收益率: 0.8312
  距离: 12.7205
  内存使用: 0.0389
  能量使用: 0.3139
  推理时间: 1.0170秒

批次 67:
  奖励值: 32.3832
  收益率: 0.7871
  距离: 8.1178
  内存使用: 0.0173
  能量使用: 0.2984
  推理时间: 0.8500秒

批次 68:
  奖励值: 29.7840
  收益率: 0.7500
  距离: 7.4040
  内存使用: -0.0413
  能量使用: 0.2694
  推理时间: 0.6400秒

批次 69:
  奖励值: 30.0095
  收益率: 0.7654
  距离: 9.5445
  内存使用: -0.0322
  能量使用: 0.2686
  推理时间: 0.8018秒

批次 70:
  奖励值: 30.4586
  收益率: 0.7263
  距离: 9.4939
  内存使用: -0.0532
  能量使用: 0.2422
  推理时间: 0.8587秒

批次 71:
  奖励值: 31.0503
  收益率: 0.7704
  距离: 9.4639
  内存使用: -0.0494
  能量使用: 0.2849
  推理时间: 0.7592秒

批次 72:
  奖励值: 31.4202
  收益率: 0.7974
  距离: 9.2997
  内存使用: -0.0319
  能量使用: 0.2917
  推理时间: 0.8886秒

批次 73:
  奖励值: 35.7447
  收益率: 0.8290
  距离: 11.5373
  内存使用: 0.0231
  能量使用: 0.3503
  推理时间: 0.9984秒

批次 74:
  奖励值: 33.5163
  收益率: 0.7828
  距离: 9.1024
  内存使用: -0.0236
  能量使用: 0.2886
  推理时间: 0.9140秒

批次 75:
  奖励值: 31.9589
  收益率: 0.8009
  距离: 8.9878
  内存使用: -0.0053
  能量使用: 0.2997
  推理时间: 0.8999秒

批次 76:
  奖励值: 30.0549
  收益率: 0.7991
  距离: 8.8164
  内存使用: -0.0241
  能量使用: 0.2978
  推理时间: 0.8589秒

批次 77:
  奖励值: 34.6626
  收益率: 0.8037
  距离: 8.9913
  内存使用: 0.0016
  能量使用: 0.2819
  推理时间: 0.8810秒

批次 78:
  奖励值: 30.2792
  收益率: 0.7709
  距离: 9.2301
  内存使用: -0.0607
  能量使用: 0.2722
  推理时间: 1.0058秒

批次 79:
  奖励值: 28.4035
  收益率: 0.7728
  距离: 8.9873
  内存使用: -0.0459
  能量使用: 0.2517
  推理时间: 0.8072秒

批次 80:
  奖励值: 31.6505
  收益率: 0.8067
  距离: 9.0021
  内存使用: 0.0014
  能量使用: 0.2955
  推理时间: 0.8876秒

批次 81:
  奖励值: 24.5068
  收益率: 0.7282
  距离: 9.2204
  内存使用: -0.0748
  能量使用: 0.2409
  推理时间: 0.7976秒

批次 82:
  奖励值: 35.9918
  收益率: 0.8198
  距离: 8.6300
  内存使用: 0.0491
  能量使用: 0.3374
  推理时间: 1.0183秒

批次 83:
  奖励值: 35.7717
  收益率: 0.8182
  距离: 9.0913
  内存使用: 0.0201
  能量使用: 0.3452
  推理时间: 0.9422秒

批次 84:
  奖励值: 32.4852
  收益率: 0.7845
  距离: 9.7528
  内存使用: 0.0153
  能量使用: 0.2615
  推理时间: 0.9167秒

批次 85:
  奖励值: 32.4489
  收益率: 0.7615
  距离: 7.6433
  内存使用: -0.0328
  能量使用: 0.2916
  推理时间: 0.8534秒

批次 86:
  奖励值: 27.9067
  收益率: 0.7212
  距离: 9.1559
  内存使用: -0.0302
  能量使用: 0.2609
  推理时间: 0.8687秒

批次 87:
  奖励值: 30.5721
  收益率: 0.7738
  距离: 8.3859
  内存使用: -0.0385
  能量使用: 0.3084
  推理时间: 0.8349秒

批次 88:
  奖励值: 34.1989
  收益率: 0.8054
  距离: 8.3635
  内存使用: -0.0028
  能量使用: 0.2416
  推理时间: 0.8997秒

批次 89:
  奖励值: 28.5210
  收益率: 0.7433
  距离: 9.3543
  内存使用: -0.0271
  能量使用: 0.2309
  推理时间: 0.8555秒

批次 90:
  奖励值: 28.5196
  收益率: 0.7546
  距离: 8.5014
  内存使用: -0.0267
  能量使用: 0.2861
  推理时间: 0.8008秒

批次 91:
  奖励值: 32.7277
  收益率: 0.8139
  距离: 9.4311
  内存使用: -0.0034
  能量使用: 0.3169
  推理时间: 0.9308秒

批次 92:
  奖励值: 34.1930
  收益率: 0.7906
  距离: 8.3397
  内存使用: -0.0019
  能量使用: 0.2763
  推理时间: 0.8414秒

批次 93:
  奖励值: 35.8817
  收益率: 0.8124
  距离: 9.2766
  内存使用: 0.0455
  能量使用: 0.3145
  推理时间: 0.7926秒

批次 94:
  奖励值: 30.8613
  收益率: 0.8004
  距离: 9.3471
  内存使用: 0.0144
  能量使用: 0.3159
  推理时间: 0.7558秒

批次 95:
  奖励值: 33.6683
  收益率: 0.8078
  距离: 11.3944
  内存使用: 0.0005
  能量使用: 0.2685
  推理时间: 0.8797秒

批次 96:
  奖励值: 31.6709
  收益率: 0.7780
  距离: 8.6399
  内存使用: -0.0301
  能量使用: 0.2485
  推理时间: 0.8013秒

批次 97:
  奖励值: 29.1764
  收益率: 0.7418
  距离: 9.1859
  内存使用: -0.0163
  能量使用: 0.2776
  推理时间: 0.7833秒

批次 98:
  奖励值: 34.5047
  收益率: 0.7960
  距离: 10.2528
  内存使用: 0.0049
  能量使用: 0.3329
  推理时间: 0.8587秒

批次 99:
  奖励值: 28.4997
  收益率: 0.7812
  距离: 9.1592
  内存使用: -0.0586
  能量使用: 0.3001
  推理时间: 0.7906秒

批次 100:
  奖励值: 32.4479
  收益率: 0.7683
  距离: 10.5178
  内存使用: -0.0645
  能量使用: 0.2509
  推理时间: 0.7898秒


==================== 总结 ====================
平均收益率: 0.7870
平均能量使用: 0.2885
平均推理时间: 0.8474秒
