推理数据数量: 100
每个序列任务数量: 1250
星座卫星数量: 3
星座模式: competitive
模型: gpn+indrnn
种子: 12348
内存总量: 0.3
电量总量: 5
检查点路径: constellation_smp/constellation_smp100/multi_constellation_comparison_2025_08_25_17_02_29/constellation_gpnindrnn_competitive_2025_08_26_05_01_59

批次 1:
  奖励值: 136.1195
  收益率: 0.2840
  距离: 39.2890
  内存使用: 0.6831
  能量使用: 1.1888
  推理时间: 2.9131秒

批次 2:
  奖励值: 132.1516
  收益率: 0.2639
  距离: 34.2766
  内存使用: 0.7061
  能量使用: 1.1140
  推理时间: 2.8090秒

批次 3:
  奖励值: 125.5279
  收益率: 0.2492
  距离: 29.8193
  内存使用: 0.6698
  能量使用: 1.0550
  推理时间: 2.6500秒

批次 4:
  奖励值: 124.1221
  收益率: 0.2473
  距离: 29.9096
  内存使用: 0.6362
  能量使用: 1.0101
  推理时间: 2.6073秒

批次 5:
  奖励值: 139.6022
  收益率: 0.2742
  距离: 32.9195
  内存使用: 0.8078
  能量使用: 1.0682
  推理时间: 2.8325秒

批次 6:
  奖励值: 148.2972
  收益率: 0.2954
  距离: 39.2186
  内存使用: 0.8237
  能量使用: 1.1634
  推理时间: 3.1168秒

批次 7:
  奖励值: 121.5054
  收益率: 0.2427
  距离: 29.1877
  内存使用: 0.6792
  能量使用: 0.9222
  推理时间: 2.4959秒

批次 8:
  奖励值: 138.7852
  收益率: 0.2790
  距离: 36.0517
  内存使用: 0.7446
  能量使用: 1.0333
  推理时间: 2.9448秒

批次 9:
  奖励值: 155.2409
  收益率: 0.3107
  距离: 39.4746
  内存使用: 0.8683
  能量使用: 1.1751
  推理时间: 3.1338秒

批次 10:
  奖励值: 131.2772
  收益率: 0.2601
  距离: 30.1104
  内存使用: 0.6949
  能量使用: 1.0403
  推理时间: 2.5572秒

批次 11:
  奖励值: 125.4295
  收益率: 0.2517
  距离: 30.0588
  内存使用: 0.6957
  能量使用: 0.9864
  推理时间: 2.7561秒

批次 12:
  奖励值: 133.0729
  收益率: 0.2650
  距离: 38.9110
  内存使用: 0.7272
  能量使用: 1.0159
  推理时间: 2.8231秒

批次 13:
  奖励值: 133.4317
  收益率: 0.2679
  距离: 31.0200
  内存使用: 0.6863
  能量使用: 1.0303
  推理时间: 2.7392秒

批次 14:
  奖励值: 134.2510
  收益率: 0.2690
  距离: 37.1244
  内存使用: 0.7099
  能量使用: 1.0637
  推理时间: 2.8703秒

批次 15:
  奖励值: 137.0934
  收益率: 0.2745
  距离: 34.1228
  内存使用: 0.7972
  能量使用: 1.0931
  推理时间: 2.9697秒

批次 16:
  奖励值: 128.1032
  收益率: 0.2566
  距离: 29.7242
  内存使用: 0.6628
  能量使用: 1.0065
  推理时间: 2.6717秒

批次 17:
  奖励值: 141.5293
  收益率: 0.2868
  距离: 34.5764
  内存使用: 0.7003
  能量使用: 1.0648
  推理时间: 2.9646秒

批次 18:
  奖励值: 121.7539
  收益率: 0.2423
  距离: 31.9494
  内存使用: 0.7141
  能量使用: 1.0287
  推理时间: 2.6795秒

批次 19:
  奖励值: 115.4687
  收益率: 0.2224
  距离: 27.5059
  内存使用: 0.8999
  能量使用: 0.8568
  推理时间: 2.5100秒

批次 20:
  奖励值: 135.8495
  收益率: 0.2688
  距离: 33.5500
  内存使用: 0.6272
  能量使用: 1.1238
  推理时间: 2.9211秒

批次 21:
  奖励值: 117.8969
  收益率: 0.2368
  距离: 29.3942
  内存使用: 0.6462
  能量使用: 0.9658
  推理时间: 2.4611秒

批次 22:
  奖励值: 133.4772
  收益率: 0.2611
  距离: 32.3560
  内存使用: 0.6839
  能量使用: 1.0056
  推理时间: 2.8316秒

批次 23:
  奖励值: 136.8761
  收益率: 0.2756
  距离: 34.1833
  内存使用: 0.7804
  能量使用: 1.0842
  推理时间: 2.9115秒

批次 24:
  奖励值: 128.7231
  收益率: 0.2577
  距离: 33.8129
  内存使用: 0.6381
  能量使用: 1.1027
  推理时间: 2.7718秒

批次 25:
  奖励值: 117.2116
  收益率: 0.2362
  距离: 30.0448
  内存使用: 0.6718
  能量使用: 0.8602
  推理时间: 2.4949秒

批次 26:
  奖励值: 123.8877
  收益率: 0.2496
  距离: 31.5020
  内存使用: 0.7090
  能量使用: 1.0137
  推理时间: 2.6796秒

批次 27:
  奖励值: 140.4478
  收益率: 0.2851
  距离: 38.3459
  内存使用: 0.8100
  能量使用: 1.1484
  推理时间: 3.0233秒

批次 28:
  奖励值: 125.1910
  收益率: 0.2429
  距离: 29.8991
  内存使用: 0.6035
  能量使用: 0.9153
  推理时间: 2.6438秒

批次 29:
  奖励值: 142.2005
  收益率: 0.2859
  距离: 37.7415
  内存使用: 0.7579
  能量使用: 1.0997
  推理时间: 2.9951秒

批次 30:
  奖励值: 134.7177
  收益率: 0.2656
  距离: 30.7629
  内存使用: 0.7291
  能量使用: 1.0726
  推理时间: 2.7868秒

批次 31:
  奖励值: 128.3095
  收益率: 0.2612
  距离: 31.2281
  内存使用: 0.5893
  能量使用: 0.9637
  推理时间: 2.6799秒

批次 32:
  奖励值: 140.1626
  收益率: 0.2792
  距离: 34.9799
  内存使用: 0.7897
  能量使用: 1.1052
  推理时间: 2.9038秒

批次 33:
  奖励值: 126.8694
  收益率: 0.2581
  距离: 38.4749
  内存使用: 0.6833
  能量使用: 1.1030
  推理时间: 2.8067秒

批次 34:
  奖励值: 125.6055
  收益率: 0.2541
  距离: 31.4837
  内存使用: 0.6812
  能量使用: 1.0242
  推理时间: 2.6877秒

批次 35:
  奖励值: 137.4097
  收益率: 0.2762
  距离: 35.9108
  内存使用: 0.7493
  能量使用: 1.1205
  推理时间: 2.9978秒

批次 36:
  奖励值: 136.4775
  收益率: 0.2788
  距离: 34.7665
  内存使用: 0.7458
  能量使用: 1.1327
  推理时间: 2.9384秒

批次 37:
  奖励值: 135.7831
  收益率: 0.2697
  距离: 31.2735
  内存使用: 0.8367
  能量使用: 1.1237
  推理时间: 2.8057秒

批次 38:
  奖励值: 128.9017
  收益率: 0.2559
  距离: 34.4798
  内存使用: 0.6875
  能量使用: 1.0291
  推理时间: 2.7480秒

批次 39:
  奖励值: 137.1337
  收益率: 0.2748
  距离: 34.9637
  内存使用: 0.7266
  能量使用: 1.0422
  推理时间: 2.9057秒

批次 40:
  奖励值: 118.8496
  收益率: 0.2380
  距离: 28.7648
  内存使用: 0.6183
  能量使用: 0.9176
  推理时间: 2.6063秒

批次 41:
  奖励值: 125.9079
  收益率: 0.2544
  距离: 33.1051
  内存使用: 0.6889
  能量使用: 0.9903
  推理时间: 2.7410秒

批次 42:
  奖励值: 123.5788
  收益率: 0.2449
  距离: 31.9537
  内存使用: 0.6437
  能量使用: 1.0450
  推理时间: 2.5822秒

批次 43:
  奖励值: 136.4017
  收益率: 0.2729
  距离: 32.2404
  内存使用: 0.7597
  能量使用: 0.9965
  推理时间: 2.8929秒

批次 44:
  奖励值: 142.9268
  收益率: 0.2828
  距离: 31.9830
  内存使用: 0.7158
  能量使用: 1.0475
  推理时间: 2.9300秒

批次 45:
  奖励值: 119.0598
  收益率: 0.2421
  距离: 29.9484
  内存使用: 0.8994
  能量使用: 1.0327
  推理时间: 2.5919秒

批次 46:
  奖励值: 123.4149
  收益率: 0.2471
  距离: 28.7568
  内存使用: 0.6135
  能量使用: 0.9998
  推理时间: 2.5633秒

批次 47:
  奖励值: 131.4529
  收益率: 0.2591
  距离: 31.6989
  内存使用: 0.7410
  能量使用: 1.0543
  推理时间: 2.8417秒

批次 48:
  奖励值: 120.2392
  收益率: 0.2433
  距离: 29.1923
  内存使用: 0.6569
  能量使用: 0.8724
  推理时间: 2.5012秒

批次 49:
  奖励值: 134.9966
  收益率: 0.2732
  距离: 35.4079
  内存使用: 0.6824
  能量使用: 1.1166
  推理时间: 2.8590秒

批次 50:
  奖励值: 130.6609
  收益率: 0.2690
  距离: 36.2148
  内存使用: 0.6407
  能量使用: 1.0227
  推理时间: 2.8270秒

批次 51:
  奖励值: 137.5911
  收益率: 0.2807
  距离: 39.3852
  内存使用: 0.7260
  能量使用: 1.1067
  推理时间: 2.9686秒

批次 52:
  奖励值: 121.6832
  收益率: 0.2453
  距离: 29.8411
  内存使用: 0.6465
  能量使用: 0.9460
  推理时间: 2.5284秒

批次 53:
  奖励值: 133.3215
  收益率: 0.2682
  距离: 35.7317
  内存使用: 0.7838
  能量使用: 1.0414
  推理时间: 2.8290秒

批次 54:
  奖励值: 119.4861
  收益率: 0.2495
  距离: 33.1145
  内存使用: 0.6050
  能量使用: 0.9082
  推理时间: 2.5375秒

批次 55:
  奖励值: 135.9160
  收益率: 0.2701
  距离: 32.4268
  内存使用: 0.7335
  能量使用: 1.0078
  推理时间: 2.8067秒

批次 56:
  奖励值: 122.2355
  收益率: 0.2464
  距离: 31.7093
  内存使用: 0.6754
  能量使用: 0.9443
  推理时间: 2.6062秒

批次 57:
  奖励值: 120.4187
  收益率: 0.2416
  距离: 33.5552
  内存使用: 0.6317
  能量使用: 0.9758
  推理时间: 2.6586秒

批次 58:
  奖励值: 138.2515
  收益率: 0.2819
  距离: 34.8847
  内存使用: 0.7005
  能量使用: 1.1118
  推理时间: 3.1003秒

批次 59:
  奖励值: 129.9742
  收益率: 0.2521
  距离: 30.9265
  内存使用: 0.6460
  能量使用: 1.0791
  推理时间: 2.6830秒

批次 60:
  奖励值: 119.9054
  收益率: 0.2495
  距离: 32.7741
  内存使用: 0.6767
  能量使用: 0.9383
  推理时间: 2.4566秒

批次 61:
  奖励值: 121.2061
  收益率: 0.2359
  距离: 30.1530
  内存使用: 0.8998
  能量使用: 0.9349
  推理时间: 2.5585秒

批次 62:
  奖励值: 139.8689
  收益率: 0.2753
  距离: 29.9266
  内存使用: 0.8453
  能量使用: 1.0904
  推理时间: 2.8203秒

批次 63:
  奖励值: 145.7478
  收益率: 0.2972
  距离: 41.5070
  内存使用: 0.8730
  能量使用: 1.1243
  推理时间: 3.0230秒

批次 64:
  奖励值: 129.8779
  收益率: 0.2562
  距离: 32.6045
  内存使用: 0.6561
  能量使用: 0.9835
  推理时间: 2.6684秒

批次 65:
  奖励值: 120.8275
  收益率: 0.2367
  距离: 26.6478
  内存使用: 0.5919
  能量使用: 0.9052
  推理时间: 2.4283秒

批次 66:
  奖励值: 131.3621
  收益率: 0.2673
  距离: 31.1165
  内存使用: 0.7015
  能量使用: 1.0578
  推理时间: 2.6761秒

批次 67:
  奖励值: 119.7156
  收益率: 0.2406
  距离: 32.6992
  内存使用: 0.8998
  能量使用: 0.9697
  推理时间: 2.6290秒

批次 68:
  奖励值: 130.1842
  收益率: 0.2679
  距离: 32.0810
  内存使用: 0.7305
  能量使用: 1.0200
  推理时间: 2.8739秒

批次 69:
  奖励值: 141.9450
  收益率: 0.2861
  距离: 35.2943
  内存使用: 0.7333
  能量使用: 1.0823
  推理时间: 2.8845秒

批次 70:
  奖励值: 117.1009
  收益率: 0.2278
  距离: 28.5718
  内存使用: 0.8998
  能量使用: 0.9265
  推理时间: 2.6105秒

批次 71:
  奖励值: 146.7628
  收益率: 0.3005
  距离: 40.5712
  内存使用: 0.7487
  能量使用: 1.1544
  推理时间: 3.2672秒

批次 72:
  奖励值: 129.1328
  收益率: 0.2597
  距离: 33.4739
  内存使用: 0.6318
  能量使用: 1.0287
  推理时间: 2.7602秒

批次 73:
  奖励值: 124.6533
  收益率: 0.2467
  距离: 30.8311
  内存使用: 0.8995
  能量使用: 0.9629
  推理时间: 2.7660秒

批次 74:
  奖励值: 133.5128
  收益率: 0.2692
  距离: 32.8344
  内存使用: 0.7207
  能量使用: 1.0191
  推理时间: 2.8312秒

批次 75:
  奖励值: 125.9310
  收益率: 0.2504
  距离: 32.2742
  内存使用: 0.6775
  能量使用: 0.9863
  推理时间: 2.7360秒

批次 76:
  奖励值: 137.3838
  收益率: 0.2738
  距离: 31.1666
  内存使用: 0.7816
  能量使用: 1.0841
  推理时间: 2.9894秒

批次 77:
  奖励值: 108.7286
  收益率: 0.2262
  距离: 30.6780
  内存使用: 0.5535
  能量使用: 0.9112
  推理时间: 2.2479秒

批次 78:
  奖励值: 128.3180
  收益率: 0.2565
  距离: 30.6268
  内存使用: 0.6877
  能量使用: 1.0496
  推理时间: 2.7327秒

批次 79:
  奖励值: 121.5971
  收益率: 0.2470
  距离: 37.4141
  内存使用: 0.6124
  能量使用: 0.9792
  推理时间: 2.8900秒

批次 80:
  奖励值: 118.8002
  收益率: 0.2416
  距离: 32.3622
  内存使用: 0.6552
  能量使用: 0.9822
  推理时间: 2.6586秒

批次 81:
  奖励值: 134.6479
  收益率: 0.2743
  距离: 34.2643
  内存使用: 0.7420
  能量使用: 1.0979
  推理时间: 2.7766秒

批次 82:
  奖励值: 137.0887
  收益率: 0.2700
  距离: 36.0255
  内存使用: 0.7041
  能量使用: 1.0930
  推理时间: 3.0849秒

批次 83:
  奖励值: 126.7945
  收益率: 0.2534
  距离: 31.4164
  内存使用: 0.6495
  能量使用: 0.9449
  推理时间: 2.6904秒

批次 84:
  奖励值: 140.2809
  收益率: 0.2917
  距离: 41.1047
  内存使用: 0.7881
  能量使用: 1.1454
  推理时间: 2.9694秒

批次 85:
  奖励值: 134.3663
  收益率: 0.2714
  距离: 31.9892
  内存使用: 0.6679
  能量使用: 1.0102
  推理时间: 2.8272秒

批次 86:
  奖励值: 142.5499
  收益率: 0.2848
  距离: 35.4559
  内存使用: 0.7754
  能量使用: 1.0689
  推理时间: 2.9613秒

批次 87:
  奖励值: 124.1582
  收益率: 0.2513
  距离: 30.0169
  内存使用: 0.6481
  能量使用: 1.0185
  推理时间: 2.6166秒

批次 88:
  奖励值: 126.6397
  收益率: 0.2611
  距离: 31.4111
  内存使用: 0.6888
  能量使用: 1.0208
  推理时间: 2.5908秒

批次 89:
  奖励值: 130.5135
  收益率: 0.2599
  距离: 32.9461
  内存使用: 0.6209
  能量使用: 1.0059
  推理时间: 2.5721秒

批次 90:
  奖励值: 138.8610
  收益率: 0.2782
  距离: 35.3312
  内存使用: 0.7633
  能量使用: 1.1835
  推理时间: 2.9340秒

批次 91:
  奖励值: 131.7435
  收益率: 0.2600
  距离: 34.3830
  内存使用: 0.7008
  能量使用: 1.0291
  推理时间: 2.5880秒

批次 92:
  奖励值: 124.1043
  收益率: 0.2503
  距离: 29.9846
  内存使用: 0.6395
  能量使用: 0.9672
  推理时间: 2.5332秒

批次 93:
  奖励值: 129.5524
  收益率: 0.2608
  距离: 34.1290
  内存使用: 0.7309
  能量使用: 1.0353
  推理时间: 2.7931秒

批次 94:
  奖励值: 139.4114
  收益率: 0.2814
  距离: 36.5760
  内存使用: 0.8001
  能量使用: 1.0011
  推理时间: 2.7882秒

批次 95:
  奖励值: 125.1247
  收益率: 0.2440
  距离: 32.9925
  内存使用: 0.6090
  能量使用: 0.9490
  推理时间: 2.8335秒

批次 96:
  奖励值: 132.3473
  收益率: 0.2585
  距离: 30.5801
  内存使用: 0.7345
  能量使用: 0.9909
  推理时间: 2.6240秒

批次 97:
  奖励值: 139.9134
  收益率: 0.2825
  距离: 33.1478
  内存使用: 0.7474
  能量使用: 1.0780
  推理时间: 2.8846秒

批次 98:
  奖励值: 117.8991
  收益率: 0.2301
  距离: 27.0819
  内存使用: 0.6209
  能量使用: 0.8990
  推理时间: 2.4095秒

批次 99:
  奖励值: 137.9217
  收益率: 0.2745
  距离: 33.1976
  内存使用: 0.7577
  能量使用: 1.0013
  推理时间: 2.8316秒

批次 100:
  奖励值: 144.2544
  收益率: 0.2908
  距离: 39.3549
  内存使用: 0.7577
  能量使用: 1.1787
  推理时间: 3.1020秒


==================== 总结 ====================
平均收益率: 0.2622
平均能量使用: 1.0308
平均推理时间: 2.7661秒
