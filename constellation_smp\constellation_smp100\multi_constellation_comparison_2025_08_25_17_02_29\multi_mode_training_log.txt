多星座模式训练实验
================================================================================
实验时间: 2025_08_25_17_02_29
设备: cuda
问题规模: 100节点, 3卫星
训练配置: 3轮, 批次大小64
使用Transformer: False

================================================================================
开始训练星座模式: COOPERATIVE
================================================================================
cooperative 模式模型信息:
  Actor参数数量: 2,211,337
  Critic参数数量: 494,285
  总参数数量: 2,705,622
详细训练配置:
  数据集大小: 训练100000, 验证10000
  学习率: Actor=0.0001, Critic=0.0002
  批次大小: 64
  训练轮数: 3
  梯度裁剪: 1
  权重衰减: 0.0001
  随机种子: 12346
  内存总量: 0.3
  功率总量: 5
开始训练 cooperative 模式...
训练开始时间: 2025-08-25 17:09:00
详细训练过程:
[COOPERATIVE] 开始训练 Epoch 1/3
[COOPERATIVE] Epoch 1, Batch 10/1563, loss: 1972.992, reward: 16.806, critic_reward: 19.416, revenue_rate: 0.4280, distance: 6.5099, memory: -0.0637, power: 0.1971, lr: 0.000100, took: 57.764s
[COOPERATIVE] Epoch 1, Batch 20/1563, loss: 70.576, reward: 19.932, critic_reward: 16.420, revenue_rate: 0.5043, distance: 7.3465, memory: -0.1085, power: 0.2215, lr: 0.000100, took: 59.749s
[COOPERATIVE] Epoch 1, Batch 30/1563, loss: 41.629, reward: 22.601, critic_reward: 21.720, revenue_rate: 0.5678, distance: 8.0886, memory: -0.0874, power: 0.2421, lr: 0.000100, took: 64.287s
[COOPERATIVE] Epoch 1, Batch 40/1563, loss: 10.496, reward: 26.636, critic_reward: 26.675, revenue_rate: 0.6649, distance: 9.0963, memory: -0.0588, power: 0.2750, lr: 0.000100, took: 73.863s
[COOPERATIVE] Epoch 1, Batch 50/1563, loss: 25.554, reward: 26.205, critic_reward: 23.790, revenue_rate: 0.6497, distance: 8.5192, memory: -0.0755, power: 0.2572, lr: 0.000100, took: 69.495s
[COOPERATIVE] Epoch 1, Batch 60/1563, loss: 11.069, reward: 27.256, critic_reward: 26.778, revenue_rate: 0.6776, distance: 8.8153, memory: -0.0658, power: 0.2640, lr: 0.000100, took: 72.301s
[COOPERATIVE] Epoch 1, Batch 70/1563, loss: 24.071, reward: 26.664, critic_reward: 25.205, revenue_rate: 0.6565, distance: 8.3616, memory: -0.0789, power: 0.2511, lr: 0.000100, took: 65.526s
[COOPERATIVE] Epoch 1, Batch 80/1563, loss: 8.130, reward: 26.334, critic_reward: 26.180, revenue_rate: 0.6474, distance: 8.1835, memory: -0.0757, power: 0.2455, lr: 0.000100, took: 66.917s
[COOPERATIVE] Epoch 1, Batch 90/1563, loss: 12.360, reward: 28.760, critic_reward: 30.177, revenue_rate: 0.7118, distance: 9.1112, memory: -0.0561, power: 0.2757, lr: 0.000100, took: 77.034s
[COOPERATIVE] Epoch 1, Batch 100/1563, loss: 26.967, reward: 30.570, critic_reward: 29.595, revenue_rate: 0.7537, distance: 9.7057, memory: -0.0350, power: 0.2950, lr: 0.000100, took: 78.619s
[COOPERATIVE] Epoch 1, Batch 110/1563, loss: 11.057, reward: 28.025, critic_reward: 29.104, revenue_rate: 0.6880, distance: 8.4989, memory: -0.0740, power: 0.2585, lr: 0.000100, took: 70.464s
[COOPERATIVE] Epoch 1, Batch 120/1563, loss: 6.743, reward: 28.948, critic_reward: 28.779, revenue_rate: 0.7098, distance: 8.9350, memory: -0.0552, power: 0.2700, lr: 0.000100, took: 74.385s
[COOPERATIVE] Epoch 1, Batch 130/1563, loss: 44.554, reward: 27.717, critic_reward: 28.422, revenue_rate: 0.6808, distance: 8.3950, memory: -0.0746, power: 0.2540, lr: 0.000100, took: 67.598s
[COOPERATIVE] Epoch 1, Batch 140/1563, loss: 33.456, reward: 28.692, critic_reward: 30.478, revenue_rate: 0.7061, distance: 8.8967, memory: -0.0593, power: 0.2698, lr: 0.000100, took: 72.190s
[COOPERATIVE] Epoch 1, Batch 150/1563, loss: 21.815, reward: 28.200, critic_reward: 29.112, revenue_rate: 0.6933, distance: 8.6740, memory: -0.0721, power: 0.2619, lr: 0.000100, took: 70.416s
[COOPERATIVE] Epoch 1, Batch 160/1563, loss: 25.750, reward: 28.315, critic_reward: 27.108, revenue_rate: 0.6988, distance: 8.9649, memory: -0.0553, power: 0.2710, lr: 0.000100, took: 72.700s
[COOPERATIVE] Epoch 1, Batch 170/1563, loss: 69.595, reward: 28.826, critic_reward: 25.752, revenue_rate: 0.7088, distance: 8.7438, memory: -0.0690, power: 0.2650, lr: 0.000100, took: 71.379s
[COOPERATIVE] Epoch 1, Batch 180/1563, loss: 11.369, reward: 30.419, critic_reward: 30.909, revenue_rate: 0.7508, distance: 9.4644, memory: -0.0496, power: 0.2865, lr: 0.000100, took: 76.418s
[COOPERATIVE] Epoch 1, Batch 190/1563, loss: 6.703, reward: 28.531, critic_reward: 28.999, revenue_rate: 0.7046, distance: 8.7386, memory: -0.0636, power: 0.2669, lr: 0.000100, took: 71.763s
[COOPERATIVE] Epoch 1, Batch 200/1563, loss: 5.896, reward: 21.865, critic_reward: 21.972, revenue_rate: 0.5363, distance: 6.4685, memory: -0.1327, power: 0.1965, lr: 0.000100, took: 52.655s
[COOPERATIVE] Epoch 1, Batch 210/1563, loss: 6.932, reward: 27.536, critic_reward: 28.200, revenue_rate: 0.6737, distance: 8.1981, memory: -0.0799, power: 0.2480, lr: 0.000100, took: 69.763s
[COOPERATIVE] Epoch 1, Batch 220/1563, loss: 11.170, reward: 30.439, critic_reward: 29.487, revenue_rate: 0.7531, distance: 9.6713, memory: -0.0310, power: 0.2918, lr: 0.000100, took: 80.179s
[COOPERATIVE] Epoch 1, Batch 230/1563, loss: 20.925, reward: 29.384, critic_reward: 31.928, revenue_rate: 0.7250, distance: 9.0501, memory: -0.0439, power: 0.2750, lr: 0.000100, took: 73.069s
[COOPERATIVE] Epoch 1, Batch 240/1563, loss: 5.404, reward: 29.755, critic_reward: 29.757, revenue_rate: 0.7309, distance: 9.0921, memory: -0.0567, power: 0.2748, lr: 0.000100, took: 74.442s
[COOPERATIVE] Epoch 1, Batch 250/1563, loss: 9.364, reward: 30.214, critic_reward: 29.435, revenue_rate: 0.7469, distance: 9.2654, memory: -0.0402, power: 0.2829, lr: 0.000100, took: 76.536s
[COOPERATIVE] Epoch 1, Batch 260/1563, loss: 9.658, reward: 25.237, critic_reward: 24.337, revenue_rate: 0.6174, distance: 7.5732, memory: -0.0909, power: 0.2305, lr: 0.000100, took: 62.709s
[COOPERATIVE] Epoch 1, Batch 270/1563, loss: 19.160, reward: 28.521, critic_reward: 29.055, revenue_rate: 0.6991, distance: 8.4744, memory: -0.0736, power: 0.2577, lr: 0.000100, took: 68.680s
[COOPERATIVE] Epoch 1, Batch 280/1563, loss: 9.164, reward: 31.955, critic_reward: 32.516, revenue_rate: 0.7873, distance: 10.1217, memory: -0.0166, power: 0.3070, lr: 0.000100, took: 82.782s
[COOPERATIVE] Epoch 1, Batch 290/1563, loss: 11.853, reward: 31.310, critic_reward: 30.550, revenue_rate: 0.7691, distance: 9.7140, memory: -0.0320, power: 0.2907, lr: 0.000100, took: 80.575s
[COOPERATIVE] Epoch 1, Batch 300/1563, loss: 5.288, reward: 29.841, critic_reward: 29.416, revenue_rate: 0.7360, distance: 9.0688, memory: -0.0556, power: 0.2738, lr: 0.000100, took: 72.751s
[COOPERATIVE] Epoch 1, Batch 310/1563, loss: 20.177, reward: 31.245, critic_reward: 32.220, revenue_rate: 0.7672, distance: 9.5148, memory: -0.0364, power: 0.2870, lr: 0.000100, took: 77.438s
[COOPERATIVE] Epoch 1, Batch 320/1563, loss: 19.722, reward: 29.591, critic_reward: 30.021, revenue_rate: 0.7263, distance: 8.9051, memory: -0.0617, power: 0.2699, lr: 0.000100, took: 71.905s
[COOPERATIVE] Epoch 1, Batch 330/1563, loss: 6.705, reward: 31.631, critic_reward: 30.798, revenue_rate: 0.7811, distance: 9.9857, memory: -0.0120, power: 0.3027, lr: 0.000100, took: 84.398s
[COOPERATIVE] Epoch 1, Batch 340/1563, loss: 10.218, reward: 32.149, critic_reward: 33.060, revenue_rate: 0.7912, distance: 10.1172, memory: -0.0280, power: 0.3069, lr: 0.000100, took: 82.473s
[COOPERATIVE] Epoch 1, Batch 350/1563, loss: 6.740, reward: 30.941, critic_reward: 30.832, revenue_rate: 0.7632, distance: 9.7453, memory: -0.0328, power: 0.2940, lr: 0.000100, took: 78.946s
[COOPERATIVE] Epoch 1, Batch 360/1563, loss: 14.662, reward: 31.892, critic_reward: 32.586, revenue_rate: 0.7836, distance: 9.8405, memory: -0.0334, power: 0.2987, lr: 0.000100, took: 80.142s
[COOPERATIVE] Epoch 1, Batch 370/1563, loss: 7.558, reward: 32.290, critic_reward: 32.889, revenue_rate: 0.7962, distance: 10.0289, memory: -0.0210, power: 0.3017, lr: 0.000100, took: 81.371s
[COOPERATIVE] Epoch 1, Batch 380/1563, loss: 7.399, reward: 31.156, critic_reward: 30.784, revenue_rate: 0.7670, distance: 9.5424, memory: -0.0341, power: 0.2890, lr: 0.000100, took: 76.938s
[COOPERATIVE] Epoch 1, Batch 390/1563, loss: 14.614, reward: 29.596, critic_reward: 28.358, revenue_rate: 0.7268, distance: 8.9382, memory: -0.0540, power: 0.2701, lr: 0.000100, took: 71.952s
[COOPERATIVE] Epoch 1, Batch 400/1563, loss: 38.264, reward: 31.042, critic_reward: 34.483, revenue_rate: 0.7643, distance: 9.6389, memory: -0.0358, power: 0.2903, lr: 0.000100, took: 78.206s
[COOPERATIVE] Epoch 1, Batch 410/1563, loss: 7.263, reward: 30.145, critic_reward: 29.424, revenue_rate: 0.7401, distance: 9.1761, memory: -0.0507, power: 0.2773, lr: 0.000100, took: 73.774s
[COOPERATIVE] Epoch 1, Batch 420/1563, loss: 12.422, reward: 29.892, critic_reward: 29.642, revenue_rate: 0.7327, distance: 9.0204, memory: -0.0556, power: 0.2721, lr: 0.000100, took: 73.378s
[COOPERATIVE] Epoch 1, Batch 430/1563, loss: 15.433, reward: 29.076, critic_reward: 28.162, revenue_rate: 0.7134, distance: 8.6770, memory: -0.0612, power: 0.2651, lr: 0.000100, took: 70.554s
[COOPERATIVE] Epoch 1, Batch 440/1563, loss: 8.137, reward: 27.046, critic_reward: 26.591, revenue_rate: 0.6607, distance: 7.9175, memory: -0.0890, power: 0.2380, lr: 0.000100, took: 62.728s
[COOPERATIVE] Epoch 1, Batch 450/1563, loss: 7.919, reward: 27.453, critic_reward: 26.744, revenue_rate: 0.6725, distance: 8.0954, memory: -0.0852, power: 0.2447, lr: 0.000100, took: 66.882s
[COOPERATIVE] Epoch 1, Batch 460/1563, loss: 3.504, reward: 28.628, critic_reward: 28.697, revenue_rate: 0.7003, distance: 8.4751, memory: -0.0676, power: 0.2564, lr: 0.000100, took: 68.113s
[COOPERATIVE] Epoch 1, Batch 470/1563, loss: 3.602, reward: 30.889, critic_reward: 31.149, revenue_rate: 0.7617, distance: 9.4785, memory: -0.0381, power: 0.2858, lr: 0.000100, took: 76.218s
[COOPERATIVE] Epoch 1, Batch 480/1563, loss: 4.101, reward: 31.479, critic_reward: 31.494, revenue_rate: 0.7747, distance: 9.6222, memory: -0.0423, power: 0.2918, lr: 0.000100, took: 78.563s
[COOPERATIVE] Epoch 1, Batch 490/1563, loss: 5.148, reward: 29.575, critic_reward: 29.207, revenue_rate: 0.7273, distance: 8.8785, memory: -0.0588, power: 0.2678, lr: 0.000100, took: 71.967s
[COOPERATIVE] Epoch 1, Batch 500/1563, loss: 4.180, reward: 30.574, critic_reward: 30.854, revenue_rate: 0.7504, distance: 9.1861, memory: -0.0528, power: 0.2790, lr: 0.000100, took: 74.411s
[COOPERATIVE] Epoch 1, Batch 510/1563, loss: 9.279, reward: 29.795, critic_reward: 30.404, revenue_rate: 0.7335, distance: 8.9267, memory: -0.0627, power: 0.2694, lr: 0.000100, took: 71.760s
[COOPERATIVE] Epoch 1, Batch 520/1563, loss: 10.775, reward: 29.228, critic_reward: 28.797, revenue_rate: 0.7137, distance: 8.6319, memory: -0.0688, power: 0.2623, lr: 0.000100, took: 69.761s
[COOPERATIVE] Epoch 1, Batch 530/1563, loss: 6.863, reward: 31.841, critic_reward: 32.687, revenue_rate: 0.7832, distance: 9.7652, memory: -0.0272, power: 0.2956, lr: 0.000100, took: 79.229s
[COOPERATIVE] Epoch 1, Batch 540/1563, loss: 8.998, reward: 30.615, critic_reward: 30.748, revenue_rate: 0.7491, distance: 9.1561, memory: -0.0514, power: 0.2764, lr: 0.000100, took: 74.126s
[COOPERATIVE] Epoch 1, Batch 550/1563, loss: 12.407, reward: 30.858, critic_reward: 31.766, revenue_rate: 0.7554, distance: 9.3253, memory: -0.0453, power: 0.2815, lr: 0.000100, took: 76.099s
[COOPERATIVE] Epoch 1, Batch 560/1563, loss: 19.002, reward: 31.793, critic_reward: 29.862, revenue_rate: 0.7856, distance: 9.9511, memory: -0.0184, power: 0.3015, lr: 0.000100, took: 80.866s
[COOPERATIVE] Epoch 1, Batch 570/1563, loss: 6.370, reward: 30.221, critic_reward: 30.496, revenue_rate: 0.7438, distance: 9.2674, memory: -0.0495, power: 0.2798, lr: 0.000100, took: 76.872s
[COOPERATIVE] Epoch 1, Batch 580/1563, loss: 9.599, reward: 28.654, critic_reward: 28.308, revenue_rate: 0.7029, distance: 8.7063, memory: -0.0549, power: 0.2627, lr: 0.000100, took: 70.081s
[COOPERATIVE] Epoch 1, Batch 590/1563, loss: 13.811, reward: 28.114, critic_reward: 29.328, revenue_rate: 0.6896, distance: 8.4078, memory: -0.0766, power: 0.2527, lr: 0.000100, took: 67.731s
[COOPERATIVE] Epoch 1, Batch 600/1563, loss: 10.064, reward: 27.651, critic_reward: 27.095, revenue_rate: 0.6776, distance: 8.2074, memory: -0.0703, power: 0.2507, lr: 0.000100, took: 65.766s
[COOPERATIVE] Epoch 1, Batch 610/1563, loss: 6.403, reward: 29.337, critic_reward: 28.833, revenue_rate: 0.7193, distance: 8.7171, memory: -0.0566, power: 0.2658, lr: 0.000100, took: 70.062s
[COOPERATIVE] Epoch 1, Batch 620/1563, loss: 11.356, reward: 32.128, critic_reward: 30.864, revenue_rate: 0.7913, distance: 9.9560, memory: -0.0298, power: 0.3004, lr: 0.000100, took: 81.451s
[COOPERATIVE] Epoch 1, Batch 630/1563, loss: 9.134, reward: 31.980, critic_reward: 32.854, revenue_rate: 0.7859, distance: 9.7048, memory: -0.0299, power: 0.2940, lr: 0.000100, took: 78.485s
[COOPERATIVE] Epoch 1, Batch 640/1563, loss: 17.337, reward: 30.516, critic_reward: 29.488, revenue_rate: 0.7493, distance: 9.1839, memory: -0.0433, power: 0.2775, lr: 0.000100, took: 74.931s
[COOPERATIVE] Epoch 1, Batch 650/1563, loss: 14.618, reward: 29.424, critic_reward: 29.482, revenue_rate: 0.7199, distance: 8.7124, memory: -0.0609, power: 0.2654, lr: 0.000100, took: 70.411s
[COOPERATIVE] Epoch 1, Batch 660/1563, loss: 5.126, reward: 30.719, critic_reward: 30.456, revenue_rate: 0.7558, distance: 9.3421, memory: -0.0323, power: 0.2843, lr: 0.000100, took: 75.980s
[COOPERATIVE] Epoch 1, Batch 670/1563, loss: 5.203, reward: 31.103, critic_reward: 30.931, revenue_rate: 0.7607, distance: 9.4016, memory: -0.0410, power: 0.2852, lr: 0.000100, took: 77.462s
[COOPERATIVE] Epoch 1, Batch 680/1563, loss: 13.449, reward: 30.060, critic_reward: 29.845, revenue_rate: 0.7363, distance: 8.9526, memory: -0.0451, power: 0.2718, lr: 0.000100, took: 72.661s
[COOPERATIVE] Epoch 1, Batch 690/1563, loss: 11.096, reward: 31.076, critic_reward: 32.221, revenue_rate: 0.7600, distance: 9.2202, memory: -0.0439, power: 0.2812, lr: 0.000100, took: 76.023s
[COOPERATIVE] Epoch 1, Batch 700/1563, loss: 10.755, reward: 31.645, critic_reward: 29.889, revenue_rate: 0.7757, distance: 9.5386, memory: -0.0361, power: 0.2876, lr: 0.000100, took: 80.115s
[COOPERATIVE] Epoch 1, Batch 710/1563, loss: 4.624, reward: 33.183, critic_reward: 33.043, revenue_rate: 0.8228, distance: 10.5018, memory: -0.0041, power: 0.3156, lr: 0.000100, took: 86.548s
[COOPERATIVE] Epoch 1, Batch 720/1563, loss: 17.263, reward: 33.158, critic_reward: 34.378, revenue_rate: 0.8170, distance: 10.4625, memory: -0.0126, power: 0.3149, lr: 0.000100, took: 86.700s
[COOPERATIVE] Epoch 1, Batch 730/1563, loss: 7.611, reward: 31.108, critic_reward: 31.517, revenue_rate: 0.7664, distance: 9.4565, memory: -0.0425, power: 0.2861, lr: 0.000100, took: 79.915s
[COOPERATIVE] Epoch 1, Batch 740/1563, loss: 4.776, reward: 28.979, critic_reward: 28.523, revenue_rate: 0.7088, distance: 8.6026, memory: -0.0632, power: 0.2595, lr: 0.000100, took: 72.953s
[COOPERATIVE] Epoch 1, Batch 750/1563, loss: 3.702, reward: 29.411, critic_reward: 29.076, revenue_rate: 0.7219, distance: 8.7435, memory: -0.0591, power: 0.2652, lr: 0.000100, took: 72.978s
[COOPERATIVE] Epoch 1, Batch 760/1563, loss: 4.788, reward: 29.319, critic_reward: 28.849, revenue_rate: 0.7180, distance: 8.8952, memory: -0.0513, power: 0.2697, lr: 0.000100, took: 74.833s
[COOPERATIVE] Epoch 1, Batch 770/1563, loss: 7.093, reward: 29.787, critic_reward: 29.241, revenue_rate: 0.7318, distance: 9.1230, memory: -0.0480, power: 0.2746, lr: 0.000100, took: 77.524s
[COOPERATIVE] Epoch 1, Batch 780/1563, loss: 2.747, reward: 31.304, critic_reward: 31.468, revenue_rate: 0.7689, distance: 9.4810, memory: -0.0354, power: 0.2851, lr: 0.000100, took: 79.022s
[COOPERATIVE] Epoch 1, Batch 790/1563, loss: 5.108, reward: 32.491, critic_reward: 32.376, revenue_rate: 0.7995, distance: 9.8970, memory: -0.0394, power: 0.3010, lr: 0.000100, took: 85.037s
[COOPERATIVE] Epoch 1, Batch 800/1563, loss: 3.831, reward: 33.142, critic_reward: 32.883, revenue_rate: 0.8186, distance: 10.3662, memory: -0.0122, power: 0.3151, lr: 0.000100, took: 89.443s
[COOPERATIVE] Epoch 1, Batch 810/1563, loss: 3.356, reward: 33.112, critic_reward: 33.124, revenue_rate: 0.8192, distance: 10.4502, memory: -0.0087, power: 0.3165, lr: 0.000100, took: 91.851s
[COOPERATIVE] Epoch 1, Batch 820/1563, loss: 6.510, reward: 32.659, critic_reward: 32.584, revenue_rate: 0.8040, distance: 10.0236, memory: -0.0185, power: 0.3040, lr: 0.000100, took: 86.983s
[COOPERATIVE] Epoch 1, Batch 830/1563, loss: 7.578, reward: 31.991, critic_reward: 32.782, revenue_rate: 0.7850, distance: 9.6339, memory: -0.0335, power: 0.2963, lr: 0.000100, took: 82.743s
[COOPERATIVE] Epoch 1, Batch 840/1563, loss: 4.789, reward: 30.991, critic_reward: 30.656, revenue_rate: 0.7606, distance: 9.3747, memory: -0.0359, power: 0.2863, lr: 0.000100, took: 78.730s
[COOPERATIVE] Epoch 1, Batch 850/1563, loss: 3.973, reward: 29.826, critic_reward: 29.660, revenue_rate: 0.7280, distance: 8.7940, memory: -0.0531, power: 0.2666, lr: 0.000100, took: 73.383s
[COOPERATIVE] Epoch 1, Batch 860/1563, loss: 3.269, reward: 31.356, critic_reward: 31.069, revenue_rate: 0.7677, distance: 9.4919, memory: -0.0330, power: 0.2872, lr: 0.000100, took: 79.309s
[COOPERATIVE] Epoch 1, Batch 870/1563, loss: 3.250, reward: 32.059, critic_reward: 32.067, revenue_rate: 0.7882, distance: 9.8478, memory: -0.0266, power: 0.2979, lr: 0.000100, took: 84.506s
[COOPERATIVE] Epoch 1, Batch 880/1563, loss: 5.944, reward: 32.377, critic_reward: 32.153, revenue_rate: 0.7953, distance: 9.8544, memory: -0.0256, power: 0.2980, lr: 0.000100, took: 84.170s
[COOPERATIVE] Epoch 1, Batch 890/1563, loss: 8.583, reward: 32.623, critic_reward: 32.634, revenue_rate: 0.8052, distance: 9.9873, memory: -0.0340, power: 0.3023, lr: 0.000100, took: 86.908s
[COOPERATIVE] Epoch 1, Batch 900/1563, loss: 3.420, reward: 31.977, critic_reward: 31.902, revenue_rate: 0.7838, distance: 9.7983, memory: -0.0325, power: 0.2966, lr: 0.000100, took: 81.143s
[COOPERATIVE] Epoch 1, Batch 910/1563, loss: 18.522, reward: 31.362, critic_reward: 29.054, revenue_rate: 0.7698, distance: 9.5224, memory: -0.0390, power: 0.2889, lr: 0.000100, took: 80.461s
[COOPERATIVE] Epoch 1, Batch 920/1563, loss: 4.551, reward: 31.888, critic_reward: 32.250, revenue_rate: 0.7830, distance: 9.7514, memory: -0.0355, power: 0.2940, lr: 0.000100, took: 84.954s
[COOPERATIVE] Epoch 1, Batch 930/1563, loss: 9.917, reward: 32.581, critic_reward: 31.998, revenue_rate: 0.7994, distance: 9.8830, memory: -0.0302, power: 0.2979, lr: 0.000100, took: 83.808s
[COOPERATIVE] Epoch 1, Batch 940/1563, loss: 3.914, reward: 32.762, critic_reward: 31.990, revenue_rate: 0.8044, distance: 9.9626, memory: -0.0167, power: 0.3025, lr: 0.000100, took: 85.983s
[COOPERATIVE] Epoch 1, Batch 950/1563, loss: 5.680, reward: 33.188, critic_reward: 32.882, revenue_rate: 0.8168, distance: 10.2712, memory: -0.0116, power: 0.3115, lr: 0.000100, took: 86.678s
[COOPERATIVE] Epoch 1, Batch 960/1563, loss: 3.994, reward: 32.982, critic_reward: 32.927, revenue_rate: 0.8126, distance: 10.1451, memory: -0.0100, power: 0.3088, lr: 0.000100, took: 84.553s
[COOPERATIVE] Epoch 1, Batch 970/1563, loss: 4.782, reward: 30.464, critic_reward: 30.000, revenue_rate: 0.7482, distance: 9.1439, memory: -0.0475, power: 0.2757, lr: 0.000100, took: 76.849s
[COOPERATIVE] Epoch 1, Batch 980/1563, loss: 4.808, reward: 31.225, critic_reward: 30.825, revenue_rate: 0.7672, distance: 9.5027, memory: -0.0341, power: 0.2880, lr: 0.000100, took: 79.976s
[COOPERATIVE] Epoch 1, Batch 990/1563, loss: 5.259, reward: 32.326, critic_reward: 32.928, revenue_rate: 0.7984, distance: 9.9534, memory: -0.0194, power: 0.3024, lr: 0.000100, took: 83.734s
[COOPERATIVE] Epoch 1, Batch 1000/1563, loss: 4.686, reward: 31.738, critic_reward: 31.842, revenue_rate: 0.7822, distance: 9.6174, memory: -0.0394, power: 0.2915, lr: 0.000100, took: 81.027s
[COOPERATIVE] Epoch 1, Batch 1010/1563, loss: 4.981, reward: 31.889, critic_reward: 32.442, revenue_rate: 0.7834, distance: 9.6676, memory: -0.0387, power: 0.2920, lr: 0.000100, took: 81.954s
[COOPERATIVE] Epoch 1, Batch 1020/1563, loss: 3.632, reward: 28.952, critic_reward: 28.531, revenue_rate: 0.7080, distance: 8.4543, memory: -0.0726, power: 0.2578, lr: 0.000100, took: 70.339s
[COOPERATIVE] Epoch 1, Batch 1030/1563, loss: 4.487, reward: 31.438, critic_reward: 31.312, revenue_rate: 0.7726, distance: 9.4416, memory: -0.0368, power: 0.2869, lr: 0.000100, took: 80.133s
[COOPERATIVE] Epoch 1, Batch 1040/1563, loss: 10.268, reward: 33.704, critic_reward: 34.012, revenue_rate: 0.8347, distance: 10.7761, memory: 0.0052, power: 0.3283, lr: 0.000100, took: 90.530s
[COOPERATIVE] Epoch 1, Batch 1050/1563, loss: 6.087, reward: 33.923, critic_reward: 34.706, revenue_rate: 0.8385, distance: 10.8195, memory: 0.0052, power: 0.3259, lr: 0.000100, took: 91.173s
[COOPERATIVE] Epoch 1, Batch 1060/1563, loss: 10.304, reward: 33.442, critic_reward: 33.958, revenue_rate: 0.8225, distance: 10.4112, memory: -0.0129, power: 0.3173, lr: 0.000100, took: 87.602s
[COOPERATIVE] Epoch 1, Batch 1070/1563, loss: 11.025, reward: 30.488, critic_reward: 29.737, revenue_rate: 0.7483, distance: 9.2800, memory: -0.0490, power: 0.2811, lr: 0.000100, took: 76.450s
[COOPERATIVE] Epoch 1, Batch 1080/1563, loss: 5.007, reward: 28.471, critic_reward: 28.523, revenue_rate: 0.6989, distance: 8.6467, memory: -0.0653, power: 0.2597, lr: 0.000100, took: 70.185s
[COOPERATIVE] Epoch 1, Batch 1090/1563, loss: 15.269, reward: 28.530, critic_reward: 31.052, revenue_rate: 0.6971, distance: 8.4806, memory: -0.0669, power: 0.2567, lr: 0.000100, took: 70.426s
[COOPERATIVE] Epoch 1, Batch 1100/1563, loss: 11.437, reward: 28.958, critic_reward: 27.179, revenue_rate: 0.7087, distance: 8.4470, memory: -0.0696, power: 0.2580, lr: 0.000100, took: 71.112s
[COOPERATIVE] Epoch 1, Batch 1110/1563, loss: 4.296, reward: 29.705, critic_reward: 29.868, revenue_rate: 0.7270, distance: 8.8600, memory: -0.0625, power: 0.2689, lr: 0.000100, took: 74.997s
[COOPERATIVE] Epoch 1, Batch 1120/1563, loss: 4.166, reward: 30.372, critic_reward: 29.404, revenue_rate: 0.7431, distance: 9.0039, memory: -0.0551, power: 0.2719, lr: 0.000100, took: 76.391s
[COOPERATIVE] Epoch 1, Batch 1130/1563, loss: 10.202, reward: 32.794, critic_reward: 32.111, revenue_rate: 0.8018, distance: 9.7987, memory: -0.0297, power: 0.2992, lr: 0.000100, took: 83.487s
[COOPERATIVE] Epoch 1, Batch 1140/1563, loss: 6.950, reward: 33.829, critic_reward: 34.188, revenue_rate: 0.8327, distance: 10.3748, memory: -0.0071, power: 0.3153, lr: 0.000100, took: 88.951s
[COOPERATIVE] Epoch 1, Batch 1150/1563, loss: 3.617, reward: 33.153, critic_reward: 32.919, revenue_rate: 0.8172, distance: 10.2375, memory: -0.0146, power: 0.3076, lr: 0.000100, took: 86.056s
[COOPERATIVE] Epoch 1, Batch 1160/1563, loss: 5.820, reward: 32.962, critic_reward: 33.318, revenue_rate: 0.8087, distance: 10.0348, memory: -0.0246, power: 0.3039, lr: 0.000100, took: 84.439s
[COOPERATIVE] Epoch 1, Batch 1170/1563, loss: 6.644, reward: 34.444, critic_reward: 33.657, revenue_rate: 0.8488, distance: 10.7844, memory: 0.0023, power: 0.3260, lr: 0.000100, took: 93.620s
[COOPERATIVE] Epoch 1, Batch 1180/1563, loss: 3.892, reward: 33.043, critic_reward: 32.820, revenue_rate: 0.8151, distance: 10.1987, memory: -0.0128, power: 0.3081, lr: 0.000100, took: 87.325s
[COOPERATIVE] Epoch 1, Batch 1190/1563, loss: 7.544, reward: 30.920, critic_reward: 32.152, revenue_rate: 0.7565, distance: 9.2873, memory: -0.0517, power: 0.2789, lr: 0.000100, took: 77.852s
[COOPERATIVE] Epoch 1, Batch 1200/1563, loss: 2.990, reward: 28.201, critic_reward: 27.653, revenue_rate: 0.6895, distance: 8.1561, memory: -0.0843, power: 0.2506, lr: 0.000100, took: 67.799s
[COOPERATIVE] Epoch 1, Batch 1210/1563, loss: 3.094, reward: 30.446, critic_reward: 30.595, revenue_rate: 0.7427, distance: 8.9620, memory: -0.0527, power: 0.2724, lr: 0.000100, took: 73.914s
[COOPERATIVE] Epoch 1, Batch 1220/1563, loss: 4.606, reward: 31.278, critic_reward: 30.741, revenue_rate: 0.7662, distance: 9.3825, memory: -0.0415, power: 0.2846, lr: 0.000100, took: 78.870s
[COOPERATIVE] Epoch 1, Batch 1230/1563, loss: 3.476, reward: 31.639, critic_reward: 31.623, revenue_rate: 0.7751, distance: 9.4196, memory: -0.0463, power: 0.2838, lr: 0.000100, took: 75.442s
[COOPERATIVE] Epoch 1, Batch 1240/1563, loss: 2.557, reward: 32.425, critic_reward: 32.384, revenue_rate: 0.7972, distance: 9.8587, memory: -0.0336, power: 0.2969, lr: 0.000100, took: 80.116s
[COOPERATIVE] Epoch 1, Batch 1250/1563, loss: 4.735, reward: 32.905, critic_reward: 33.173, revenue_rate: 0.8094, distance: 9.9801, memory: -0.0224, power: 0.3037, lr: 0.000100, took: 87.916s
[COOPERATIVE] Epoch 1, Batch 1260/1563, loss: 2.738, reward: 33.064, critic_reward: 32.892, revenue_rate: 0.8145, distance: 10.1194, memory: -0.0233, power: 0.3064, lr: 0.000100, took: 84.338s
[COOPERATIVE] Epoch 1, Batch 1270/1563, loss: 9.757, reward: 33.975, critic_reward: 33.911, revenue_rate: 0.8357, distance: 10.4894, memory: -0.0112, power: 0.3187, lr: 0.000100, took: 88.792s
[COOPERATIVE] Epoch 1, Batch 1280/1563, loss: 9.632, reward: 31.907, critic_reward: 31.603, revenue_rate: 0.7839, distance: 9.5608, memory: -0.0320, power: 0.2919, lr: 0.000100, took: 80.856s
[COOPERATIVE] Epoch 1, Batch 1290/1563, loss: 9.457, reward: 31.191, critic_reward: 32.243, revenue_rate: 0.7619, distance: 9.1721, memory: -0.0413, power: 0.2792, lr: 0.000100, took: 77.399s
[COOPERATIVE] Epoch 1, Batch 1300/1563, loss: 15.173, reward: 30.894, critic_reward: 29.206, revenue_rate: 0.7579, distance: 9.2295, memory: -0.0476, power: 0.2796, lr: 0.000100, took: 74.993s
[COOPERATIVE] Epoch 1, Batch 1310/1563, loss: 4.058, reward: 32.233, critic_reward: 31.547, revenue_rate: 0.7923, distance: 9.7850, memory: -0.0197, power: 0.2968, lr: 0.000100, took: 80.204s
[COOPERATIVE] Epoch 1, Batch 1320/1563, loss: 6.049, reward: 31.469, critic_reward: 30.783, revenue_rate: 0.7736, distance: 9.7012, memory: -0.0420, power: 0.2902, lr: 0.000100, took: 80.123s
[COOPERATIVE] Epoch 1, Batch 1330/1563, loss: 4.057, reward: 30.667, critic_reward: 30.365, revenue_rate: 0.7522, distance: 9.1834, memory: -0.0545, power: 0.2774, lr: 0.000100, took: 76.578s
[COOPERATIVE] Epoch 1, Batch 1340/1563, loss: 9.007, reward: 31.934, critic_reward: 31.469, revenue_rate: 0.7850, distance: 9.6811, memory: -0.0356, power: 0.2933, lr: 0.000100, took: 81.672s
[COOPERATIVE] Epoch 1, Batch 1350/1563, loss: 19.717, reward: 30.971, critic_reward: 33.897, revenue_rate: 0.7578, distance: 9.1418, memory: -0.0467, power: 0.2801, lr: 0.000100, took: 77.446s
[COOPERATIVE] Epoch 1, Batch 1360/1563, loss: 4.858, reward: 29.005, critic_reward: 28.845, revenue_rate: 0.7097, distance: 8.5631, memory: -0.0674, power: 0.2592, lr: 0.000100, took: 74.974s
[COOPERATIVE] Epoch 1, Batch 1370/1563, loss: 4.153, reward: 31.031, critic_reward: 31.517, revenue_rate: 0.7637, distance: 9.4655, memory: -0.0292, power: 0.2869, lr: 0.000100, took: 78.225s
[COOPERATIVE] Epoch 1, Batch 1380/1563, loss: 5.760, reward: 31.963, critic_reward: 32.385, revenue_rate: 0.7893, distance: 9.9862, memory: -0.0107, power: 0.3015, lr: 0.000100, took: 83.587s
[COOPERATIVE] Epoch 1, Batch 1390/1563, loss: 5.297, reward: 29.138, critic_reward: 29.405, revenue_rate: 0.7128, distance: 8.6342, memory: -0.0613, power: 0.2621, lr: 0.000100, took: 70.694s
[COOPERATIVE] Epoch 1, Batch 1400/1563, loss: 18.514, reward: 30.007, critic_reward: 27.949, revenue_rate: 0.7354, distance: 8.8588, memory: -0.0568, power: 0.2694, lr: 0.000100, took: 72.186s
[COOPERATIVE] Epoch 1, Batch 1410/1563, loss: 17.012, reward: 34.370, critic_reward: 35.376, revenue_rate: 0.8499, distance: 11.0569, memory: 0.0041, power: 0.3325, lr: 0.000100, took: 93.905s
[COOPERATIVE] Epoch 1, Batch 1420/1563, loss: 5.897, reward: 35.547, critic_reward: 36.208, revenue_rate: 0.8824, distance: 11.5261, memory: 0.0183, power: 0.3493, lr: 0.000100, took: 104.503s
[COOPERATIVE] Epoch 1, Batch 1430/1563, loss: 5.634, reward: 34.067, critic_reward: 33.432, revenue_rate: 0.8423, distance: 10.6801, memory: -0.0059, power: 0.3226, lr: 0.000100, took: 92.480s
[COOPERATIVE] Epoch 1, Batch 1440/1563, loss: 4.480, reward: 30.662, critic_reward: 30.542, revenue_rate: 0.7500, distance: 9.0363, memory: -0.0542, power: 0.2749, lr: 0.000100, took: 77.439s
[COOPERATIVE] Epoch 1, Batch 1450/1563, loss: 5.084, reward: 29.162, critic_reward: 29.319, revenue_rate: 0.7154, distance: 8.4962, memory: -0.0731, power: 0.2599, lr: 0.000100, took: 69.736s
[COOPERATIVE] Epoch 1, Batch 1460/1563, loss: 2.781, reward: 30.115, critic_reward: 29.727, revenue_rate: 0.7393, distance: 8.8616, memory: -0.0516, power: 0.2709, lr: 0.000100, took: 77.257s
[COOPERATIVE] Epoch 1, Batch 1470/1563, loss: 3.780, reward: 31.500, critic_reward: 31.826, revenue_rate: 0.7728, distance: 9.4146, memory: -0.0427, power: 0.2866, lr: 0.000100, took: 84.973s
[COOPERATIVE] Epoch 1, Batch 1480/1563, loss: 6.301, reward: 31.893, critic_reward: 31.028, revenue_rate: 0.7815, distance: 9.5441, memory: -0.0378, power: 0.2924, lr: 0.000100, took: 82.129s
[COOPERATIVE] Epoch 1, Batch 1490/1563, loss: 11.094, reward: 33.424, critic_reward: 34.781, revenue_rate: 0.8260, distance: 10.2834, memory: -0.0103, power: 0.3152, lr: 0.000100, took: 88.260s
[COOPERATIVE] Epoch 1, Batch 1500/1563, loss: 8.933, reward: 34.050, critic_reward: 33.807, revenue_rate: 0.8415, distance: 10.7127, memory: 0.0018, power: 0.3239, lr: 0.000100, took: 89.735s
[COOPERATIVE] Epoch 1, Batch 1510/1563, loss: 3.967, reward: 33.167, critic_reward: 33.326, revenue_rate: 0.8165, distance: 10.0847, memory: -0.0199, power: 0.3080, lr: 0.000100, took: 85.325s
[COOPERATIVE] Epoch 1, Batch 1520/1563, loss: 8.602, reward: 31.035, critic_reward: 31.742, revenue_rate: 0.7578, distance: 9.0660, memory: -0.0520, power: 0.2764, lr: 0.000100, took: 75.126s
[COOPERATIVE] Epoch 1, Batch 1530/1563, loss: 6.566, reward: 29.529, critic_reward: 28.623, revenue_rate: 0.7207, distance: 8.5595, memory: -0.0581, power: 0.2609, lr: 0.000100, took: 69.181s
[COOPERATIVE] Epoch 1, Batch 1540/1563, loss: 4.817, reward: 30.568, critic_reward: 31.571, revenue_rate: 0.7506, distance: 8.9775, memory: -0.0586, power: 0.2734, lr: 0.000100, took: 72.633s
[COOPERATIVE] Epoch 1, Batch 1550/1563, loss: 12.167, reward: 31.387, critic_reward: 28.861, revenue_rate: 0.7696, distance: 9.3135, memory: -0.0428, power: 0.2837, lr: 0.000100, took: 75.465s
[COOPERATIVE] Epoch 1, Batch 1560/1563, loss: 3.314, reward: 32.720, critic_reward: 33.114, revenue_rate: 0.8050, distance: 9.9790, memory: -0.0179, power: 0.3011, lr: 0.000100, took: 80.742s
[COOPERATIVE] 开始验证...
[COOPERATIVE] 验证完成 - Epoch 1, reward: 33.664, revenue_rate: 0.8287, distance: 10.2830, memory: -0.0143, power: 0.3115
[COOPERATIVE] 已保存新模型到 constellation_smp\constellation_smp100\multi_constellation_comparison_2025_08_25_17_02_29\constellation_gpnindrnn_cooperative_2025_08_25_17_02_29 (验证集奖励: 33.6641)
[COOPERATIVE] 开始训练 Epoch 2/3
[COOPERATIVE] Epoch 2, Batch 10/1563, loss: 4.799, reward: 33.170, critic_reward: 33.488, revenue_rate: 0.8167, distance: 10.1930, memory: -0.0110, power: 0.3096, lr: 0.000100, took: 89.406s
[COOPERATIVE] Epoch 2, Batch 20/1563, loss: 6.595, reward: 32.017, critic_reward: 31.342, revenue_rate: 0.7850, distance: 9.5946, memory: -0.0408, power: 0.2928, lr: 0.000100, took: 79.162s
[COOPERATIVE] Epoch 2, Batch 30/1563, loss: 4.818, reward: 31.595, critic_reward: 31.989, revenue_rate: 0.7734, distance: 9.3818, memory: -0.0415, power: 0.2822, lr: 0.000100, took: 78.013s
[COOPERATIVE] Epoch 2, Batch 40/1563, loss: 3.354, reward: 32.399, critic_reward: 32.006, revenue_rate: 0.7928, distance: 9.6750, memory: -0.0369, power: 0.2927, lr: 0.000100, took: 79.941s
[COOPERATIVE] Epoch 2, Batch 50/1563, loss: 3.253, reward: 31.645, critic_reward: 31.503, revenue_rate: 0.7725, distance: 9.3349, memory: -0.0523, power: 0.2835, lr: 0.000100, took: 77.161s
[COOPERATIVE] Epoch 2, Batch 60/1563, loss: 4.523, reward: 30.289, critic_reward: 30.401, revenue_rate: 0.7418, distance: 8.9851, memory: -0.0505, power: 0.2723, lr: 0.000100, took: 73.342s
[COOPERATIVE] Epoch 2, Batch 70/1563, loss: 8.166, reward: 30.623, critic_reward: 28.870, revenue_rate: 0.7488, distance: 9.0280, memory: -0.0496, power: 0.2722, lr: 0.000100, took: 73.611s
[COOPERATIVE] Epoch 2, Batch 80/1563, loss: 4.259, reward: 33.333, critic_reward: 33.968, revenue_rate: 0.8212, distance: 10.2665, memory: -0.0155, power: 0.3103, lr: 0.000100, took: 84.797s
[COOPERATIVE] Epoch 2, Batch 90/1563, loss: 3.566, reward: 31.987, critic_reward: 32.573, revenue_rate: 0.7836, distance: 9.6068, memory: -0.0374, power: 0.2929, lr: 0.000100, took: 82.533s
[COOPERATIVE] Epoch 2, Batch 100/1563, loss: 3.306, reward: 30.292, critic_reward: 30.566, revenue_rate: 0.7425, distance: 8.9043, memory: -0.0558, power: 0.2694, lr: 0.000100, took: 72.789s
[COOPERATIVE] Epoch 2, Batch 110/1563, loss: 2.819, reward: 29.090, critic_reward: 29.342, revenue_rate: 0.7079, distance: 8.4324, memory: -0.0690, power: 0.2562, lr: 0.000100, took: 69.156s
[COOPERATIVE] Epoch 2, Batch 120/1563, loss: 3.937, reward: 30.377, critic_reward: 30.551, revenue_rate: 0.7454, distance: 9.0641, memory: -0.0507, power: 0.2732, lr: 0.000100, took: 74.009s
[COOPERATIVE] Epoch 2, Batch 130/1563, loss: 6.855, reward: 31.529, critic_reward: 32.144, revenue_rate: 0.7745, distance: 9.4184, memory: -0.0436, power: 0.2849, lr: 0.000100, took: 77.614s
[COOPERATIVE] Epoch 2, Batch 140/1563, loss: 3.356, reward: 32.118, critic_reward: 32.012, revenue_rate: 0.7904, distance: 9.9148, memory: -0.0246, power: 0.3005, lr: 0.000100, took: 81.971s
[COOPERATIVE] Epoch 2, Batch 150/1563, loss: 3.097, reward: 32.287, critic_reward: 32.398, revenue_rate: 0.7954, distance: 9.8374, memory: -0.0290, power: 0.2987, lr: 0.000100, took: 81.336s
[COOPERATIVE] Epoch 2, Batch 160/1563, loss: 2.299, reward: 33.052, critic_reward: 33.032, revenue_rate: 0.8112, distance: 10.0798, memory: -0.0164, power: 0.3075, lr: 0.000100, took: 84.279s
[COOPERATIVE] Epoch 2, Batch 170/1563, loss: 2.555, reward: 32.765, critic_reward: 32.671, revenue_rate: 0.8063, distance: 10.0308, memory: -0.0258, power: 0.3023, lr: 0.000100, took: 83.286s
[COOPERATIVE] Epoch 2, Batch 180/1563, loss: 4.440, reward: 31.798, critic_reward: 31.119, revenue_rate: 0.7811, distance: 9.5305, memory: -0.0368, power: 0.2906, lr: 0.000100, took: 78.176s
[COOPERATIVE] Epoch 2, Batch 190/1563, loss: 3.418, reward: 32.623, critic_reward: 32.652, revenue_rate: 0.8007, distance: 9.8794, memory: -0.0251, power: 0.2975, lr: 0.000100, took: 81.798s
[COOPERATIVE] Epoch 2, Batch 200/1563, loss: 3.052, reward: 33.087, critic_reward: 32.941, revenue_rate: 0.8166, distance: 10.2486, memory: -0.0094, power: 0.3121, lr: 0.000100, took: 87.586s
[COOPERATIVE] Epoch 2, Batch 210/1563, loss: 2.507, reward: 31.409, critic_reward: 31.540, revenue_rate: 0.7676, distance: 9.3611, memory: -0.0358, power: 0.2861, lr: 0.000100, took: 77.707s
[COOPERATIVE] Epoch 2, Batch 220/1563, loss: 2.508, reward: 29.897, critic_reward: 29.964, revenue_rate: 0.7287, distance: 8.7588, memory: -0.0655, power: 0.2646, lr: 0.000100, took: 71.328s
[COOPERATIVE] Epoch 2, Batch 230/1563, loss: 4.220, reward: 30.591, critic_reward: 30.580, revenue_rate: 0.7493, distance: 9.0820, memory: -0.0470, power: 0.2744, lr: 0.000100, took: 74.220s
[COOPERATIVE] Epoch 2, Batch 240/1563, loss: 3.641, reward: 31.850, critic_reward: 32.276, revenue_rate: 0.7805, distance: 9.5002, memory: -0.0379, power: 0.2895, lr: 0.000100, took: 78.210s
[COOPERATIVE] Epoch 2, Batch 250/1563, loss: 3.640, reward: 31.931, critic_reward: 32.174, revenue_rate: 0.7843, distance: 9.4900, memory: -0.0342, power: 0.2898, lr: 0.000100, took: 78.786s
[COOPERATIVE] Epoch 2, Batch 260/1563, loss: 3.669, reward: 30.770, critic_reward: 30.650, revenue_rate: 0.7532, distance: 9.0123, memory: -0.0448, power: 0.2737, lr: 0.000100, took: 74.158s
[COOPERATIVE] Epoch 2, Batch 270/1563, loss: 4.010, reward: 31.494, critic_reward: 31.687, revenue_rate: 0.7708, distance: 9.4414, memory: -0.0460, power: 0.2824, lr: 0.000100, took: 76.748s
[COOPERATIVE] Epoch 2, Batch 280/1563, loss: 2.427, reward: 31.899, critic_reward: 31.689, revenue_rate: 0.7811, distance: 9.3645, memory: -0.0344, power: 0.2863, lr: 0.000100, took: 77.839s
[COOPERATIVE] Epoch 2, Batch 290/1563, loss: 3.111, reward: 33.060, critic_reward: 32.932, revenue_rate: 0.8118, distance: 10.0412, memory: -0.0230, power: 0.3041, lr: 0.000100, took: 83.215s
[COOPERATIVE] Epoch 2, Batch 300/1563, loss: 4.930, reward: 32.119, critic_reward: 32.511, revenue_rate: 0.7874, distance: 9.6342, memory: -0.0336, power: 0.2931, lr: 0.000100, took: 79.696s
[COOPERATIVE] Epoch 2, Batch 310/1563, loss: 7.373, reward: 32.331, critic_reward: 31.296, revenue_rate: 0.7940, distance: 9.7903, memory: -0.0339, power: 0.2965, lr: 0.000100, took: 80.603s
[COOPERATIVE] Epoch 2, Batch 320/1563, loss: 4.064, reward: 31.434, critic_reward: 30.848, revenue_rate: 0.7688, distance: 9.3278, memory: -0.0421, power: 0.2817, lr: 0.000100, took: 78.150s
[COOPERATIVE] Epoch 2, Batch 330/1563, loss: 3.002, reward: 31.159, critic_reward: 30.637, revenue_rate: 0.7630, distance: 9.2668, memory: -0.0503, power: 0.2804, lr: 0.000100, took: 75.828s
[COOPERATIVE] Epoch 2, Batch 340/1563, loss: 4.282, reward: 32.135, critic_reward: 32.763, revenue_rate: 0.7872, distance: 9.5849, memory: -0.0341, power: 0.2915, lr: 0.000100, took: 79.306s
[COOPERATIVE] Epoch 2, Batch 350/1563, loss: 3.955, reward: 31.379, critic_reward: 31.687, revenue_rate: 0.7671, distance: 9.2606, memory: -0.0532, power: 0.2807, lr: 0.000100, took: 76.194s
[COOPERATIVE] Epoch 2, Batch 360/1563, loss: 9.646, reward: 30.674, critic_reward: 30.798, revenue_rate: 0.7487, distance: 8.9907, memory: -0.0543, power: 0.2731, lr: 0.000100, took: 73.762s
[COOPERATIVE] Epoch 2, Batch 370/1563, loss: 15.146, reward: 29.387, critic_reward: 31.081, revenue_rate: 0.7180, distance: 8.4517, memory: -0.0727, power: 0.2574, lr: 0.000100, took: 69.150s
[COOPERATIVE] Epoch 2, Batch 380/1563, loss: 6.095, reward: 29.649, critic_reward: 29.126, revenue_rate: 0.7226, distance: 8.5953, memory: -0.0747, power: 0.2609, lr: 0.000100, took: 70.408s
[COOPERATIVE] Epoch 2, Batch 390/1563, loss: 3.758, reward: 30.955, critic_reward: 31.245, revenue_rate: 0.7610, distance: 9.3416, memory: -0.0403, power: 0.2812, lr: 0.000100, took: 76.423s
[COOPERATIVE] Epoch 2, Batch 400/1563, loss: 4.136, reward: 32.363, critic_reward: 32.629, revenue_rate: 0.7956, distance: 9.7572, memory: -0.0329, power: 0.2965, lr: 0.000100, took: 81.364s
[COOPERATIVE] Epoch 2, Batch 410/1563, loss: 3.247, reward: 31.898, critic_reward: 32.035, revenue_rate: 0.7792, distance: 9.4598, memory: -0.0350, power: 0.2886, lr: 0.000100, took: 78.524s
[COOPERATIVE] Epoch 2, Batch 420/1563, loss: 3.716, reward: 29.518, critic_reward: 30.243, revenue_rate: 0.7218, distance: 8.6077, memory: -0.0562, power: 0.2628, lr: 0.000100, took: 71.786s
[COOPERATIVE] Epoch 2, Batch 430/1563, loss: 3.601, reward: 29.518, critic_reward: 28.603, revenue_rate: 0.7234, distance: 8.6927, memory: -0.0586, power: 0.2649, lr: 0.000100, took: 71.585s
[COOPERATIVE] Epoch 2, Batch 440/1563, loss: 3.768, reward: 31.484, critic_reward: 31.605, revenue_rate: 0.7713, distance: 9.2941, memory: -0.0419, power: 0.2819, lr: 0.000100, took: 78.601s
[COOPERATIVE] Epoch 2, Batch 450/1563, loss: 5.030, reward: 32.323, critic_reward: 33.011, revenue_rate: 0.7881, distance: 9.5512, memory: -0.0419, power: 0.2920, lr: 0.000100, took: 78.781s
[COOPERATIVE] Epoch 2, Batch 460/1563, loss: 3.373, reward: 31.402, critic_reward: 31.233, revenue_rate: 0.7710, distance: 9.2939, memory: -0.0362, power: 0.2830, lr: 0.000100, took: 76.567s
[COOPERATIVE] Epoch 2, Batch 470/1563, loss: 2.588, reward: 33.104, critic_reward: 32.943, revenue_rate: 0.8125, distance: 9.9907, memory: -0.0211, power: 0.3078, lr: 0.000100, took: 83.808s
[COOPERATIVE] Epoch 2, Batch 480/1563, loss: 3.260, reward: 34.843, critic_reward: 34.909, revenue_rate: 0.8615, distance: 10.9358, memory: 0.0172, power: 0.3343, lr: 0.000100, took: 92.849s
[COOPERATIVE] Epoch 2, Batch 490/1563, loss: 3.278, reward: 34.302, critic_reward: 34.084, revenue_rate: 0.8421, distance: 10.4660, memory: -0.0095, power: 0.3205, lr: 0.000100, took: 88.553s
[COOPERATIVE] Epoch 2, Batch 500/1563, loss: 3.736, reward: 33.612, critic_reward: 34.037, revenue_rate: 0.8271, distance: 10.2633, memory: -0.0181, power: 0.3110, lr: 0.000100, took: 85.709s
[COOPERATIVE] Epoch 2, Batch 510/1563, loss: 3.692, reward: 33.245, critic_reward: 33.119, revenue_rate: 0.8134, distance: 9.9994, memory: -0.0229, power: 0.3027, lr: 0.000100, took: 83.337s
[COOPERATIVE] Epoch 2, Batch 520/1563, loss: 3.766, reward: 31.903, critic_reward: 31.319, revenue_rate: 0.7804, distance: 9.4030, memory: -0.0380, power: 0.2852, lr: 0.000100, took: 77.784s
[COOPERATIVE] Epoch 2, Batch 530/1563, loss: 3.657, reward: 32.564, critic_reward: 32.150, revenue_rate: 0.7969, distance: 9.7333, memory: -0.0216, power: 0.2957, lr: 0.000100, took: 81.221s
[COOPERATIVE] Epoch 2, Batch 540/1563, loss: 3.757, reward: 33.741, critic_reward: 33.747, revenue_rate: 0.8287, distance: 10.2689, memory: -0.0128, power: 0.3119, lr: 0.000100, took: 85.368s
[COOPERATIVE] Epoch 2, Batch 550/1563, loss: 2.764, reward: 34.558, critic_reward: 34.677, revenue_rate: 0.8544, distance: 10.7084, memory: 0.0013, power: 0.3246, lr: 0.000100, took: 91.997s
[COOPERATIVE] Epoch 2, Batch 560/1563, loss: 3.316, reward: 33.622, critic_reward: 34.034, revenue_rate: 0.8253, distance: 10.3564, memory: -0.0148, power: 0.3120, lr: 0.000100, took: 86.116s
[COOPERATIVE] Epoch 2, Batch 570/1563, loss: 8.743, reward: 32.297, critic_reward: 32.345, revenue_rate: 0.7904, distance: 9.5062, memory: -0.0316, power: 0.2916, lr: 0.000100, took: 79.748s
[COOPERATIVE] Epoch 2, Batch 580/1563, loss: 5.643, reward: 31.369, critic_reward: 31.982, revenue_rate: 0.7674, distance: 9.1710, memory: -0.0428, power: 0.2810, lr: 0.000100, took: 75.773s
[COOPERATIVE] Epoch 2, Batch 590/1563, loss: 5.051, reward: 32.076, critic_reward: 31.212, revenue_rate: 0.7877, distance: 9.5554, memory: -0.0312, power: 0.2904, lr: 0.000100, took: 79.265s
[COOPERATIVE] Epoch 2, Batch 600/1563, loss: 5.433, reward: 33.029, critic_reward: 33.326, revenue_rate: 0.8108, distance: 10.0283, memory: -0.0140, power: 0.3019, lr: 0.000100, took: 83.323s
[COOPERATIVE] Epoch 2, Batch 610/1563, loss: 3.846, reward: 29.923, critic_reward: 30.308, revenue_rate: 0.7333, distance: 8.7581, memory: -0.0553, power: 0.2678, lr: 0.000100, took: 72.077s
[COOPERATIVE] Epoch 2, Batch 620/1563, loss: 4.801, reward: 29.537, critic_reward: 30.101, revenue_rate: 0.7212, distance: 8.5756, memory: -0.0622, power: 0.2618, lr: 0.000100, took: 70.620s
[COOPERATIVE] Epoch 2, Batch 630/1563, loss: 4.373, reward: 29.294, critic_reward: 29.195, revenue_rate: 0.7134, distance: 8.3225, memory: -0.0630, power: 0.2570, lr: 0.000100, took: 68.998s
[COOPERATIVE] Epoch 2, Batch 640/1563, loss: 8.874, reward: 28.369, critic_reward: 27.306, revenue_rate: 0.6916, distance: 8.1251, memory: -0.0822, power: 0.2485, lr: 0.000100, took: 66.400s
[COOPERATIVE] Epoch 2, Batch 650/1563, loss: 7.146, reward: 30.367, critic_reward: 31.800, revenue_rate: 0.7437, distance: 8.9012, memory: -0.0588, power: 0.2691, lr: 0.000100, took: 73.174s
[COOPERATIVE] Epoch 2, Batch 660/1563, loss: 5.582, reward: 31.417, critic_reward: 31.807, revenue_rate: 0.7715, distance: 9.2764, memory: -0.0482, power: 0.2826, lr: 0.000100, took: 78.611s
[COOPERATIVE] Epoch 2, Batch 670/1563, loss: 3.033, reward: 32.675, critic_reward: 33.007, revenue_rate: 0.8033, distance: 9.9384, memory: -0.0251, power: 0.3003, lr: 0.000100, took: 81.985s
[COOPERATIVE] Epoch 2, Batch 680/1563, loss: 3.120, reward: 33.116, critic_reward: 32.983, revenue_rate: 0.8134, distance: 10.1148, memory: -0.0204, power: 0.3109, lr: 0.000100, took: 84.374s
[COOPERATIVE] Epoch 2, Batch 690/1563, loss: 2.739, reward: 33.105, critic_reward: 33.508, revenue_rate: 0.8173, distance: 10.2503, memory: -0.0216, power: 0.3084, lr: 0.000100, took: 84.803s
[COOPERATIVE] Epoch 2, Batch 700/1563, loss: 5.181, reward: 32.851, critic_reward: 32.711, revenue_rate: 0.8053, distance: 9.9149, memory: -0.0203, power: 0.3025, lr: 0.000100, took: 82.883s
[COOPERATIVE] Epoch 2, Batch 710/1563, loss: 6.626, reward: 32.600, critic_reward: 32.304, revenue_rate: 0.7963, distance: 9.6665, memory: -0.0275, power: 0.2934, lr: 0.000100, took: 79.801s
[COOPERATIVE] Epoch 2, Batch 720/1563, loss: 2.820, reward: 31.886, critic_reward: 32.043, revenue_rate: 0.7831, distance: 9.5236, memory: -0.0380, power: 0.2894, lr: 0.000100, took: 79.151s
[COOPERATIVE] Epoch 2, Batch 730/1563, loss: 2.961, reward: 31.490, critic_reward: 31.486, revenue_rate: 0.7735, distance: 9.3866, memory: -0.0376, power: 0.2870, lr: 0.000100, took: 78.171s
[COOPERATIVE] Epoch 2, Batch 740/1563, loss: 2.773, reward: 29.561, critic_reward: 29.164, revenue_rate: 0.7231, distance: 8.5637, memory: -0.0638, power: 0.2652, lr: 0.000100, took: 70.771s
[COOPERATIVE] Epoch 2, Batch 750/1563, loss: 4.154, reward: 29.768, critic_reward: 30.111, revenue_rate: 0.7289, distance: 8.6983, memory: -0.0552, power: 0.2649, lr: 0.000100, took: 71.000s
[COOPERATIVE] Epoch 2, Batch 760/1563, loss: 5.801, reward: 31.477, critic_reward: 31.690, revenue_rate: 0.7684, distance: 9.2066, memory: -0.0483, power: 0.2814, lr: 0.000100, took: 76.379s
[COOPERATIVE] Epoch 2, Batch 770/1563, loss: 3.543, reward: 31.258, critic_reward: 31.248, revenue_rate: 0.7647, distance: 9.2085, memory: -0.0452, power: 0.2811, lr: 0.000100, took: 75.745s
[COOPERATIVE] Epoch 2, Batch 780/1563, loss: 2.877, reward: 30.224, critic_reward: 30.151, revenue_rate: 0.7402, distance: 8.8268, memory: -0.0602, power: 0.2702, lr: 0.000100, took: 74.576s
[COOPERATIVE] Epoch 2, Batch 790/1563, loss: 3.703, reward: 30.209, critic_reward: 29.981, revenue_rate: 0.7358, distance: 8.7893, memory: -0.0585, power: 0.2677, lr: 0.000100, took: 72.206s
[COOPERATIVE] Epoch 2, Batch 800/1563, loss: 8.091, reward: 31.051, critic_reward: 30.883, revenue_rate: 0.7588, distance: 9.0892, memory: -0.0501, power: 0.2776, lr: 0.000100, took: 75.273s
[COOPERATIVE] Epoch 2, Batch 810/1563, loss: 5.647, reward: 32.706, critic_reward: 32.565, revenue_rate: 0.8057, distance: 9.8699, memory: -0.0290, power: 0.2986, lr: 0.000100, took: 82.143s
[COOPERATIVE] Epoch 2, Batch 820/1563, loss: 3.423, reward: 33.483, critic_reward: 33.028, revenue_rate: 0.8244, distance: 10.1976, memory: -0.0263, power: 0.3067, lr: 0.000100, took: 84.561s
[COOPERATIVE] Epoch 2, Batch 830/1563, loss: 8.095, reward: 32.438, critic_reward: 34.171, revenue_rate: 0.7963, distance: 9.7990, memory: -0.0318, power: 0.2939, lr: 0.000100, took: 80.562s
[COOPERATIVE] Epoch 2, Batch 840/1563, loss: 6.951, reward: 32.420, critic_reward: 30.930, revenue_rate: 0.7946, distance: 9.8030, memory: -0.0271, power: 0.2957, lr: 0.000100, took: 80.936s
[COOPERATIVE] Epoch 2, Batch 850/1563, loss: 7.571, reward: 34.904, critic_reward: 33.722, revenue_rate: 0.8597, distance: 10.8492, memory: -0.0082, power: 0.3280, lr: 0.000100, took: 92.151s
[COOPERATIVE] Epoch 2, Batch 860/1563, loss: 7.108, reward: 34.241, critic_reward: 35.729, revenue_rate: 0.8431, distance: 10.5375, memory: -0.0019, power: 0.3204, lr: 0.000100, took: 88.240s
[COOPERATIVE] Epoch 2, Batch 870/1563, loss: 3.538, reward: 33.692, critic_reward: 33.317, revenue_rate: 0.8307, distance: 10.3456, memory: -0.0076, power: 0.3136, lr: 0.000100, took: 86.440s
[COOPERATIVE] Epoch 2, Batch 880/1563, loss: 2.728, reward: 34.203, critic_reward: 34.121, revenue_rate: 0.8388, distance: 10.5115, memory: -0.0013, power: 0.3178, lr: 0.000100, took: 88.308s
[COOPERATIVE] Epoch 2, Batch 890/1563, loss: 5.012, reward: 34.003, critic_reward: 33.261, revenue_rate: 0.8370, distance: 10.3950, memory: -0.0090, power: 0.3152, lr: 0.000100, took: 88.734s
[COOPERATIVE] Epoch 2, Batch 900/1563, loss: 3.865, reward: 31.794, critic_reward: 32.259, revenue_rate: 0.7812, distance: 9.4773, memory: -0.0458, power: 0.2845, lr: 0.000100, took: 78.332s
[COOPERATIVE] Epoch 2, Batch 910/1563, loss: 2.400, reward: 30.501, critic_reward: 30.350, revenue_rate: 0.7467, distance: 9.0023, memory: -0.0538, power: 0.2727, lr: 0.000100, took: 74.006s
[COOPERATIVE] Epoch 2, Batch 920/1563, loss: 3.812, reward: 31.235, critic_reward: 30.459, revenue_rate: 0.7671, distance: 9.2070, memory: -0.0441, power: 0.2797, lr: 0.000100, took: 76.011s
[COOPERATIVE] Epoch 2, Batch 930/1563, loss: 6.771, reward: 33.259, critic_reward: 34.861, revenue_rate: 0.8139, distance: 9.8732, memory: -0.0344, power: 0.3025, lr: 0.000100, took: 83.271s
[COOPERATIVE] Epoch 2, Batch 940/1563, loss: 2.987, reward: 32.154, critic_reward: 32.661, revenue_rate: 0.7891, distance: 9.5834, memory: -0.0277, power: 0.2899, lr: 0.000100, took: 78.851s
[COOPERATIVE] Epoch 2, Batch 950/1563, loss: 4.122, reward: 31.896, critic_reward: 31.957, revenue_rate: 0.7789, distance: 9.3607, memory: -0.0405, power: 0.2842, lr: 0.000100, took: 77.400s
[COOPERATIVE] Epoch 2, Batch 960/1563, loss: 5.702, reward: 31.130, critic_reward: 30.933, revenue_rate: 0.7668, distance: 9.2466, memory: -0.0399, power: 0.2806, lr: 0.000100, took: 76.340s
[COOPERATIVE] Epoch 2, Batch 970/1563, loss: 3.167, reward: 32.258, critic_reward: 32.553, revenue_rate: 0.7921, distance: 9.6016, memory: -0.0303, power: 0.2903, lr: 0.000100, took: 79.439s
[COOPERATIVE] Epoch 2, Batch 980/1563, loss: 2.093, reward: 33.477, critic_reward: 33.555, revenue_rate: 0.8216, distance: 10.0857, memory: -0.0150, power: 0.3068, lr: 0.000100, took: 83.770s
[COOPERATIVE] Epoch 2, Batch 990/1563, loss: 2.661, reward: 33.640, critic_reward: 33.104, revenue_rate: 0.8230, distance: 10.1254, memory: -0.0211, power: 0.3083, lr: 0.000100, took: 83.799s
[COOPERATIVE] Epoch 2, Batch 1000/1563, loss: 3.091, reward: 33.047, critic_reward: 33.153, revenue_rate: 0.8120, distance: 9.9566, memory: -0.0169, power: 0.3009, lr: 0.000100, took: 84.195s
[COOPERATIVE] Epoch 2, Batch 1010/1563, loss: 3.572, reward: 32.222, critic_reward: 31.826, revenue_rate: 0.7887, distance: 9.5022, memory: -0.0342, power: 0.2890, lr: 0.000100, took: 78.883s
[COOPERATIVE] Epoch 2, Batch 1020/1563, loss: 2.906, reward: 31.136, critic_reward: 31.238, revenue_rate: 0.7580, distance: 9.0008, memory: -0.0466, power: 0.2747, lr: 0.000100, took: 74.361s
[COOPERATIVE] Epoch 2, Batch 1030/1563, loss: 5.575, reward: 29.718, critic_reward: 28.545, revenue_rate: 0.7262, distance: 8.6196, memory: -0.0613, power: 0.2620, lr: 0.000100, took: 70.157s
[COOPERATIVE] Epoch 2, Batch 1040/1563, loss: 3.896, reward: 31.712, critic_reward: 32.393, revenue_rate: 0.7769, distance: 9.3616, memory: -0.0323, power: 0.2843, lr: 0.000100, took: 77.693s
[COOPERATIVE] Epoch 2, Batch 1050/1563, loss: 3.007, reward: 32.607, critic_reward: 32.483, revenue_rate: 0.7944, distance: 9.5596, memory: -0.0312, power: 0.2922, lr: 0.000100, took: 78.908s
[COOPERATIVE] Epoch 2, Batch 1060/1563, loss: 2.335, reward: 31.985, critic_reward: 31.611, revenue_rate: 0.7830, distance: 9.3569, memory: -0.0408, power: 0.2863, lr: 0.000100, took: 77.582s
[COOPERATIVE] Epoch 2, Batch 1070/1563, loss: 5.253, reward: 30.770, critic_reward: 31.424, revenue_rate: 0.7499, distance: 8.9647, memory: -0.0617, power: 0.2726, lr: 0.000100, took: 72.752s
[COOPERATIVE] Epoch 2, Batch 1080/1563, loss: 2.338, reward: 29.121, critic_reward: 29.314, revenue_rate: 0.7105, distance: 8.4230, memory: -0.0638, power: 0.2562, lr: 0.000100, took: 68.960s
[COOPERATIVE] Epoch 2, Batch 1090/1563, loss: 3.141, reward: 30.870, critic_reward: 31.252, revenue_rate: 0.7554, distance: 9.0852, memory: -0.0505, power: 0.2756, lr: 0.000100, took: 74.573s
[COOPERATIVE] Epoch 2, Batch 1100/1563, loss: 3.263, reward: 31.721, critic_reward: 31.553, revenue_rate: 0.7802, distance: 9.4313, memory: -0.0418, power: 0.2856, lr: 0.000100, took: 78.191s
[COOPERATIVE] Epoch 2, Batch 1110/1563, loss: 4.433, reward: 30.229, critic_reward: 30.555, revenue_rate: 0.7361, distance: 8.7612, memory: -0.0509, power: 0.2658, lr: 0.000100, took: 71.755s
[COOPERATIVE] Epoch 2, Batch 1120/1563, loss: 8.560, reward: 30.730, critic_reward: 31.214, revenue_rate: 0.7520, distance: 8.9196, memory: -0.0549, power: 0.2715, lr: 0.000100, took: 75.406s
[COOPERATIVE] Epoch 2, Batch 1130/1563, loss: 2.978, reward: 30.136, critic_reward: 30.547, revenue_rate: 0.7376, distance: 8.7942, memory: -0.0616, power: 0.2665, lr: 0.000100, took: 72.090s
[COOPERATIVE] Epoch 2, Batch 1140/1563, loss: 3.507, reward: 28.211, critic_reward: 27.523, revenue_rate: 0.6873, distance: 8.0905, memory: -0.0833, power: 0.2437, lr: 0.000100, took: 65.413s
[COOPERATIVE] Epoch 2, Batch 1150/1563, loss: 3.395, reward: 30.056, critic_reward: 30.210, revenue_rate: 0.7349, distance: 8.7627, memory: -0.0573, power: 0.2652, lr: 0.000100, took: 71.260s
[COOPERATIVE] Epoch 2, Batch 1160/1563, loss: 3.005, reward: 30.977, critic_reward: 30.684, revenue_rate: 0.7564, distance: 9.0241, memory: -0.0518, power: 0.2747, lr: 0.000100, took: 74.187s
[COOPERATIVE] Epoch 2, Batch 1170/1563, loss: 2.411, reward: 31.508, critic_reward: 31.123, revenue_rate: 0.7698, distance: 9.2639, memory: -0.0412, power: 0.2820, lr: 0.000100, took: 75.744s
[COOPERATIVE] Epoch 2, Batch 1180/1563, loss: 3.207, reward: 29.503, critic_reward: 29.580, revenue_rate: 0.7178, distance: 8.4315, memory: -0.0632, power: 0.2577, lr: 0.000100, took: 69.304s
[COOPERATIVE] Epoch 2, Batch 1190/1563, loss: 4.208, reward: 28.657, critic_reward: 28.667, revenue_rate: 0.6993, distance: 8.2129, memory: -0.0681, power: 0.2503, lr: 0.000100, took: 66.892s
[COOPERATIVE] Epoch 2, Batch 1200/1563, loss: 2.677, reward: 29.771, critic_reward: 30.268, revenue_rate: 0.7268, distance: 8.6345, memory: -0.0651, power: 0.2613, lr: 0.000100, took: 70.687s
[COOPERATIVE] Epoch 2, Batch 1210/1563, loss: 3.059, reward: 30.920, critic_reward: 31.109, revenue_rate: 0.7581, distance: 9.2230, memory: -0.0413, power: 0.2788, lr: 0.000100, took: 75.866s
[COOPERATIVE] Epoch 2, Batch 1220/1563, loss: 3.743, reward: 32.089, critic_reward: 32.028, revenue_rate: 0.7878, distance: 9.6833, memory: -0.0338, power: 0.2949, lr: 0.000100, took: 79.928s
[COOPERATIVE] Epoch 2, Batch 1230/1563, loss: 2.912, reward: 31.257, critic_reward: 31.309, revenue_rate: 0.7670, distance: 9.3124, memory: -0.0406, power: 0.2813, lr: 0.000100, took: 76.776s
[COOPERATIVE] Epoch 2, Batch 1240/1563, loss: 3.033, reward: 32.143, critic_reward: 31.890, revenue_rate: 0.7900, distance: 9.6322, memory: -0.0304, power: 0.2927, lr: 0.000100, took: 81.383s
[COOPERATIVE] Epoch 2, Batch 1250/1563, loss: 7.431, reward: 33.696, critic_reward: 33.103, revenue_rate: 0.8309, distance: 10.3714, memory: -0.0152, power: 0.3127, lr: 0.000100, took: 86.302s
[COOPERATIVE] Epoch 2, Batch 1260/1563, loss: 4.985, reward: 35.468, critic_reward: 36.086, revenue_rate: 0.8750, distance: 11.0492, memory: 0.0138, power: 0.3359, lr: 0.000100, took: 94.553s
[COOPERATIVE] Epoch 2, Batch 1270/1563, loss: 4.320, reward: 33.705, critic_reward: 32.812, revenue_rate: 0.8304, distance: 10.3819, memory: -0.0050, power: 0.3126, lr: 0.000100, took: 87.110s
[COOPERATIVE] Epoch 2, Batch 1280/1563, loss: 2.820, reward: 34.374, critic_reward: 34.457, revenue_rate: 0.8504, distance: 10.7232, memory: 0.0027, power: 0.3213, lr: 0.000100, took: 90.392s
[COOPERATIVE] Epoch 2, Batch 1290/1563, loss: 3.111, reward: 34.472, critic_reward: 34.888, revenue_rate: 0.8506, distance: 10.6858, memory: -0.0029, power: 0.3252, lr: 0.000100, took: 89.943s
[COOPERATIVE] Epoch 2, Batch 1300/1563, loss: 4.999, reward: 34.564, critic_reward: 33.403, revenue_rate: 0.8513, distance: 10.7625, memory: 0.0061, power: 0.3263, lr: 0.000100, took: 90.653s
[COOPERATIVE] Epoch 2, Batch 1310/1563, loss: 2.875, reward: 34.686, critic_reward: 34.520, revenue_rate: 0.8566, distance: 10.8502, memory: 0.0036, power: 0.3294, lr: 0.000100, took: 91.684s
[COOPERATIVE] Epoch 2, Batch 1320/1563, loss: 2.485, reward: 33.782, critic_reward: 33.706, revenue_rate: 0.8348, distance: 10.6212, memory: -0.0020, power: 0.3188, lr: 0.000100, took: 88.237s
[COOPERATIVE] Epoch 2, Batch 1330/1563, loss: 2.880, reward: 33.243, critic_reward: 33.516, revenue_rate: 0.8188, distance: 10.1728, memory: -0.0146, power: 0.3074, lr: 0.000100, took: 84.707s
[COOPERATIVE] Epoch 2, Batch 1340/1563, loss: 4.123, reward: 33.008, critic_reward: 32.177, revenue_rate: 0.8112, distance: 9.9945, memory: -0.0149, power: 0.3021, lr: 0.000100, took: 84.796s
[COOPERATIVE] Epoch 2, Batch 1350/1563, loss: 3.812, reward: 34.719, critic_reward: 35.005, revenue_rate: 0.8577, distance: 10.7166, memory: 0.0012, power: 0.3247, lr: 0.000100, took: 90.232s
[COOPERATIVE] Epoch 2, Batch 1360/1563, loss: 3.955, reward: 35.287, critic_reward: 35.761, revenue_rate: 0.8774, distance: 11.2711, memory: 0.0104, power: 0.3423, lr: 0.000100, took: 95.200s
[COOPERATIVE] Epoch 2, Batch 1370/1563, loss: 3.089, reward: 35.541, critic_reward: 35.775, revenue_rate: 0.8811, distance: 11.2379, memory: 0.0215, power: 0.3408, lr: 0.000100, took: 95.517s
[COOPERATIVE] Epoch 2, Batch 1380/1563, loss: 2.192, reward: 33.216, critic_reward: 33.358, revenue_rate: 0.8156, distance: 9.9855, memory: -0.0200, power: 0.3032, lr: 0.000100, took: 83.243s
[COOPERATIVE] Epoch 2, Batch 1390/1563, loss: 6.424, reward: 30.968, critic_reward: 31.465, revenue_rate: 0.7561, distance: 9.1198, memory: -0.0586, power: 0.2765, lr: 0.000100, took: 74.404s
[COOPERATIVE] Epoch 2, Batch 1400/1563, loss: 5.702, reward: 32.779, critic_reward: 31.821, revenue_rate: 0.8039, distance: 9.8395, memory: -0.0304, power: 0.2980, lr: 0.000100, took: 81.494s
[COOPERATIVE] Epoch 2, Batch 1410/1563, loss: 4.807, reward: 35.100, critic_reward: 36.328, revenue_rate: 0.8689, distance: 11.0300, memory: -0.0003, power: 0.3339, lr: 0.000100, took: 92.355s
[COOPERATIVE] Epoch 2, Batch 1420/1563, loss: 2.949, reward: 35.831, critic_reward: 35.413, revenue_rate: 0.8864, distance: 11.3939, memory: 0.0168, power: 0.3445, lr: 0.000100, took: 96.800s
[COOPERATIVE] Epoch 2, Batch 1430/1563, loss: 3.262, reward: 34.718, critic_reward: 34.803, revenue_rate: 0.8571, distance: 10.7854, memory: 0.0034, power: 0.3270, lr: 0.000100, took: 90.557s
[COOPERATIVE] Epoch 2, Batch 1440/1563, loss: 2.679, reward: 34.261, critic_reward: 34.487, revenue_rate: 0.8405, distance: 10.4284, memory: -0.0101, power: 0.3139, lr: 0.000100, took: 88.214s
[COOPERATIVE] Epoch 2, Batch 1450/1563, loss: 3.912, reward: 34.225, critic_reward: 34.995, revenue_rate: 0.8363, distance: 10.3792, memory: -0.0166, power: 0.3161, lr: 0.000100, took: 86.235s
[COOPERATIVE] Epoch 2, Batch 1460/1563, loss: 6.964, reward: 32.520, critic_reward: 31.369, revenue_rate: 0.7972, distance: 9.7787, memory: -0.0315, power: 0.2949, lr: 0.000100, took: 80.351s
[COOPERATIVE] Epoch 2, Batch 1470/1563, loss: 3.136, reward: 31.386, critic_reward: 31.217, revenue_rate: 0.7671, distance: 9.1904, memory: -0.0507, power: 0.2799, lr: 0.000100, took: 75.806s
[COOPERATIVE] Epoch 2, Batch 1480/1563, loss: 2.952, reward: 31.693, critic_reward: 32.293, revenue_rate: 0.7757, distance: 9.2529, memory: -0.0455, power: 0.2833, lr: 0.000100, took: 76.746s
[COOPERATIVE] Epoch 2, Batch 1490/1563, loss: 2.821, reward: 31.762, critic_reward: 32.164, revenue_rate: 0.7780, distance: 9.3490, memory: -0.0429, power: 0.2857, lr: 0.000100, took: 77.383s
[COOPERATIVE] Epoch 2, Batch 1500/1563, loss: 2.620, reward: 32.228, critic_reward: 32.329, revenue_rate: 0.7901, distance: 9.6239, memory: -0.0346, power: 0.2904, lr: 0.000100, took: 80.030s
[COOPERATIVE] Epoch 2, Batch 1510/1563, loss: 4.794, reward: 32.832, critic_reward: 32.917, revenue_rate: 0.8023, distance: 9.7482, memory: -0.0322, power: 0.2975, lr: 0.000100, took: 80.948s
[COOPERATIVE] Epoch 2, Batch 1520/1563, loss: 7.457, reward: 32.888, critic_reward: 32.588, revenue_rate: 0.8063, distance: 9.9139, memory: -0.0182, power: 0.2995, lr: 0.000100, took: 81.959s
[COOPERATIVE] Epoch 2, Batch 1530/1563, loss: 4.226, reward: 33.544, critic_reward: 33.855, revenue_rate: 0.8232, distance: 10.0961, memory: -0.0252, power: 0.3063, lr: 0.000100, took: 83.880s
[COOPERATIVE] Epoch 2, Batch 1540/1563, loss: 7.699, reward: 34.256, critic_reward: 34.158, revenue_rate: 0.8423, distance: 10.3790, memory: -0.0102, power: 0.3175, lr: 0.000100, took: 87.085s
[COOPERATIVE] Epoch 2, Batch 1550/1563, loss: 6.316, reward: 32.822, critic_reward: 33.812, revenue_rate: 0.8046, distance: 9.8446, memory: -0.0230, power: 0.2969, lr: 0.000100, took: 81.172s
[COOPERATIVE] Epoch 2, Batch 1560/1563, loss: 10.892, reward: 30.917, critic_reward: 33.024, revenue_rate: 0.7548, distance: 9.0704, memory: -0.0457, power: 0.2763, lr: 0.000100, took: 76.319s
[COOPERATIVE] 开始验证...
[COOPERATIVE] 验证完成 - Epoch 2, reward: 29.966, revenue_rate: 0.7312, distance: 8.6163, memory: -0.0618, power: 0.2621
[COOPERATIVE] 开始训练 Epoch 3/3
[COOPERATIVE] Epoch 3, Batch 10/1563, loss: 5.190, reward: 29.380, critic_reward: 28.480, revenue_rate: 0.7167, distance: 8.4087, memory: -0.0617, power: 0.2563, lr: 0.000100, took: 73.168s
[COOPERATIVE] Epoch 3, Batch 20/1563, loss: 3.894, reward: 29.802, critic_reward: 30.127, revenue_rate: 0.7300, distance: 8.7543, memory: -0.0629, power: 0.2646, lr: 0.000100, took: 71.373s
[COOPERATIVE] Epoch 3, Batch 30/1563, loss: 7.616, reward: 31.975, critic_reward: 31.915, revenue_rate: 0.7856, distance: 9.4961, memory: -0.0287, power: 0.2896, lr: 0.000100, took: 78.662s
[COOPERATIVE] Epoch 3, Batch 40/1563, loss: 5.157, reward: 33.655, critic_reward: 33.613, revenue_rate: 0.8293, distance: 10.2377, memory: -0.0140, power: 0.3111, lr: 0.000100, took: 85.145s
[COOPERATIVE] Epoch 3, Batch 50/1563, loss: 7.169, reward: 33.314, critic_reward: 33.511, revenue_rate: 0.8157, distance: 10.0770, memory: -0.0189, power: 0.3046, lr: 0.000100, took: 84.019s
[COOPERATIVE] Epoch 3, Batch 60/1563, loss: 5.550, reward: 33.099, critic_reward: 33.394, revenue_rate: 0.8097, distance: 9.8574, memory: -0.0177, power: 0.2997, lr: 0.000100, took: 82.380s
[COOPERATIVE] Epoch 3, Batch 70/1563, loss: 3.118, reward: 34.021, critic_reward: 33.626, revenue_rate: 0.8351, distance: 10.2939, memory: -0.0048, power: 0.3121, lr: 0.000100, took: 87.393s
[COOPERATIVE] Epoch 3, Batch 80/1563, loss: 2.465, reward: 33.494, critic_reward: 33.689, revenue_rate: 0.8235, distance: 10.1507, memory: -0.0121, power: 0.3059, lr: 0.000100, took: 87.356s
[COOPERATIVE] Epoch 3, Batch 90/1563, loss: 2.101, reward: 32.726, critic_reward: 32.486, revenue_rate: 0.8027, distance: 9.7955, memory: -0.0307, power: 0.2971, lr: 0.000100, took: 80.303s
[COOPERATIVE] Epoch 3, Batch 100/1563, loss: 3.240, reward: 31.707, critic_reward: 31.090, revenue_rate: 0.7763, distance: 9.3430, memory: -0.0460, power: 0.2831, lr: 0.000100, took: 76.986s
[COOPERATIVE] Epoch 3, Batch 110/1563, loss: 3.884, reward: 32.603, critic_reward: 33.024, revenue_rate: 0.7990, distance: 9.6927, memory: -0.0362, power: 0.2962, lr: 0.000100, took: 81.040s
[COOPERATIVE] Epoch 3, Batch 120/1563, loss: 2.628, reward: 33.712, critic_reward: 33.820, revenue_rate: 0.8274, distance: 10.1548, memory: -0.0170, power: 0.3095, lr: 0.000100, took: 84.707s
[COOPERATIVE] Epoch 3, Batch 130/1563, loss: 2.828, reward: 35.171, critic_reward: 35.214, revenue_rate: 0.8670, distance: 10.9440, memory: 0.0129, power: 0.3319, lr: 0.000100, took: 92.285s
[COOPERATIVE] Epoch 3, Batch 140/1563, loss: 2.435, reward: 36.632, critic_reward: 36.845, revenue_rate: 0.9024, distance: 11.5726, memory: 0.0297, power: 0.3516, lr: 0.000100, took: 99.207s
[COOPERATIVE] Epoch 3, Batch 150/1563, loss: 3.903, reward: 35.241, critic_reward: 34.872, revenue_rate: 0.8684, distance: 10.9610, memory: 0.0067, power: 0.3324, lr: 0.000100, took: 92.845s
[COOPERATIVE] Epoch 3, Batch 160/1563, loss: 3.618, reward: 32.927, critic_reward: 32.295, revenue_rate: 0.8087, distance: 9.8844, memory: -0.0272, power: 0.2988, lr: 0.000100, took: 81.734s
[COOPERATIVE] Epoch 3, Batch 170/1563, loss: 3.796, reward: 32.867, critic_reward: 32.704, revenue_rate: 0.8035, distance: 9.8120, memory: -0.0352, power: 0.2959, lr: 0.000100, took: 80.617s
[COOPERATIVE] Epoch 3, Batch 180/1563, loss: 2.911, reward: 33.365, critic_reward: 33.350, revenue_rate: 0.8183, distance: 10.0611, memory: -0.0238, power: 0.3051, lr: 0.000100, took: 83.178s
[COOPERATIVE] Epoch 3, Batch 190/1563, loss: 2.759, reward: 33.005, critic_reward: 33.204, revenue_rate: 0.8090, distance: 9.8594, memory: -0.0289, power: 0.3005, lr: 0.000100, took: 84.192s
[COOPERATIVE] Epoch 3, Batch 200/1563, loss: 2.257, reward: 31.747, critic_reward: 31.797, revenue_rate: 0.7746, distance: 9.2690, memory: -0.0461, power: 0.2828, lr: 0.000100, took: 76.736s
[COOPERATIVE] Epoch 3, Batch 210/1563, loss: 2.773, reward: 30.795, critic_reward: 30.632, revenue_rate: 0.7522, distance: 8.9527, memory: -0.0525, power: 0.2723, lr: 0.000100, took: 73.549s
[COOPERATIVE] Epoch 3, Batch 220/1563, loss: 2.195, reward: 31.192, critic_reward: 30.898, revenue_rate: 0.7601, distance: 9.1403, memory: -0.0448, power: 0.2769, lr: 0.000100, took: 74.984s
[COOPERATIVE] Epoch 3, Batch 230/1563, loss: 3.172, reward: 31.676, critic_reward: 31.638, revenue_rate: 0.7766, distance: 9.3870, memory: -0.0340, power: 0.2836, lr: 0.000100, took: 77.363s
[COOPERATIVE] Epoch 3, Batch 240/1563, loss: 2.335, reward: 30.861, critic_reward: 30.618, revenue_rate: 0.7512, distance: 8.9208, memory: -0.0621, power: 0.2720, lr: 0.000100, took: 73.751s
[COOPERATIVE] Epoch 3, Batch 250/1563, loss: 3.055, reward: 32.115, critic_reward: 31.983, revenue_rate: 0.7806, distance: 9.3771, memory: -0.0461, power: 0.2836, lr: 0.000100, took: 77.432s
[COOPERATIVE] Epoch 3, Batch 260/1563, loss: 4.444, reward: 32.844, critic_reward: 33.586, revenue_rate: 0.8059, distance: 9.7962, memory: -0.0223, power: 0.2961, lr: 0.000100, took: 81.382s
[COOPERATIVE] Epoch 3, Batch 270/1563, loss: 3.649, reward: 31.776, critic_reward: 30.968, revenue_rate: 0.7780, distance: 9.4235, memory: -0.0316, power: 0.2854, lr: 0.000100, took: 78.135s
[COOPERATIVE] Epoch 3, Batch 280/1563, loss: 3.337, reward: 31.109, critic_reward: 31.586, revenue_rate: 0.7601, distance: 9.0417, memory: -0.0477, power: 0.2733, lr: 0.000100, took: 75.153s
[COOPERATIVE] Epoch 3, Batch 290/1563, loss: 2.921, reward: 30.782, critic_reward: 30.388, revenue_rate: 0.7546, distance: 8.9755, memory: -0.0579, power: 0.2732, lr: 0.000100, took: 73.848s
[COOPERATIVE] Epoch 3, Batch 300/1563, loss: 2.545, reward: 31.334, critic_reward: 31.410, revenue_rate: 0.7643, distance: 9.0891, memory: -0.0528, power: 0.2798, lr: 0.000100, took: 74.947s
[COOPERATIVE] Epoch 3, Batch 310/1563, loss: 2.682, reward: 32.338, critic_reward: 31.812, revenue_rate: 0.7946, distance: 9.5012, memory: -0.0386, power: 0.2920, lr: 0.000100, took: 81.766s
[COOPERATIVE] Epoch 3, Batch 320/1563, loss: 3.534, reward: 31.260, critic_reward: 31.704, revenue_rate: 0.7628, distance: 9.1354, memory: -0.0513, power: 0.2786, lr: 0.000100, took: 74.788s
[COOPERATIVE] Epoch 3, Batch 330/1563, loss: 2.936, reward: 31.822, critic_reward: 31.416, revenue_rate: 0.7786, distance: 9.3724, memory: -0.0454, power: 0.2840, lr: 0.000100, took: 77.169s
[COOPERATIVE] Epoch 3, Batch 340/1563, loss: 3.180, reward: 31.535, critic_reward: 31.774, revenue_rate: 0.7740, distance: 9.3456, memory: -0.0334, power: 0.2821, lr: 0.000100, took: 77.373s
[COOPERATIVE] Epoch 3, Batch 350/1563, loss: 7.825, reward: 33.413, critic_reward: 32.601, revenue_rate: 0.8220, distance: 10.0751, memory: -0.0224, power: 0.3051, lr: 0.000100, took: 83.991s
[COOPERATIVE] Epoch 3, Batch 360/1563, loss: 5.742, reward: 31.964, critic_reward: 32.049, revenue_rate: 0.7802, distance: 9.3984, memory: -0.0441, power: 0.2855, lr: 0.000100, took: 77.340s
[COOPERATIVE] Epoch 3, Batch 370/1563, loss: 3.892, reward: 31.130, critic_reward: 31.175, revenue_rate: 0.7596, distance: 9.0951, memory: -0.0483, power: 0.2763, lr: 0.000100, took: 74.639s
[COOPERATIVE] Epoch 3, Batch 380/1563, loss: 3.481, reward: 33.260, critic_reward: 32.912, revenue_rate: 0.8198, distance: 10.0663, memory: -0.0173, power: 0.3049, lr: 0.000100, took: 83.353s
[COOPERATIVE] Epoch 3, Batch 390/1563, loss: 4.909, reward: 34.741, critic_reward: 35.243, revenue_rate: 0.8587, distance: 10.7839, memory: -0.0019, power: 0.3282, lr: 0.000100, took: 91.067s
[COOPERATIVE] Epoch 3, Batch 400/1563, loss: 3.166, reward: 33.827, critic_reward: 34.116, revenue_rate: 0.8338, distance: 10.3577, memory: -0.0135, power: 0.3171, lr: 0.000100, took: 86.700s
[COOPERATIVE] Epoch 3, Batch 410/1563, loss: 3.385, reward: 32.867, critic_reward: 31.997, revenue_rate: 0.8100, distance: 10.0747, memory: -0.0262, power: 0.3032, lr: 0.000100, took: 83.174s
[COOPERATIVE] Epoch 3, Batch 420/1563, loss: 3.083, reward: 32.557, critic_reward: 33.275, revenue_rate: 0.7970, distance: 9.7070, memory: -0.0304, power: 0.2949, lr: 0.000100, took: 82.530s
[COOPERATIVE] Epoch 3, Batch 430/1563, loss: 3.146, reward: 32.420, critic_reward: 32.180, revenue_rate: 0.7929, distance: 9.6122, memory: -0.0452, power: 0.2927, lr: 0.000100, took: 79.762s
[COOPERATIVE] Epoch 3, Batch 440/1563, loss: 2.662, reward: 33.365, critic_reward: 33.631, revenue_rate: 0.8168, distance: 10.0752, memory: -0.0185, power: 0.3059, lr: 0.000100, took: 84.206s
[COOPERATIVE] Epoch 3, Batch 450/1563, loss: 3.522, reward: 34.354, critic_reward: 34.388, revenue_rate: 0.8465, distance: 10.4919, memory: -0.0065, power: 0.3205, lr: 0.000100, took: 88.477s
[COOPERATIVE] Epoch 3, Batch 460/1563, loss: 3.112, reward: 32.869, critic_reward: 33.407, revenue_rate: 0.8061, distance: 9.8536, memory: -0.0239, power: 0.3004, lr: 0.000100, took: 82.100s
[COOPERATIVE] Epoch 3, Batch 470/1563, loss: 3.737, reward: 30.432, critic_reward: 29.773, revenue_rate: 0.7463, distance: 8.9106, memory: -0.0553, power: 0.2726, lr: 0.000100, took: 73.707s
[COOPERATIVE] Epoch 3, Batch 480/1563, loss: 2.928, reward: 30.425, critic_reward: 30.190, revenue_rate: 0.7404, distance: 8.7787, memory: -0.0590, power: 0.2693, lr: 0.000100, took: 72.246s
[COOPERATIVE] Epoch 3, Batch 490/1563, loss: 2.485, reward: 31.876, critic_reward: 31.894, revenue_rate: 0.7797, distance: 9.3876, memory: -0.0385, power: 0.2839, lr: 0.000100, took: 77.558s
[COOPERATIVE] Epoch 3, Batch 500/1563, loss: 2.701, reward: 32.557, critic_reward: 32.637, revenue_rate: 0.7983, distance: 9.6164, memory: -0.0334, power: 0.2933, lr: 0.000100, took: 80.186s
[COOPERATIVE] Epoch 3, Batch 510/1563, loss: 2.302, reward: 32.510, critic_reward: 32.825, revenue_rate: 0.7949, distance: 9.5278, memory: -0.0353, power: 0.2918, lr: 0.000100, took: 79.737s
[COOPERATIVE] Epoch 3, Batch 520/1563, loss: 2.252, reward: 32.466, critic_reward: 32.355, revenue_rate: 0.7947, distance: 9.6067, memory: -0.0362, power: 0.2915, lr: 0.000100, took: 79.964s
[COOPERATIVE] Epoch 3, Batch 530/1563, loss: 2.819, reward: 32.268, critic_reward: 32.787, revenue_rate: 0.7912, distance: 9.5829, memory: -0.0262, power: 0.2902, lr: 0.000100, took: 80.620s
[COOPERATIVE] Epoch 3, Batch 540/1563, loss: 3.522, reward: 31.879, critic_reward: 31.160, revenue_rate: 0.7794, distance: 9.3098, memory: -0.0423, power: 0.2835, lr: 0.000100, took: 77.297s
[COOPERATIVE] Epoch 3, Batch 550/1563, loss: 3.144, reward: 33.099, critic_reward: 33.652, revenue_rate: 0.8122, distance: 9.9971, memory: -0.0288, power: 0.3024, lr: 0.000100, took: 82.639s
[COOPERATIVE] Epoch 3, Batch 560/1563, loss: 3.910, reward: 33.016, critic_reward: 32.550, revenue_rate: 0.8114, distance: 9.9047, memory: -0.0180, power: 0.3006, lr: 0.000100, took: 82.387s
[COOPERATIVE] Epoch 3, Batch 570/1563, loss: 2.934, reward: 33.788, critic_reward: 33.471, revenue_rate: 0.8284, distance: 10.1105, memory: -0.0094, power: 0.3073, lr: 0.000100, took: 84.787s
[COOPERATIVE] Epoch 3, Batch 580/1563, loss: 3.652, reward: 32.737, critic_reward: 32.931, revenue_rate: 0.8008, distance: 9.7029, memory: -0.0284, power: 0.2951, lr: 0.000100, took: 80.182s
[COOPERATIVE] Epoch 3, Batch 590/1563, loss: 5.069, reward: 32.699, critic_reward: 32.788, revenue_rate: 0.8026, distance: 9.6684, memory: -0.0366, power: 0.2962, lr: 0.000100, took: 80.927s
[COOPERATIVE] Epoch 3, Batch 600/1563, loss: 5.154, reward: 33.230, critic_reward: 31.856, revenue_rate: 0.8194, distance: 10.0950, memory: -0.0181, power: 0.3044, lr: 0.000100, took: 83.814s
[COOPERATIVE] Epoch 3, Batch 610/1563, loss: 7.979, reward: 33.058, critic_reward: 34.976, revenue_rate: 0.8117, distance: 9.8995, memory: -0.0360, power: 0.2987, lr: 0.000100, took: 81.697s
[COOPERATIVE] Epoch 3, Batch 620/1563, loss: 3.532, reward: 31.846, critic_reward: 31.618, revenue_rate: 0.7815, distance: 9.3584, memory: -0.0353, power: 0.2846, lr: 0.000100, took: 77.462s
[COOPERATIVE] Epoch 3, Batch 630/1563, loss: 4.233, reward: 31.414, critic_reward: 31.034, revenue_rate: 0.7681, distance: 9.1531, memory: -0.0384, power: 0.2795, lr: 0.000100, took: 75.879s
[COOPERATIVE] Epoch 3, Batch 640/1563, loss: 4.837, reward: 31.560, critic_reward: 31.989, revenue_rate: 0.7752, distance: 9.3149, memory: -0.0430, power: 0.2822, lr: 0.000100, took: 78.630s
[COOPERATIVE] Epoch 3, Batch 650/1563, loss: 3.372, reward: 32.403, critic_reward: 31.799, revenue_rate: 0.7952, distance: 9.4910, memory: -0.0329, power: 0.2897, lr: 0.000100, took: 79.553s
[COOPERATIVE] Epoch 3, Batch 660/1563, loss: 2.578, reward: 32.477, critic_reward: 32.030, revenue_rate: 0.7949, distance: 9.5727, memory: -0.0351, power: 0.2914, lr: 0.000100, took: 79.627s
[COOPERATIVE] Epoch 3, Batch 670/1563, loss: 4.893, reward: 32.149, critic_reward: 33.294, revenue_rate: 0.7881, distance: 9.4631, memory: -0.0332, power: 0.2898, lr: 0.000100, took: 78.738s
[COOPERATIVE] Epoch 3, Batch 680/1563, loss: 3.130, reward: 31.121, critic_reward: 31.116, revenue_rate: 0.7602, distance: 9.0652, memory: -0.0547, power: 0.2751, lr: 0.000100, took: 75.158s
[COOPERATIVE] Epoch 3, Batch 690/1563, loss: 2.049, reward: 29.896, critic_reward: 29.875, revenue_rate: 0.7301, distance: 8.6747, memory: -0.0622, power: 0.2631, lr: 0.000100, took: 70.955s
[COOPERATIVE] Epoch 3, Batch 700/1563, loss: 2.957, reward: 30.789, critic_reward: 31.055, revenue_rate: 0.7502, distance: 8.9069, memory: -0.0650, power: 0.2725, lr: 0.000100, took: 73.441s
[COOPERATIVE] Epoch 3, Batch 710/1563, loss: 2.658, reward: 32.242, critic_reward: 32.536, revenue_rate: 0.7905, distance: 9.6544, memory: -0.0305, power: 0.2910, lr: 0.000100, took: 79.574s
[COOPERATIVE] Epoch 3, Batch 720/1563, loss: 4.271, reward: 32.673, critic_reward: 31.728, revenue_rate: 0.7974, distance: 9.6977, memory: -0.0339, power: 0.2937, lr: 0.000100, took: 80.565s
[COOPERATIVE] Epoch 3, Batch 730/1563, loss: 2.701, reward: 32.025, critic_reward: 32.148, revenue_rate: 0.7836, distance: 9.4183, memory: -0.0400, power: 0.2847, lr: 0.000100, took: 78.141s
[COOPERATIVE] Epoch 3, Batch 740/1563, loss: 3.723, reward: 32.014, critic_reward: 31.432, revenue_rate: 0.7847, distance: 9.4806, memory: -0.0365, power: 0.2884, lr: 0.000100, took: 78.183s
[COOPERATIVE] Epoch 3, Batch 750/1563, loss: 1.810, reward: 31.924, critic_reward: 32.098, revenue_rate: 0.7817, distance: 9.4119, memory: -0.0281, power: 0.2865, lr: 0.000100, took: 77.346s
[COOPERATIVE] Epoch 3, Batch 760/1563, loss: 3.070, reward: 30.005, critic_reward: 29.781, revenue_rate: 0.7334, distance: 8.7164, memory: -0.0563, power: 0.2657, lr: 0.000100, took: 73.608s
[COOPERATIVE] Epoch 3, Batch 770/1563, loss: 2.840, reward: 30.070, critic_reward: 29.508, revenue_rate: 0.7319, distance: 8.7135, memory: -0.0652, power: 0.2642, lr: 0.000100, took: 70.784s
[COOPERATIVE] Epoch 3, Batch 780/1563, loss: 6.797, reward: 32.592, critic_reward: 34.095, revenue_rate: 0.7973, distance: 9.7445, memory: -0.0316, power: 0.2967, lr: 0.000100, took: 80.441s
[COOPERATIVE] Epoch 3, Batch 790/1563, loss: 5.631, reward: 33.771, critic_reward: 33.066, revenue_rate: 0.8281, distance: 10.2007, memory: -0.0179, power: 0.3092, lr: 0.000100, took: 85.393s
[COOPERATIVE] Epoch 3, Batch 800/1563, loss: 3.966, reward: 33.711, critic_reward: 34.781, revenue_rate: 0.8290, distance: 10.1664, memory: -0.0127, power: 0.3112, lr: 0.000100, took: 85.505s
[COOPERATIVE] Epoch 3, Batch 810/1563, loss: 3.919, reward: 33.404, critic_reward: 33.247, revenue_rate: 0.8214, distance: 10.1619, memory: -0.0084, power: 0.3088, lr: 0.000100, took: 84.249s
[COOPERATIVE] Epoch 3, Batch 820/1563, loss: 1.779, reward: 33.202, critic_reward: 33.203, revenue_rate: 0.8130, distance: 9.9679, memory: -0.0183, power: 0.3028, lr: 0.000100, took: 83.360s
[COOPERATIVE] Epoch 3, Batch 830/1563, loss: 2.162, reward: 32.604, critic_reward: 32.627, revenue_rate: 0.7986, distance: 9.7456, memory: -0.0260, power: 0.2987, lr: 0.000100, took: 80.691s
[COOPERATIVE] Epoch 3, Batch 840/1563, loss: 2.680, reward: 32.523, critic_reward: 32.698, revenue_rate: 0.7939, distance: 9.5763, memory: -0.0260, power: 0.2925, lr: 0.000100, took: 79.202s
[COOPERATIVE] Epoch 3, Batch 850/1563, loss: 4.359, reward: 32.928, critic_reward: 31.855, revenue_rate: 0.8092, distance: 9.8968, memory: -0.0209, power: 0.3002, lr: 0.000100, took: 82.552s
[COOPERATIVE] Epoch 3, Batch 860/1563, loss: 5.162, reward: 33.397, critic_reward: 32.654, revenue_rate: 0.8197, distance: 10.0110, memory: -0.0130, power: 0.3080, lr: 0.000100, took: 83.976s
[COOPERATIVE] Epoch 3, Batch 870/1563, loss: 2.992, reward: 33.932, critic_reward: 34.318, revenue_rate: 0.8348, distance: 10.3255, memory: -0.0002, power: 0.3131, lr: 0.000100, took: 88.063s
[COOPERATIVE] Epoch 3, Batch 880/1563, loss: 2.264, reward: 34.206, critic_reward: 34.041, revenue_rate: 0.8400, distance: 10.4068, memory: -0.0105, power: 0.3149, lr: 0.000100, took: 86.547s
[COOPERATIVE] Epoch 3, Batch 890/1563, loss: 2.578, reward: 33.400, critic_reward: 33.608, revenue_rate: 0.8224, distance: 10.0751, memory: -0.0181, power: 0.3071, lr: 0.000100, took: 84.127s
[COOPERATIVE] Epoch 3, Batch 900/1563, loss: 2.357, reward: 32.398, critic_reward: 32.333, revenue_rate: 0.7917, distance: 9.6372, memory: -0.0300, power: 0.2926, lr: 0.000100, took: 79.388s
[COOPERATIVE] Epoch 3, Batch 910/1563, loss: 3.304, reward: 32.736, critic_reward: 32.461, revenue_rate: 0.8062, distance: 9.8153, memory: -0.0317, power: 0.2963, lr: 0.000100, took: 81.212s
[COOPERATIVE] Epoch 3, Batch 920/1563, loss: 3.222, reward: 32.886, critic_reward: 33.264, revenue_rate: 0.8074, distance: 9.8630, memory: -0.0306, power: 0.3010, lr: 0.000100, took: 82.530s
[COOPERATIVE] Epoch 3, Batch 930/1563, loss: 4.941, reward: 32.733, critic_reward: 32.780, revenue_rate: 0.8030, distance: 9.8718, memory: -0.0236, power: 0.2981, lr: 0.000100, took: 81.144s
[COOPERATIVE] Epoch 3, Batch 940/1563, loss: 2.455, reward: 32.327, critic_reward: 32.131, revenue_rate: 0.7953, distance: 9.6482, memory: -0.0334, power: 0.2930, lr: 0.000100, took: 79.570s
[COOPERATIVE] Epoch 3, Batch 950/1563, loss: 2.312, reward: 32.434, critic_reward: 32.551, revenue_rate: 0.7906, distance: 9.5566, memory: -0.0272, power: 0.2909, lr: 0.000100, took: 79.374s
[COOPERATIVE] Epoch 3, Batch 960/1563, loss: 2.981, reward: 31.770, critic_reward: 31.613, revenue_rate: 0.7772, distance: 9.2943, memory: -0.0472, power: 0.2820, lr: 0.000100, took: 77.060s
[COOPERATIVE] Epoch 3, Batch 970/1563, loss: 2.516, reward: 31.192, critic_reward: 31.101, revenue_rate: 0.7652, distance: 9.1618, memory: -0.0459, power: 0.2777, lr: 0.000100, took: 75.084s
[COOPERATIVE] Epoch 3, Batch 980/1563, loss: 3.046, reward: 32.827, critic_reward: 32.908, revenue_rate: 0.8069, distance: 9.8567, memory: -0.0259, power: 0.3024, lr: 0.000100, took: 84.699s
[COOPERATIVE] Epoch 3, Batch 990/1563, loss: 3.275, reward: 34.191, critic_reward: 33.853, revenue_rate: 0.8408, distance: 10.4449, memory: -0.0066, power: 0.3186, lr: 0.000100, took: 87.891s
[COOPERATIVE] Epoch 3, Batch 1000/1563, loss: 5.459, reward: 33.510, critic_reward: 34.980, revenue_rate: 0.8254, distance: 10.1277, memory: -0.0237, power: 0.3068, lr: 0.000100, took: 84.183s
[COOPERATIVE] Epoch 3, Batch 1010/1563, loss: 3.540, reward: 32.647, critic_reward: 31.930, revenue_rate: 0.7995, distance: 9.6905, memory: -0.0247, power: 0.2957, lr: 0.000100, took: 80.399s
[COOPERATIVE] Epoch 3, Batch 1020/1563, loss: 2.210, reward: 34.800, critic_reward: 34.943, revenue_rate: 0.8548, distance: 10.6329, memory: -0.0026, power: 0.3219, lr: 0.000100, took: 90.246s
[COOPERATIVE] Epoch 3, Batch 1030/1563, loss: 3.049, reward: 35.385, critic_reward: 35.909, revenue_rate: 0.8701, distance: 11.0439, memory: 0.0103, power: 0.3339, lr: 0.000100, took: 94.122s
[COOPERATIVE] Epoch 3, Batch 1040/1563, loss: 2.970, reward: 34.721, critic_reward: 34.373, revenue_rate: 0.8550, distance: 10.7389, memory: 0.0025, power: 0.3261, lr: 0.000100, took: 90.336s
[COOPERATIVE] Epoch 3, Batch 1050/1563, loss: 2.997, reward: 34.006, critic_reward: 34.119, revenue_rate: 0.8364, distance: 10.3240, memory: -0.0139, power: 0.3135, lr: 0.000100, took: 85.950s
[COOPERATIVE] Epoch 3, Batch 1060/1563, loss: 2.406, reward: 33.115, critic_reward: 32.932, revenue_rate: 0.8120, distance: 9.8580, memory: -0.0215, power: 0.3011, lr: 0.000100, took: 81.972s
[COOPERATIVE] Epoch 3, Batch 1070/1563, loss: 3.349, reward: 33.081, critic_reward: 33.927, revenue_rate: 0.8138, distance: 9.9367, memory: -0.0238, power: 0.3020, lr: 0.000100, took: 82.790s
[COOPERATIVE] Epoch 3, Batch 1080/1563, loss: 3.039, reward: 31.833, critic_reward: 31.638, revenue_rate: 0.7784, distance: 9.3077, memory: -0.0416, power: 0.2821, lr: 0.000100, took: 76.734s
[COOPERATIVE] Epoch 3, Batch 1090/1563, loss: 2.720, reward: 29.989, critic_reward: 29.721, revenue_rate: 0.7312, distance: 8.6409, memory: -0.0574, power: 0.2645, lr: 0.000100, took: 73.147s
[COOPERATIVE] Epoch 3, Batch 1100/1563, loss: 2.345, reward: 29.157, critic_reward: 29.116, revenue_rate: 0.7120, distance: 8.3264, memory: -0.0794, power: 0.2515, lr: 0.000100, took: 68.164s
[COOPERATIVE] Epoch 3, Batch 1110/1563, loss: 2.904, reward: 31.634, critic_reward: 31.727, revenue_rate: 0.7726, distance: 9.2847, memory: -0.0514, power: 0.2843, lr: 0.000100, took: 77.286s
[COOPERATIVE] Epoch 3, Batch 1120/1563, loss: 2.649, reward: 32.208, critic_reward: 31.830, revenue_rate: 0.7867, distance: 9.5401, memory: -0.0379, power: 0.2901, lr: 0.000100, took: 78.914s
[COOPERATIVE] Epoch 3, Batch 1130/1563, loss: 3.036, reward: 31.994, critic_reward: 31.999, revenue_rate: 0.7820, distance: 9.3927, memory: -0.0390, power: 0.2886, lr: 0.000100, took: 78.208s
[COOPERATIVE] Epoch 3, Batch 1140/1563, loss: 2.915, reward: 30.680, critic_reward: 30.928, revenue_rate: 0.7474, distance: 8.9229, memory: -0.0494, power: 0.2700, lr: 0.000100, took: 72.936s
[COOPERATIVE] Epoch 3, Batch 1150/1563, loss: 2.179, reward: 29.891, critic_reward: 29.916, revenue_rate: 0.7270, distance: 8.6025, memory: -0.0590, power: 0.2623, lr: 0.000100, took: 71.153s
[COOPERATIVE] Epoch 3, Batch 1160/1563, loss: 2.370, reward: 31.802, critic_reward: 31.678, revenue_rate: 0.7763, distance: 9.3115, memory: -0.0332, power: 0.2862, lr: 0.000100, took: 77.403s
[COOPERATIVE] Epoch 3, Batch 1170/1563, loss: 3.355, reward: 34.454, critic_reward: 34.017, revenue_rate: 0.8435, distance: 10.3115, memory: -0.0060, power: 0.3175, lr: 0.000100, took: 87.444s
[COOPERATIVE] Epoch 3, Batch 1180/1563, loss: 5.283, reward: 33.790, critic_reward: 32.939, revenue_rate: 0.8301, distance: 10.1681, memory: -0.0183, power: 0.3088, lr: 0.000100, took: 85.214s
[COOPERATIVE] Epoch 3, Batch 1190/1563, loss: 4.753, reward: 32.945, critic_reward: 32.315, revenue_rate: 0.8050, distance: 9.7775, memory: -0.0224, power: 0.2971, lr: 0.000100, took: 80.888s
[COOPERATIVE] Epoch 3, Batch 1200/1563, loss: 2.748, reward: 32.877, critic_reward: 33.254, revenue_rate: 0.8045, distance: 9.7470, memory: -0.0290, power: 0.2980, lr: 0.000100, took: 82.772s
[COOPERATIVE] Epoch 3, Batch 1210/1563, loss: 2.212, reward: 32.950, critic_reward: 32.798, revenue_rate: 0.8083, distance: 9.7632, memory: -0.0227, power: 0.2973, lr: 0.000100, took: 81.586s
[COOPERATIVE] Epoch 3, Batch 1220/1563, loss: 3.284, reward: 33.110, critic_reward: 33.863, revenue_rate: 0.8121, distance: 9.7900, memory: -0.0294, power: 0.2994, lr: 0.000100, took: 81.961s
[COOPERATIVE] Epoch 3, Batch 1230/1563, loss: 5.119, reward: 33.807, critic_reward: 32.623, revenue_rate: 0.8290, distance: 10.2173, memory: -0.0152, power: 0.3110, lr: 0.000100, took: 85.022s
[COOPERATIVE] Epoch 3, Batch 1240/1563, loss: 6.479, reward: 34.739, critic_reward: 36.288, revenue_rate: 0.8537, distance: 10.6338, memory: -0.0032, power: 0.3223, lr: 0.000100, took: 89.457s
[COOPERATIVE] Epoch 3, Batch 1250/1563, loss: 2.848, reward: 34.179, critic_reward: 34.033, revenue_rate: 0.8404, distance: 10.3049, memory: -0.0170, power: 0.3137, lr: 0.000100, took: 86.688s
[COOPERATIVE] Epoch 3, Batch 1260/1563, loss: 3.119, reward: 32.926, critic_reward: 33.312, revenue_rate: 0.8091, distance: 9.8095, memory: -0.0233, power: 0.2979, lr: 0.000100, took: 81.729s
[COOPERATIVE] Epoch 3, Batch 1270/1563, loss: 2.969, reward: 31.905, critic_reward: 31.661, revenue_rate: 0.7811, distance: 9.2815, memory: -0.0457, power: 0.2857, lr: 0.000100, took: 77.392s
[COOPERATIVE] Epoch 3, Batch 1280/1563, loss: 2.571, reward: 30.972, critic_reward: 31.070, revenue_rate: 0.7579, distance: 8.9321, memory: -0.0516, power: 0.2734, lr: 0.000100, took: 74.402s
[COOPERATIVE] Epoch 3, Batch 1290/1563, loss: 1.665, reward: 31.283, critic_reward: 31.049, revenue_rate: 0.7613, distance: 9.0310, memory: -0.0524, power: 0.2742, lr: 0.000100, took: 74.731s
[COOPERATIVE] Epoch 3, Batch 1300/1563, loss: 4.554, reward: 33.982, critic_reward: 34.071, revenue_rate: 0.8343, distance: 10.2634, memory: -0.0147, power: 0.3109, lr: 0.000100, took: 86.401s
[COOPERATIVE] Epoch 3, Batch 1310/1563, loss: 3.800, reward: 34.145, critic_reward: 33.918, revenue_rate: 0.8377, distance: 10.2110, memory: -0.0159, power: 0.3143, lr: 0.000100, took: 88.242s
[COOPERATIVE] Epoch 3, Batch 1320/1563, loss: 7.695, reward: 34.421, critic_reward: 36.220, revenue_rate: 0.8491, distance: 10.6785, memory: -0.0085, power: 0.3216, lr: 0.000100, took: 89.758s
[COOPERATIVE] Epoch 3, Batch 1330/1563, loss: 3.438, reward: 34.795, critic_reward: 34.554, revenue_rate: 0.8586, distance: 10.7786, memory: 0.0014, power: 0.3285, lr: 0.000100, took: 91.580s
[COOPERATIVE] Epoch 3, Batch 1340/1563, loss: 3.591, reward: 33.880, critic_reward: 34.767, revenue_rate: 0.8371, distance: 10.2605, memory: -0.0094, power: 0.3150, lr: 0.000100, took: 86.549s
[COOPERATIVE] Epoch 3, Batch 1350/1563, loss: 2.596, reward: 33.273, critic_reward: 33.269, revenue_rate: 0.8163, distance: 9.9268, memory: -0.0316, power: 0.3004, lr: 0.000100, took: 82.894s
[COOPERATIVE] Epoch 3, Batch 1360/1563, loss: 4.040, reward: 32.993, critic_reward: 32.939, revenue_rate: 0.8085, distance: 9.8662, memory: -0.0219, power: 0.2989, lr: 0.000100, took: 81.775s
[COOPERATIVE] Epoch 3, Batch 1370/1563, loss: 3.107, reward: 32.424, critic_reward: 32.262, revenue_rate: 0.7906, distance: 9.4542, memory: -0.0357, power: 0.2873, lr: 0.000100, took: 78.365s
[COOPERATIVE] Epoch 3, Batch 1380/1563, loss: 2.461, reward: 33.583, critic_reward: 33.537, revenue_rate: 0.8264, distance: 10.1223, memory: -0.0237, power: 0.3087, lr: 0.000100, took: 84.821s
[COOPERATIVE] Epoch 3, Batch 1390/1563, loss: 3.235, reward: 33.584, critic_reward: 34.302, revenue_rate: 0.8246, distance: 10.1364, memory: -0.0189, power: 0.3071, lr: 0.000100, took: 84.374s
[COOPERATIVE] Epoch 3, Batch 1400/1563, loss: 2.900, reward: 33.361, critic_reward: 32.992, revenue_rate: 0.8156, distance: 9.9504, memory: -0.0254, power: 0.3008, lr: 0.000100, took: 82.958s
[COOPERATIVE] Epoch 3, Batch 1410/1563, loss: 3.702, reward: 32.321, critic_reward: 32.278, revenue_rate: 0.7928, distance: 9.6214, memory: -0.0335, power: 0.2899, lr: 0.000100, took: 78.768s
[COOPERATIVE] Epoch 3, Batch 1420/1563, loss: 4.008, reward: 33.145, critic_reward: 33.563, revenue_rate: 0.8127, distance: 9.8837, memory: -0.0264, power: 0.3015, lr: 0.000100, took: 84.216s
[COOPERATIVE] Epoch 3, Batch 1430/1563, loss: 3.876, reward: 33.829, critic_reward: 33.402, revenue_rate: 0.8286, distance: 10.1836, memory: -0.0167, power: 0.3111, lr: 0.000100, took: 84.819s
[COOPERATIVE] Epoch 3, Batch 1440/1563, loss: 2.927, reward: 34.043, critic_reward: 34.520, revenue_rate: 0.8343, distance: 10.3554, memory: -0.0063, power: 0.3150, lr: 0.000100, took: 86.323s
[COOPERATIVE] Epoch 3, Batch 1450/1563, loss: 3.558, reward: 33.753, critic_reward: 33.919, revenue_rate: 0.8321, distance: 10.2300, memory: -0.0074, power: 0.3118, lr: 0.000100, took: 86.159s
[COOPERATIVE] Epoch 3, Batch 1460/1563, loss: 2.328, reward: 31.822, critic_reward: 31.872, revenue_rate: 0.7784, distance: 9.3411, memory: -0.0394, power: 0.2869, lr: 0.000100, took: 77.937s
[COOPERATIVE] Epoch 3, Batch 1470/1563, loss: 4.488, reward: 29.024, critic_reward: 28.407, revenue_rate: 0.7106, distance: 8.3977, memory: -0.0706, power: 0.2562, lr: 0.000100, took: 68.982s
[COOPERATIVE] Epoch 3, Batch 1480/1563, loss: 2.225, reward: 30.387, critic_reward: 30.286, revenue_rate: 0.7411, distance: 8.8852, memory: -0.0498, power: 0.2689, lr: 0.000100, took: 73.354s
[COOPERATIVE] Epoch 3, Batch 1490/1563, loss: 3.949, reward: 31.085, critic_reward: 31.424, revenue_rate: 0.7606, distance: 9.1376, memory: -0.0498, power: 0.2785, lr: 0.000100, took: 75.450s
[COOPERATIVE] Epoch 3, Batch 1500/1563, loss: 3.349, reward: 32.155, critic_reward: 31.733, revenue_rate: 0.7860, distance: 9.4601, memory: -0.0365, power: 0.2887, lr: 0.000100, took: 80.737s
[COOPERATIVE] Epoch 3, Batch 1510/1563, loss: 6.244, reward: 32.887, critic_reward: 31.912, revenue_rate: 0.8082, distance: 9.8602, memory: -0.0175, power: 0.3001, lr: 0.000100, took: 89.041s
[COOPERATIVE] Epoch 3, Batch 1520/1563, loss: 2.664, reward: 33.444, critic_reward: 33.142, revenue_rate: 0.8207, distance: 10.0470, memory: -0.0164, power: 0.3059, lr: 0.000100, took: 83.363s
[COOPERATIVE] Epoch 3, Batch 1530/1563, loss: 2.666, reward: 33.924, critic_reward: 33.562, revenue_rate: 0.8336, distance: 10.2395, memory: -0.0173, power: 0.3121, lr: 0.000100, took: 87.430s
[COOPERATIVE] Epoch 3, Batch 1540/1563, loss: 3.307, reward: 34.447, critic_reward: 34.682, revenue_rate: 0.8487, distance: 10.5714, memory: -0.0039, power: 0.3234, lr: 0.000100, took: 89.187s
[COOPERATIVE] Epoch 3, Batch 1550/1563, loss: 2.075, reward: 35.424, critic_reward: 35.192, revenue_rate: 0.8761, distance: 11.2276, memory: 0.0200, power: 0.3373, lr: 0.000100, took: 93.967s
[COOPERATIVE] Epoch 3, Batch 1560/1563, loss: 5.644, reward: 35.702, critic_reward: 36.314, revenue_rate: 0.8865, distance: 11.3368, memory: 0.0209, power: 0.3409, lr: 0.000100, took: 95.171s
[COOPERATIVE] 开始验证...
[COOPERATIVE] 验证完成 - Epoch 3, reward: 36.189, revenue_rate: 0.8940, distance: 11.3046, memory: 0.0173, power: 0.3429
[COOPERATIVE] 已保存新模型到 constellation_smp\constellation_smp100\multi_constellation_comparison_2025_08_25_17_02_29\constellation_gpnindrnn_cooperative_2025_08_25_17_02_29 (验证集奖励: 36.1888)
[COOPERATIVE] 训练完成
训练结束时间: 2025-08-26 04:35:18
训练总耗时: 11:26:18.118327
训练过程统计:
  最终训练奖励: 35.0103
  最佳验证奖励: 36.1888
  训练轮数完成: 4689
  奖励提升: 24.5720
  平均每轮提升: 0.0052
生成训练曲线图...
✓ 训练曲线图已保存到: constellation_smp\constellation_smp100\multi_constellation_comparison_2025_08_25_17_02_29\constellation_gpnindrnn_cooperative_2025_08_25_17_02_29\train_loss_reward.png
开始测试 cooperative 模式...
测试配置:
  测试数据大小: 10000
  测试批次数: 157
  可视化样本数: 5
测试开始时间: 2025-08-26 04:36:02
测试结束时间: 2025-08-26 05:01:59
测试耗时: 0:25:56.894803

COOPERATIVE 模式完整结果:
==================================================
训练结果:
  最佳验证奖励: 36.1888
  模型保存路径: constellation_smp\constellation_smp100\multi_constellation_comparison_2025_08_25_17_02_29\constellation_gpnindrnn_cooperative_2025_08_25_17_02_29
测试结果:
  平均收益率: 0.8908
  平均距离: 11.2893
  平均内存使用: 0.0157
  平均功耗: 0.3415
模型信息:
  Actor参数: 2,211,337
  Critic参数: 494,285
  总参数: 2,705,622
综合性能评分: 3.0848
文件输出:
  权重文件: actor.pt, critic.pt
  训练日志: log.txt
  测试结果: test_cooperative/
==================================================

================================================================================
开始训练星座模式: COMPETITIVE
================================================================================
competitive 模式模型信息:
  Actor参数数量: 2,211,337
  Critic参数数量: 494,285
  总参数数量: 2,705,622
详细训练配置:
  数据集大小: 训练100000, 验证10000
  学习率: Actor=0.0001, Critic=0.0002
  批次大小: 64
  训练轮数: 3
  梯度裁剪: 1
  权重衰减: 0.0001
  随机种子: 12346
  内存总量: 0.3
  功率总量: 5
开始训练 competitive 模式...
训练开始时间: 2025-08-26 05:08:29
详细训练过程:
[COMPETITIVE] 开始训练 Epoch 1/3
[COMPETITIVE] Epoch 1, Batch 10/1563, loss: 2706.636, reward: 14.082, critic_reward: 38.395, revenue_rate: 0.3556, distance: 5.2205, memory: -0.1036, power: 0.1570, lr: 0.000100, took: 46.757s
[COMPETITIVE] Epoch 1, Batch 20/1563, loss: 80.748, reward: 18.029, critic_reward: 21.393, revenue_rate: 0.4557, distance: 6.6447, memory: -0.1185, power: 0.1999, lr: 0.000100, took: 53.333s
[COMPETITIVE] Epoch 1, Batch 30/1563, loss: 20.760, reward: 20.702, critic_reward: 19.917, revenue_rate: 0.5199, distance: 7.4116, memory: -0.1079, power: 0.2245, lr: 0.000100, took: 60.273s
[COMPETITIVE] Epoch 1, Batch 40/1563, loss: 8.582, reward: 20.671, critic_reward: 21.093, revenue_rate: 0.5167, distance: 7.1930, memory: -0.1127, power: 0.2179, lr: 0.000100, took: 57.710s
[COMPETITIVE] Epoch 1, Batch 50/1563, loss: 7.239, reward: 21.921, critic_reward: 21.701, revenue_rate: 0.5450, distance: 7.3017, memory: -0.1055, power: 0.2212, lr: 0.000100, took: 58.234s
[COMPETITIVE] Epoch 1, Batch 60/1563, loss: 6.339, reward: 25.553, critic_reward: 25.888, revenue_rate: 0.6334, distance: 8.1058, memory: -0.0794, power: 0.2438, lr: 0.000100, took: 65.806s
[COMPETITIVE] Epoch 1, Batch 70/1563, loss: 7.239, reward: 25.916, critic_reward: 26.221, revenue_rate: 0.6380, distance: 8.1189, memory: -0.0883, power: 0.2473, lr: 0.000100, took: 66.122s
[COMPETITIVE] Epoch 1, Batch 80/1563, loss: 6.051, reward: 24.695, critic_reward: 24.963, revenue_rate: 0.6062, distance: 7.5974, memory: -0.0896, power: 0.2309, lr: 0.000100, took: 62.017s
[COMPETITIVE] Epoch 1, Batch 90/1563, loss: 12.149, reward: 25.415, critic_reward: 26.904, revenue_rate: 0.6256, distance: 7.7679, memory: -0.0939, power: 0.2351, lr: 0.000100, took: 63.157s
[COMPETITIVE] Epoch 1, Batch 100/1563, loss: 12.467, reward: 26.792, critic_reward: 26.439, revenue_rate: 0.6560, distance: 8.1231, memory: -0.0770, power: 0.2460, lr: 0.000100, took: 68.391s
[COMPETITIVE] Epoch 1, Batch 110/1563, loss: 5.855, reward: 27.793, critic_reward: 28.064, revenue_rate: 0.6828, distance: 8.4665, memory: -0.0753, power: 0.2567, lr: 0.000100, took: 69.608s
[COMPETITIVE] Epoch 1, Batch 120/1563, loss: 4.694, reward: 27.854, critic_reward: 27.950, revenue_rate: 0.6824, distance: 8.5728, memory: -0.0688, power: 0.2607, lr: 0.000100, took: 70.188s
[COMPETITIVE] Epoch 1, Batch 130/1563, loss: 4.864, reward: 30.175, critic_reward: 29.833, revenue_rate: 0.7464, distance: 9.5687, memory: -0.0376, power: 0.2889, lr: 0.000100, took: 79.216s
[COMPETITIVE] Epoch 1, Batch 140/1563, loss: 4.781, reward: 28.883, critic_reward: 29.042, revenue_rate: 0.7101, distance: 8.8913, memory: -0.0640, power: 0.2676, lr: 0.000100, took: 72.308s
[COMPETITIVE] Epoch 1, Batch 150/1563, loss: 5.282, reward: 28.814, critic_reward: 29.230, revenue_rate: 0.7088, distance: 8.8768, memory: -0.0626, power: 0.2687, lr: 0.000100, took: 72.194s
[COMPETITIVE] Epoch 1, Batch 160/1563, loss: 5.401, reward: 28.108, critic_reward: 28.392, revenue_rate: 0.6895, distance: 8.5254, memory: -0.0719, power: 0.2604, lr: 0.000100, took: 70.153s
[COMPETITIVE] Epoch 1, Batch 170/1563, loss: 5.725, reward: 27.185, critic_reward: 26.329, revenue_rate: 0.6698, distance: 8.3828, memory: -0.0710, power: 0.2523, lr: 0.000100, took: 68.815s
[COMPETITIVE] Epoch 1, Batch 180/1563, loss: 5.786, reward: 29.520, critic_reward: 29.161, revenue_rate: 0.7291, distance: 9.2294, memory: -0.0465, power: 0.2775, lr: 0.000100, took: 75.430s
[COMPETITIVE] Epoch 1, Batch 190/1563, loss: 7.372, reward: 31.116, critic_reward: 30.946, revenue_rate: 0.7740, distance: 10.0215, memory: -0.0242, power: 0.3028, lr: 0.000100, took: 82.981s
[COMPETITIVE] Epoch 1, Batch 200/1563, loss: 4.686, reward: 29.787, critic_reward: 30.370, revenue_rate: 0.7378, distance: 9.3438, memory: -0.0452, power: 0.2832, lr: 0.000100, took: 77.124s
[COMPETITIVE] Epoch 1, Batch 210/1563, loss: 6.542, reward: 27.415, critic_reward: 28.120, revenue_rate: 0.6733, distance: 8.4063, memory: -0.0698, power: 0.2515, lr: 0.000100, took: 67.751s
[COMPETITIVE] Epoch 1, Batch 220/1563, loss: 6.718, reward: 28.839, critic_reward: 29.013, revenue_rate: 0.7089, distance: 8.7801, memory: -0.0637, power: 0.2655, lr: 0.000100, took: 71.404s
[COMPETITIVE] Epoch 1, Batch 230/1563, loss: 12.069, reward: 30.708, critic_reward: 31.765, revenue_rate: 0.7587, distance: 9.5543, memory: -0.0450, power: 0.2881, lr: 0.000100, took: 81.188s
[COMPETITIVE] Epoch 1, Batch 240/1563, loss: 9.644, reward: 30.251, critic_reward: 29.295, revenue_rate: 0.7452, distance: 9.4279, memory: -0.0425, power: 0.2826, lr: 0.000100, took: 77.148s
[COMPETITIVE] Epoch 1, Batch 250/1563, loss: 6.873, reward: 28.920, critic_reward: 28.220, revenue_rate: 0.7142, distance: 8.8135, memory: -0.0506, power: 0.2690, lr: 0.000100, took: 72.761s
[COMPETITIVE] Epoch 1, Batch 260/1563, loss: 5.464, reward: 28.881, critic_reward: 29.659, revenue_rate: 0.7058, distance: 8.5738, memory: -0.0694, power: 0.2607, lr: 0.000100, took: 70.233s
[COMPETITIVE] Epoch 1, Batch 270/1563, loss: 4.478, reward: 28.580, critic_reward: 28.995, revenue_rate: 0.7012, distance: 8.5300, memory: -0.0622, power: 0.2610, lr: 0.000100, took: 69.952s
[COMPETITIVE] Epoch 1, Batch 280/1563, loss: 6.255, reward: 28.951, critic_reward: 29.692, revenue_rate: 0.7078, distance: 8.7089, memory: -0.0552, power: 0.2636, lr: 0.000100, took: 70.550s
[COMPETITIVE] Epoch 1, Batch 290/1563, loss: 8.595, reward: 31.191, critic_reward: 30.090, revenue_rate: 0.7667, distance: 9.7378, memory: -0.0362, power: 0.2891, lr: 0.000100, took: 79.492s
[COMPETITIVE] Epoch 1, Batch 300/1563, loss: 7.418, reward: 30.167, critic_reward: 30.238, revenue_rate: 0.7440, distance: 9.1711, memory: -0.0493, power: 0.2765, lr: 0.000100, took: 74.862s
[COMPETITIVE] Epoch 1, Batch 310/1563, loss: 5.564, reward: 29.630, critic_reward: 30.200, revenue_rate: 0.7257, distance: 8.8574, memory: -0.0517, power: 0.2699, lr: 0.000100, took: 73.021s
[COMPETITIVE] Epoch 1, Batch 320/1563, loss: 4.835, reward: 30.181, critic_reward: 30.729, revenue_rate: 0.7422, distance: 9.2133, memory: -0.0479, power: 0.2777, lr: 0.000100, took: 75.571s
[COMPETITIVE] Epoch 1, Batch 330/1563, loss: 6.247, reward: 30.901, critic_reward: 31.438, revenue_rate: 0.7586, distance: 9.3929, memory: -0.0404, power: 0.2853, lr: 0.000100, took: 77.407s
[COMPETITIVE] Epoch 1, Batch 340/1563, loss: 7.411, reward: 29.750, critic_reward: 30.508, revenue_rate: 0.7268, distance: 8.9010, memory: -0.0613, power: 0.2698, lr: 0.000100, took: 72.706s
[COMPETITIVE] Epoch 1, Batch 350/1563, loss: 4.647, reward: 28.912, critic_reward: 28.893, revenue_rate: 0.7079, distance: 8.6824, memory: -0.0663, power: 0.2617, lr: 0.000100, took: 72.584s
[COMPETITIVE] Epoch 1, Batch 360/1563, loss: 6.447, reward: 30.729, critic_reward: 30.029, revenue_rate: 0.7539, distance: 9.4054, memory: -0.0494, power: 0.2848, lr: 0.000100, took: 77.284s
[COMPETITIVE] Epoch 1, Batch 370/1563, loss: 22.023, reward: 31.963, critic_reward: 34.929, revenue_rate: 0.7962, distance: 10.6347, memory: 0.0104, power: 0.3196, lr: 0.000100, took: 88.628s
[COMPETITIVE] Epoch 1, Batch 380/1563, loss: 7.908, reward: 33.448, critic_reward: 33.898, revenue_rate: 0.8351, distance: 11.2603, memory: 0.0295, power: 0.3418, lr: 0.000100, took: 95.570s
[COMPETITIVE] Epoch 1, Batch 390/1563, loss: 5.441, reward: 32.379, critic_reward: 31.779, revenue_rate: 0.8011, distance: 10.2824, memory: -0.0031, power: 0.3116, lr: 0.000100, took: 86.466s
[COMPETITIVE] Epoch 1, Batch 400/1563, loss: 5.043, reward: 30.511, critic_reward: 30.222, revenue_rate: 0.7509, distance: 9.4357, memory: -0.0396, power: 0.2854, lr: 0.000100, took: 77.931s
[COMPETITIVE] Epoch 1, Batch 410/1563, loss: 8.350, reward: 30.168, critic_reward: 28.754, revenue_rate: 0.7409, distance: 9.1977, memory: -0.0586, power: 0.2787, lr: 0.000100, took: 75.436s
[COMPETITIVE] Epoch 1, Batch 420/1563, loss: 5.980, reward: 30.753, critic_reward: 31.560, revenue_rate: 0.7557, distance: 9.4418, memory: -0.0399, power: 0.2863, lr: 0.000100, took: 78.139s
[COMPETITIVE] Epoch 1, Batch 430/1563, loss: 24.266, reward: 30.988, critic_reward: 33.852, revenue_rate: 0.7615, distance: 9.3543, memory: -0.0433, power: 0.2870, lr: 0.000100, took: 78.185s
[COMPETITIVE] Epoch 1, Batch 440/1563, loss: 5.999, reward: 27.700, critic_reward: 27.487, revenue_rate: 0.6782, distance: 8.2443, memory: -0.0746, power: 0.2494, lr: 0.000100, took: 67.111s
[COMPETITIVE] Epoch 1, Batch 450/1563, loss: 9.073, reward: 26.109, critic_reward: 24.975, revenue_rate: 0.6393, distance: 7.6834, memory: -0.0929, power: 0.2336, lr: 0.000100, took: 62.623s
[COMPETITIVE] Epoch 1, Batch 460/1563, loss: 3.808, reward: 27.597, critic_reward: 27.475, revenue_rate: 0.6758, distance: 8.2296, memory: -0.0662, power: 0.2504, lr: 0.000100, took: 69.369s
[COMPETITIVE] Epoch 1, Batch 470/1563, loss: 5.549, reward: 29.379, critic_reward: 28.882, revenue_rate: 0.7250, distance: 9.0642, memory: -0.0509, power: 0.2740, lr: 0.000100, took: 74.039s
[COMPETITIVE] Epoch 1, Batch 480/1563, loss: 4.558, reward: 30.607, critic_reward: 30.383, revenue_rate: 0.7548, distance: 9.5012, memory: -0.0474, power: 0.2857, lr: 0.000100, took: 78.120s
[COMPETITIVE] Epoch 1, Batch 490/1563, loss: 3.958, reward: 30.425, critic_reward: 30.344, revenue_rate: 0.7511, distance: 9.3760, memory: -0.0422, power: 0.2846, lr: 0.000100, took: 77.399s
[COMPETITIVE] Epoch 1, Batch 500/1563, loss: 4.432, reward: 30.626, critic_reward: 31.089, revenue_rate: 0.7528, distance: 9.2940, memory: -0.0433, power: 0.2837, lr: 0.000100, took: 77.389s
[COMPETITIVE] Epoch 1, Batch 510/1563, loss: 9.774, reward: 29.747, critic_reward: 29.057, revenue_rate: 0.7342, distance: 9.0815, memory: -0.0603, power: 0.2745, lr: 0.000100, took: 74.443s
[COMPETITIVE] Epoch 1, Batch 520/1563, loss: 6.127, reward: 29.166, critic_reward: 29.689, revenue_rate: 0.7153, distance: 8.8815, memory: -0.0645, power: 0.2679, lr: 0.000100, took: 72.299s
[COMPETITIVE] Epoch 1, Batch 530/1563, loss: 3.753, reward: 29.869, critic_reward: 29.288, revenue_rate: 0.7318, distance: 8.9258, memory: -0.0565, power: 0.2696, lr: 0.000100, took: 72.963s
[COMPETITIVE] Epoch 1, Batch 540/1563, loss: 4.696, reward: 30.611, critic_reward: 30.676, revenue_rate: 0.7513, distance: 9.3752, memory: -0.0424, power: 0.2813, lr: 0.000100, took: 76.594s
[COMPETITIVE] Epoch 1, Batch 550/1563, loss: 8.271, reward: 31.942, critic_reward: 32.329, revenue_rate: 0.7843, distance: 9.8490, memory: -0.0283, power: 0.2967, lr: 0.000100, took: 81.178s
[COMPETITIVE] Epoch 1, Batch 560/1563, loss: 10.059, reward: 30.083, critic_reward: 31.918, revenue_rate: 0.7387, distance: 9.0298, memory: -0.0524, power: 0.2752, lr: 0.000100, took: 74.542s
[COMPETITIVE] Epoch 1, Batch 570/1563, loss: 5.842, reward: 29.267, critic_reward: 27.871, revenue_rate: 0.7180, distance: 8.7717, memory: -0.0528, power: 0.2646, lr: 0.000100, took: 71.408s
[COMPETITIVE] Epoch 1, Batch 580/1563, loss: 4.518, reward: 30.079, critic_reward: 29.667, revenue_rate: 0.7367, distance: 9.0433, memory: -0.0531, power: 0.2733, lr: 0.000100, took: 76.580s
[COMPETITIVE] Epoch 1, Batch 590/1563, loss: 5.237, reward: 30.384, critic_reward: 30.995, revenue_rate: 0.7463, distance: 9.1505, memory: -0.0498, power: 0.2755, lr: 0.000100, took: 75.362s
[COMPETITIVE] Epoch 1, Batch 600/1563, loss: 6.716, reward: 30.122, critic_reward: 29.921, revenue_rate: 0.7398, distance: 9.0892, memory: -0.0529, power: 0.2760, lr: 0.000100, took: 74.542s
[COMPETITIVE] Epoch 1, Batch 610/1563, loss: 3.698, reward: 28.599, critic_reward: 28.327, revenue_rate: 0.7019, distance: 8.5689, memory: -0.0644, power: 0.2606, lr: 0.000100, took: 69.576s
[COMPETITIVE] Epoch 1, Batch 620/1563, loss: 3.773, reward: 28.090, critic_reward: 28.031, revenue_rate: 0.6873, distance: 8.3508, memory: -0.0770, power: 0.2518, lr: 0.000100, took: 68.003s
[COMPETITIVE] Epoch 1, Batch 630/1563, loss: 2.847, reward: 27.866, critic_reward: 27.371, revenue_rate: 0.6822, distance: 8.2669, memory: -0.0730, power: 0.2514, lr: 0.000100, took: 67.760s
[COMPETITIVE] Epoch 1, Batch 640/1563, loss: 7.954, reward: 28.781, critic_reward: 28.679, revenue_rate: 0.7054, distance: 8.5714, memory: -0.0687, power: 0.2583, lr: 0.000100, took: 69.961s
[COMPETITIVE] Epoch 1, Batch 650/1563, loss: 11.429, reward: 30.613, critic_reward: 31.581, revenue_rate: 0.7513, distance: 9.2615, memory: -0.0415, power: 0.2830, lr: 0.000100, took: 76.475s
[COMPETITIVE] Epoch 1, Batch 660/1563, loss: 7.766, reward: 30.904, critic_reward: 30.211, revenue_rate: 0.7604, distance: 9.4132, memory: -0.0408, power: 0.2862, lr: 0.000100, took: 77.523s
[COMPETITIVE] Epoch 1, Batch 670/1563, loss: 4.165, reward: 31.149, critic_reward: 31.491, revenue_rate: 0.7611, distance: 9.3465, memory: -0.0406, power: 0.2834, lr: 0.000100, took: 77.210s
[COMPETITIVE] Epoch 1, Batch 680/1563, loss: 6.051, reward: 31.247, critic_reward: 31.451, revenue_rate: 0.7674, distance: 9.4718, memory: -0.0350, power: 0.2894, lr: 0.000100, took: 78.832s
[COMPETITIVE] Epoch 1, Batch 690/1563, loss: 3.361, reward: 31.234, critic_reward: 31.104, revenue_rate: 0.7656, distance: 9.4297, memory: -0.0386, power: 0.2855, lr: 0.000100, took: 77.695s
[COMPETITIVE] Epoch 1, Batch 700/1563, loss: 11.790, reward: 31.433, critic_reward: 32.250, revenue_rate: 0.7707, distance: 9.4996, memory: -0.0377, power: 0.2886, lr: 0.000100, took: 81.021s
[COMPETITIVE] Epoch 1, Batch 710/1563, loss: 8.761, reward: 31.884, critic_reward: 31.019, revenue_rate: 0.7874, distance: 9.8249, memory: -0.0334, power: 0.2984, lr: 0.000100, took: 80.596s
[COMPETITIVE] Epoch 1, Batch 720/1563, loss: 4.214, reward: 32.372, critic_reward: 33.141, revenue_rate: 0.7947, distance: 9.9375, memory: -0.0191, power: 0.3014, lr: 0.000100, took: 82.780s
[COMPETITIVE] Epoch 1, Batch 730/1563, loss: 3.326, reward: 31.121, critic_reward: 30.918, revenue_rate: 0.7672, distance: 9.4769, memory: -0.0315, power: 0.2882, lr: 0.000100, took: 78.515s
[COMPETITIVE] Epoch 1, Batch 740/1563, loss: 4.172, reward: 30.044, critic_reward: 30.535, revenue_rate: 0.7355, distance: 8.9694, memory: -0.0571, power: 0.2707, lr: 0.000100, took: 73.517s
[COMPETITIVE] Epoch 1, Batch 750/1563, loss: 5.320, reward: 29.244, critic_reward: 28.350, revenue_rate: 0.7183, distance: 8.7395, memory: -0.0646, power: 0.2656, lr: 0.000100, took: 71.051s
[COMPETITIVE] Epoch 1, Batch 760/1563, loss: 6.307, reward: 31.298, critic_reward: 31.349, revenue_rate: 0.7660, distance: 9.4382, memory: -0.0463, power: 0.2854, lr: 0.000100, took: 77.387s
[COMPETITIVE] Epoch 1, Batch 770/1563, loss: 7.164, reward: 33.486, critic_reward: 33.945, revenue_rate: 0.8241, distance: 10.3591, memory: -0.0151, power: 0.3134, lr: 0.000100, took: 86.359s
[COMPETITIVE] Epoch 1, Batch 780/1563, loss: 4.804, reward: 32.562, critic_reward: 32.834, revenue_rate: 0.8012, distance: 9.9746, memory: -0.0208, power: 0.3022, lr: 0.000100, took: 82.775s
[COMPETITIVE] Epoch 1, Batch 790/1563, loss: 4.444, reward: 31.317, critic_reward: 31.859, revenue_rate: 0.7682, distance: 9.3229, memory: -0.0490, power: 0.2838, lr: 0.000100, took: 77.159s
[COMPETITIVE] Epoch 1, Batch 800/1563, loss: 2.967, reward: 31.339, critic_reward: 31.484, revenue_rate: 0.7682, distance: 9.3025, memory: -0.0481, power: 0.2840, lr: 0.000100, took: 77.376s
[COMPETITIVE] Epoch 1, Batch 810/1563, loss: 5.950, reward: 31.577, critic_reward: 31.318, revenue_rate: 0.7769, distance: 9.6268, memory: -0.0476, power: 0.2905, lr: 0.000100, took: 79.155s
[COMPETITIVE] Epoch 1, Batch 820/1563, loss: 5.224, reward: 30.733, critic_reward: 30.356, revenue_rate: 0.7526, distance: 9.1064, memory: -0.0520, power: 0.2757, lr: 0.000100, took: 76.765s
[COMPETITIVE] Epoch 1, Batch 830/1563, loss: 3.464, reward: 29.540, critic_reward: 29.719, revenue_rate: 0.7217, distance: 8.6281, memory: -0.0632, power: 0.2634, lr: 0.000100, took: 71.337s
[COMPETITIVE] Epoch 1, Batch 840/1563, loss: 5.711, reward: 29.108, critic_reward: 27.920, revenue_rate: 0.7124, distance: 8.6517, memory: -0.0681, power: 0.2619, lr: 0.000100, took: 71.064s
[COMPETITIVE] Epoch 1, Batch 850/1563, loss: 6.627, reward: 27.691, critic_reward: 29.378, revenue_rate: 0.6743, distance: 8.0560, memory: -0.0774, power: 0.2440, lr: 0.000100, took: 65.701s
[COMPETITIVE] Epoch 1, Batch 860/1563, loss: 9.228, reward: 27.443, critic_reward: 25.854, revenue_rate: 0.6675, distance: 7.9633, memory: -0.0891, power: 0.2416, lr: 0.000100, took: 64.586s
[COMPETITIVE] Epoch 1, Batch 870/1563, loss: 3.829, reward: 28.192, critic_reward: 27.897, revenue_rate: 0.6872, distance: 8.1661, memory: -0.0802, power: 0.2473, lr: 0.000100, took: 66.820s
[COMPETITIVE] Epoch 1, Batch 880/1563, loss: 3.958, reward: 29.898, critic_reward: 30.087, revenue_rate: 0.7320, distance: 8.9096, memory: -0.0482, power: 0.2678, lr: 0.000100, took: 72.777s
[COMPETITIVE] Epoch 1, Batch 890/1563, loss: 4.568, reward: 31.349, critic_reward: 31.653, revenue_rate: 0.7727, distance: 9.5126, memory: -0.0341, power: 0.2866, lr: 0.000100, took: 78.395s
[COMPETITIVE] Epoch 1, Batch 900/1563, loss: 3.892, reward: 32.132, critic_reward: 32.484, revenue_rate: 0.7860, distance: 9.6998, memory: -0.0337, power: 0.2931, lr: 0.000100, took: 80.027s
[COMPETITIVE] Epoch 1, Batch 910/1563, loss: 6.112, reward: 32.773, critic_reward: 32.088, revenue_rate: 0.8045, distance: 9.9400, memory: -0.0314, power: 0.3024, lr: 0.000100, took: 82.155s
[COMPETITIVE] Epoch 1, Batch 920/1563, loss: 3.674, reward: 31.795, critic_reward: 31.722, revenue_rate: 0.7801, distance: 9.6804, memory: -0.0373, power: 0.2911, lr: 0.000100, took: 79.525s
[COMPETITIVE] Epoch 1, Batch 930/1563, loss: 4.696, reward: 30.428, critic_reward: 29.440, revenue_rate: 0.7438, distance: 9.0091, memory: -0.0563, power: 0.2726, lr: 0.000100, took: 74.198s
[COMPETITIVE] Epoch 1, Batch 940/1563, loss: 2.779, reward: 30.898, critic_reward: 30.923, revenue_rate: 0.7555, distance: 9.1276, memory: -0.0435, power: 0.2777, lr: 0.000100, took: 77.161s
[COMPETITIVE] Epoch 1, Batch 950/1563, loss: 4.154, reward: 32.699, critic_reward: 33.211, revenue_rate: 0.8034, distance: 10.0012, memory: -0.0267, power: 0.3032, lr: 0.000100, took: 82.805s
[COMPETITIVE] Epoch 1, Batch 960/1563, loss: 4.365, reward: 33.049, critic_reward: 32.573, revenue_rate: 0.8130, distance: 10.0711, memory: -0.0089, power: 0.3080, lr: 0.000100, took: 85.049s
[COMPETITIVE] Epoch 1, Batch 970/1563, loss: 2.978, reward: 32.527, critic_reward: 32.149, revenue_rate: 0.8018, distance: 9.9894, memory: -0.0115, power: 0.3014, lr: 0.000100, took: 82.916s
[COMPETITIVE] Epoch 1, Batch 980/1563, loss: 5.260, reward: 33.280, critic_reward: 34.392, revenue_rate: 0.8197, distance: 10.2904, memory: -0.0172, power: 0.3114, lr: 0.000100, took: 85.544s
[COMPETITIVE] Epoch 1, Batch 990/1563, loss: 4.211, reward: 31.706, critic_reward: 31.140, revenue_rate: 0.7826, distance: 9.7494, memory: -0.0294, power: 0.2940, lr: 0.000100, took: 80.156s
[COMPETITIVE] Epoch 1, Batch 1000/1563, loss: 2.726, reward: 31.398, critic_reward: 31.325, revenue_rate: 0.7739, distance: 9.5213, memory: -0.0409, power: 0.2860, lr: 0.000100, took: 77.987s
[COMPETITIVE] Epoch 1, Batch 1010/1563, loss: 3.367, reward: 30.359, critic_reward: 30.110, revenue_rate: 0.7433, distance: 9.0059, memory: -0.0557, power: 0.2734, lr: 0.000100, took: 73.560s
[COMPETITIVE] Epoch 1, Batch 1020/1563, loss: 3.842, reward: 31.139, critic_reward: 31.266, revenue_rate: 0.7654, distance: 9.3998, memory: -0.0451, power: 0.2857, lr: 0.000100, took: 77.370s
[COMPETITIVE] Epoch 1, Batch 1030/1563, loss: 3.004, reward: 32.126, critic_reward: 32.314, revenue_rate: 0.7915, distance: 9.8286, memory: -0.0258, power: 0.2978, lr: 0.000100, took: 81.408s
[COMPETITIVE] Epoch 1, Batch 1040/1563, loss: 3.410, reward: 32.929, critic_reward: 32.711, revenue_rate: 0.8112, distance: 10.1474, memory: -0.0066, power: 0.3101, lr: 0.000100, took: 84.011s
[COMPETITIVE] Epoch 1, Batch 1050/1563, loss: 4.851, reward: 34.060, critic_reward: 34.259, revenue_rate: 0.8403, distance: 10.6859, memory: 0.0207, power: 0.3225, lr: 0.000100, took: 92.445s
[COMPETITIVE] Epoch 1, Batch 1060/1563, loss: 4.668, reward: 33.329, critic_reward: 33.467, revenue_rate: 0.8184, distance: 10.2277, memory: -0.0112, power: 0.3100, lr: 0.000100, took: 85.679s
[COMPETITIVE] Epoch 1, Batch 1070/1563, loss: 6.933, reward: 31.445, critic_reward: 30.664, revenue_rate: 0.7691, distance: 9.3248, memory: -0.0473, power: 0.2843, lr: 0.000100, took: 76.737s
[COMPETITIVE] Epoch 1, Batch 1080/1563, loss: 5.030, reward: 30.919, critic_reward: 30.906, revenue_rate: 0.7562, distance: 9.1267, memory: -0.0480, power: 0.2769, lr: 0.000100, took: 75.540s
[COMPETITIVE] Epoch 1, Batch 1090/1563, loss: 2.128, reward: 31.026, critic_reward: 31.110, revenue_rate: 0.7574, distance: 9.1481, memory: -0.0478, power: 0.2773, lr: 0.000100, took: 75.660s
[COMPETITIVE] Epoch 1, Batch 1100/1563, loss: 3.315, reward: 31.956, critic_reward: 32.320, revenue_rate: 0.7867, distance: 9.6911, memory: -0.0300, power: 0.2940, lr: 0.000100, took: 79.971s
[COMPETITIVE] Epoch 1, Batch 1110/1563, loss: 4.346, reward: 33.197, critic_reward: 33.497, revenue_rate: 0.8178, distance: 10.3523, memory: -0.0166, power: 0.3118, lr: 0.000100, took: 86.044s
[COMPETITIVE] Epoch 1, Batch 1120/1563, loss: 3.587, reward: 31.392, critic_reward: 31.441, revenue_rate: 0.7697, distance: 9.4522, memory: -0.0403, power: 0.2858, lr: 0.000100, took: 77.466s
[COMPETITIVE] Epoch 1, Batch 1130/1563, loss: 3.858, reward: 29.412, critic_reward: 29.741, revenue_rate: 0.7167, distance: 8.6068, memory: -0.0588, power: 0.2619, lr: 0.000100, took: 70.148s
[COMPETITIVE] Epoch 1, Batch 1140/1563, loss: 4.632, reward: 31.206, critic_reward: 30.532, revenue_rate: 0.7650, distance: 9.3187, memory: -0.0418, power: 0.2825, lr: 0.000100, took: 76.832s
[COMPETITIVE] Epoch 1, Batch 1150/1563, loss: 6.969, reward: 33.952, critic_reward: 35.352, revenue_rate: 0.8398, distance: 10.7328, memory: -0.0021, power: 0.3220, lr: 0.000100, took: 89.670s
[COMPETITIVE] Epoch 1, Batch 1160/1563, loss: 6.267, reward: 33.131, critic_reward: 32.402, revenue_rate: 0.8137, distance: 10.1598, memory: -0.0181, power: 0.3091, lr: 0.000100, took: 86.671s
[COMPETITIVE] Epoch 1, Batch 1170/1563, loss: 5.887, reward: 31.928, critic_reward: 31.618, revenue_rate: 0.7828, distance: 9.6529, memory: -0.0276, power: 0.2934, lr: 0.000100, took: 79.782s
[COMPETITIVE] Epoch 1, Batch 1180/1563, loss: 4.889, reward: 32.012, critic_reward: 32.458, revenue_rate: 0.7876, distance: 9.7022, memory: -0.0279, power: 0.2938, lr: 0.000100, took: 80.372s
[COMPETITIVE] Epoch 1, Batch 1190/1563, loss: 8.867, reward: 32.945, critic_reward: 31.090, revenue_rate: 0.8083, distance: 10.0646, memory: -0.0247, power: 0.3052, lr: 0.000100, took: 83.769s
[COMPETITIVE] Epoch 1, Batch 1200/1563, loss: 4.176, reward: 32.265, critic_reward: 32.360, revenue_rate: 0.7952, distance: 9.8494, memory: -0.0243, power: 0.3014, lr: 0.000100, took: 82.651s
[COMPETITIVE] Epoch 1, Batch 1210/1563, loss: 3.839, reward: 31.507, critic_reward: 31.261, revenue_rate: 0.7704, distance: 9.4436, memory: -0.0387, power: 0.2838, lr: 0.000100, took: 77.287s
[COMPETITIVE] Epoch 1, Batch 1220/1563, loss: 3.139, reward: 31.521, critic_reward: 31.832, revenue_rate: 0.7709, distance: 9.3461, memory: -0.0458, power: 0.2848, lr: 0.000100, took: 76.976s
[COMPETITIVE] Epoch 1, Batch 1230/1563, loss: 2.869, reward: 31.026, critic_reward: 31.266, revenue_rate: 0.7593, distance: 9.1677, memory: -0.0465, power: 0.2777, lr: 0.000100, took: 76.349s
[COMPETITIVE] Epoch 1, Batch 1240/1563, loss: 3.373, reward: 31.437, critic_reward: 31.473, revenue_rate: 0.7724, distance: 9.5163, memory: -0.0367, power: 0.2867, lr: 0.000100, took: 77.817s
[COMPETITIVE] Epoch 1, Batch 1250/1563, loss: 2.996, reward: 30.485, critic_reward: 30.246, revenue_rate: 0.7472, distance: 9.0226, memory: -0.0406, power: 0.2746, lr: 0.000100, took: 74.791s
[COMPETITIVE] Epoch 1, Batch 1260/1563, loss: 4.974, reward: 31.644, critic_reward: 30.677, revenue_rate: 0.7773, distance: 9.4946, memory: -0.0314, power: 0.2881, lr: 0.000100, took: 79.134s
[COMPETITIVE] Epoch 1, Batch 1270/1563, loss: 6.078, reward: 31.868, critic_reward: 33.257, revenue_rate: 0.7809, distance: 9.5938, memory: -0.0262, power: 0.2904, lr: 0.000100, took: 80.767s
[COMPETITIVE] Epoch 1, Batch 1280/1563, loss: 5.055, reward: 31.520, critic_reward: 30.326, revenue_rate: 0.7744, distance: 9.4384, memory: -0.0284, power: 0.2872, lr: 0.000100, took: 78.575s
[COMPETITIVE] Epoch 1, Batch 1290/1563, loss: 4.600, reward: 33.363, critic_reward: 33.480, revenue_rate: 0.8200, distance: 10.2508, memory: -0.0089, power: 0.3115, lr: 0.000100, took: 85.621s
[COMPETITIVE] Epoch 1, Batch 1300/1563, loss: 3.305, reward: 33.735, critic_reward: 33.950, revenue_rate: 0.8322, distance: 10.4671, memory: -0.0086, power: 0.3173, lr: 0.000100, took: 87.370s
[COMPETITIVE] Epoch 1, Batch 1310/1563, loss: 2.561, reward: 33.294, critic_reward: 32.928, revenue_rate: 0.8208, distance: 10.3104, memory: -0.0147, power: 0.3122, lr: 0.000100, took: 85.842s
[COMPETITIVE] Epoch 1, Batch 1320/1563, loss: 3.552, reward: 33.164, critic_reward: 33.002, revenue_rate: 0.8150, distance: 10.1622, memory: -0.0186, power: 0.3048, lr: 0.000100, took: 84.300s
[COMPETITIVE] Epoch 1, Batch 1330/1563, loss: 3.309, reward: 32.395, critic_reward: 31.976, revenue_rate: 0.7964, distance: 9.8445, memory: -0.0275, power: 0.2981, lr: 0.000100, took: 81.547s
[COMPETITIVE] Epoch 1, Batch 1340/1563, loss: 3.650, reward: 32.846, critic_reward: 32.964, revenue_rate: 0.8091, distance: 10.1138, memory: -0.0252, power: 0.3044, lr: 0.000100, took: 83.597s
[COMPETITIVE] Epoch 1, Batch 1350/1563, loss: 2.950, reward: 33.353, critic_reward: 33.143, revenue_rate: 0.8207, distance: 10.2158, memory: -0.0165, power: 0.3111, lr: 0.000100, took: 84.816s
[COMPETITIVE] Epoch 1, Batch 1360/1563, loss: 4.159, reward: 32.619, critic_reward: 33.693, revenue_rate: 0.8006, distance: 9.8099, memory: -0.0318, power: 0.2984, lr: 0.000100, took: 82.489s
[COMPETITIVE] Epoch 1, Batch 1370/1563, loss: 3.497, reward: 29.255, critic_reward: 29.031, revenue_rate: 0.7158, distance: 8.5815, memory: -0.0632, power: 0.2609, lr: 0.000100, took: 70.358s
[COMPETITIVE] Epoch 1, Batch 1380/1563, loss: 4.683, reward: 26.670, critic_reward: 27.607, revenue_rate: 0.6498, distance: 7.6186, memory: -0.0927, power: 0.2330, lr: 0.000100, took: 62.144s
[COMPETITIVE] Epoch 1, Batch 1390/1563, loss: 4.167, reward: 26.748, critic_reward: 27.279, revenue_rate: 0.6510, distance: 7.6392, memory: -0.0936, power: 0.2327, lr: 0.000100, took: 63.561s
[COMPETITIVE] Epoch 1, Batch 1400/1563, loss: 3.834, reward: 28.833, critic_reward: 28.069, revenue_rate: 0.7043, distance: 8.3150, memory: -0.0704, power: 0.2546, lr: 0.000100, took: 68.066s
[COMPETITIVE] Epoch 1, Batch 1410/1563, loss: 3.322, reward: 31.418, critic_reward: 31.221, revenue_rate: 0.7683, distance: 9.3736, memory: -0.0467, power: 0.2840, lr: 0.000100, took: 77.391s
[COMPETITIVE] Epoch 1, Batch 1420/1563, loss: 4.258, reward: 32.631, critic_reward: 32.307, revenue_rate: 0.8029, distance: 9.9764, memory: -0.0212, power: 0.3000, lr: 0.000100, took: 82.689s
[COMPETITIVE] Epoch 1, Batch 1430/1563, loss: 7.002, reward: 32.375, critic_reward: 32.920, revenue_rate: 0.7968, distance: 9.8187, memory: -0.0218, power: 0.3016, lr: 0.000100, took: 82.284s
[COMPETITIVE] Epoch 1, Batch 1440/1563, loss: 2.669, reward: 32.627, critic_reward: 32.833, revenue_rate: 0.8033, distance: 10.0747, memory: -0.0158, power: 0.3050, lr: 0.000100, took: 83.334s
[COMPETITIVE] Epoch 1, Batch 1450/1563, loss: 3.394, reward: 31.970, critic_reward: 32.169, revenue_rate: 0.7902, distance: 9.8130, memory: -0.0338, power: 0.2971, lr: 0.000100, took: 81.029s
[COMPETITIVE] Epoch 1, Batch 1460/1563, loss: 6.364, reward: 32.614, critic_reward: 30.982, revenue_rate: 0.8045, distance: 9.9180, memory: -0.0223, power: 0.3018, lr: 0.000100, took: 82.658s
[COMPETITIVE] Epoch 1, Batch 1470/1563, loss: 3.494, reward: 32.383, critic_reward: 32.954, revenue_rate: 0.7963, distance: 9.8251, memory: -0.0274, power: 0.2998, lr: 0.000100, took: 81.681s
[COMPETITIVE] Epoch 1, Batch 1480/1563, loss: 3.091, reward: 31.365, critic_reward: 31.364, revenue_rate: 0.7685, distance: 9.3744, memory: -0.0429, power: 0.2869, lr: 0.000100, took: 77.383s
[COMPETITIVE] Epoch 1, Batch 1490/1563, loss: 3.271, reward: 32.341, critic_reward: 32.228, revenue_rate: 0.7969, distance: 9.7456, memory: -0.0240, power: 0.2980, lr: 0.000100, took: 82.173s
[COMPETITIVE] Epoch 1, Batch 1500/1563, loss: 3.103, reward: 32.006, critic_reward: 32.367, revenue_rate: 0.7861, distance: 9.6406, memory: -0.0359, power: 0.2915, lr: 0.000100, took: 81.349s
[COMPETITIVE] Epoch 1, Batch 1510/1563, loss: 3.147, reward: 30.530, critic_reward: 30.127, revenue_rate: 0.7467, distance: 8.8746, memory: -0.0478, power: 0.2712, lr: 0.000100, took: 73.349s
[COMPETITIVE] Epoch 1, Batch 1520/1563, loss: 3.144, reward: 31.820, critic_reward: 32.037, revenue_rate: 0.7778, distance: 9.3580, memory: -0.0373, power: 0.2848, lr: 0.000100, took: 77.777s
[COMPETITIVE] Epoch 1, Batch 1530/1563, loss: 2.639, reward: 32.395, critic_reward: 32.154, revenue_rate: 0.7946, distance: 9.7143, memory: -0.0225, power: 0.2960, lr: 0.000100, took: 80.265s
[COMPETITIVE] Epoch 1, Batch 1540/1563, loss: 3.945, reward: 32.446, critic_reward: 31.813, revenue_rate: 0.7994, distance: 9.7436, memory: -0.0284, power: 0.2968, lr: 0.000100, took: 81.328s
[COMPETITIVE] Epoch 1, Batch 1550/1563, loss: 4.183, reward: 31.956, critic_reward: 31.177, revenue_rate: 0.7847, distance: 9.5684, memory: -0.0252, power: 0.2896, lr: 0.000100, took: 79.585s
[COMPETITIVE] Epoch 1, Batch 1560/1563, loss: 5.575, reward: 31.543, critic_reward: 32.363, revenue_rate: 0.7728, distance: 9.3314, memory: -0.0441, power: 0.2834, lr: 0.000100, took: 76.949s
[COMPETITIVE] 开始验证...
[COMPETITIVE] 验证完成 - Epoch 1, reward: 31.368, revenue_rate: 0.7682, distance: 9.2485, memory: -0.0439, power: 0.2807
[COMPETITIVE] 已保存新模型到 constellation_smp\constellation_smp100\multi_constellation_comparison_2025_08_25_17_02_29\constellation_gpnindrnn_competitive_2025_08_26_05_01_59 (验证集奖励: 31.3683)
[COMPETITIVE] 开始训练 Epoch 2/3
[COMPETITIVE] Epoch 2, Batch 10/1563, loss: 6.515, reward: 31.742, critic_reward: 31.544, revenue_rate: 0.7781, distance: 9.4688, memory: -0.0305, power: 0.2885, lr: 0.000100, took: 84.659s
[COMPETITIVE] Epoch 2, Batch 20/1563, loss: 4.141, reward: 33.441, critic_reward: 33.235, revenue_rate: 0.8219, distance: 10.1727, memory: -0.0189, power: 0.3094, lr: 0.000100, took: 83.774s
[COMPETITIVE] Epoch 2, Batch 30/1563, loss: 3.352, reward: 33.857, critic_reward: 33.875, revenue_rate: 0.8336, distance: 10.4645, memory: -0.0092, power: 0.3129, lr: 0.000100, took: 87.997s
[COMPETITIVE] Epoch 2, Batch 40/1563, loss: 3.542, reward: 34.654, critic_reward: 35.133, revenue_rate: 0.8534, distance: 10.7867, memory: 0.0054, power: 0.3282, lr: 0.000100, took: 91.291s
[COMPETITIVE] Epoch 2, Batch 50/1563, loss: 3.223, reward: 34.527, critic_reward: 34.362, revenue_rate: 0.8486, distance: 10.6723, memory: -0.0056, power: 0.3235, lr: 0.000100, took: 91.534s
[COMPETITIVE] Epoch 2, Batch 60/1563, loss: 2.716, reward: 32.266, critic_reward: 32.543, revenue_rate: 0.7949, distance: 9.9677, memory: -0.0199, power: 0.3002, lr: 0.000100, took: 84.293s
[COMPETITIVE] Epoch 2, Batch 70/1563, loss: 2.814, reward: 31.586, critic_reward: 31.302, revenue_rate: 0.7726, distance: 9.3232, memory: -0.0453, power: 0.2825, lr: 0.000100, took: 78.178s
[COMPETITIVE] Epoch 2, Batch 80/1563, loss: 4.685, reward: 31.027, critic_reward: 29.944, revenue_rate: 0.7609, distance: 9.2632, memory: -0.0459, power: 0.2796, lr: 0.000100, took: 76.612s
[COMPETITIVE] Epoch 2, Batch 90/1563, loss: 8.283, reward: 30.998, critic_reward: 32.606, revenue_rate: 0.7576, distance: 9.1590, memory: -0.0490, power: 0.2798, lr: 0.000100, took: 78.229s
[COMPETITIVE] Epoch 2, Batch 100/1563, loss: 6.335, reward: 31.285, critic_reward: 30.275, revenue_rate: 0.7675, distance: 9.2505, memory: -0.0466, power: 0.2813, lr: 0.000100, took: 77.610s
[COMPETITIVE] Epoch 2, Batch 110/1563, loss: 3.759, reward: 31.541, critic_reward: 31.466, revenue_rate: 0.7703, distance: 9.3883, memory: -0.0380, power: 0.2841, lr: 0.000100, took: 74.125s
[COMPETITIVE] Epoch 2, Batch 120/1563, loss: 3.511, reward: 32.228, critic_reward: 31.666, revenue_rate: 0.7917, distance: 9.6587, memory: -0.0279, power: 0.2943, lr: 0.000100, took: 78.129s
[COMPETITIVE] Epoch 2, Batch 130/1563, loss: 3.347, reward: 31.612, critic_reward: 32.371, revenue_rate: 0.7757, distance: 9.3617, memory: -0.0441, power: 0.2850, lr: 0.000100, took: 75.211s
[COMPETITIVE] Epoch 2, Batch 140/1563, loss: 2.205, reward: 30.323, critic_reward: 30.467, revenue_rate: 0.7418, distance: 8.9769, memory: -0.0486, power: 0.2732, lr: 0.000100, took: 76.920s
[COMPETITIVE] Epoch 2, Batch 150/1563, loss: 2.777, reward: 30.133, critic_reward: 30.325, revenue_rate: 0.7380, distance: 8.8311, memory: -0.0549, power: 0.2704, lr: 0.000100, took: 72.287s
[COMPETITIVE] Epoch 2, Batch 160/1563, loss: 3.266, reward: 31.056, critic_reward: 30.675, revenue_rate: 0.7582, distance: 9.1266, memory: -0.0482, power: 0.2803, lr: 0.000100, took: 75.209s
[COMPETITIVE] Epoch 2, Batch 170/1563, loss: 3.498, reward: 32.337, critic_reward: 32.697, revenue_rate: 0.7929, distance: 9.6424, memory: -0.0350, power: 0.2933, lr: 0.000100, took: 82.060s
[COMPETITIVE] Epoch 2, Batch 180/1563, loss: 2.346, reward: 33.260, critic_reward: 33.101, revenue_rate: 0.8189, distance: 10.1239, memory: -0.0147, power: 0.3093, lr: 0.000100, took: 85.962s
[COMPETITIVE] Epoch 2, Batch 190/1563, loss: 5.611, reward: 33.149, critic_reward: 34.425, revenue_rate: 0.8147, distance: 10.1335, memory: -0.0226, power: 0.3028, lr: 0.000100, took: 82.387s
[COMPETITIVE] Epoch 2, Batch 200/1563, loss: 3.232, reward: 33.614, critic_reward: 33.170, revenue_rate: 0.8289, distance: 10.3596, memory: -0.0092, power: 0.3145, lr: 0.000100, took: 85.225s
[COMPETITIVE] Epoch 2, Batch 210/1563, loss: 6.893, reward: 34.518, critic_reward: 35.716, revenue_rate: 0.8496, distance: 10.8059, memory: 0.0051, power: 0.3277, lr: 0.000100, took: 91.775s
[COMPETITIVE] Epoch 2, Batch 220/1563, loss: 2.585, reward: 33.392, critic_reward: 33.159, revenue_rate: 0.8203, distance: 10.3135, memory: -0.0146, power: 0.3129, lr: 0.000100, took: 84.093s
[COMPETITIVE] Epoch 2, Batch 230/1563, loss: 2.638, reward: 32.990, critic_reward: 33.105, revenue_rate: 0.8117, distance: 10.1112, memory: -0.0289, power: 0.3050, lr: 0.000100, took: 82.574s
[COMPETITIVE] Epoch 2, Batch 240/1563, loss: 3.186, reward: 32.635, critic_reward: 32.573, revenue_rate: 0.8013, distance: 9.8735, memory: -0.0293, power: 0.3018, lr: 0.000100, took: 81.417s
[COMPETITIVE] Epoch 2, Batch 250/1563, loss: 3.035, reward: 32.106, critic_reward: 32.158, revenue_rate: 0.7897, distance: 9.6418, memory: -0.0286, power: 0.2919, lr: 0.000100, took: 78.940s
[COMPETITIVE] Epoch 2, Batch 260/1563, loss: 8.073, reward: 33.726, critic_reward: 34.945, revenue_rate: 0.8311, distance: 10.3536, memory: -0.0097, power: 0.3136, lr: 0.000100, took: 85.779s
[COMPETITIVE] Epoch 2, Batch 270/1563, loss: 5.078, reward: 34.145, critic_reward: 33.654, revenue_rate: 0.8410, distance: 10.6741, memory: -0.0101, power: 0.3217, lr: 0.000100, took: 87.860s
[COMPETITIVE] Epoch 2, Batch 280/1563, loss: 2.811, reward: 33.581, critic_reward: 34.109, revenue_rate: 0.8277, distance: 10.3356, memory: -0.0137, power: 0.3128, lr: 0.000100, took: 86.256s
[COMPETITIVE] Epoch 2, Batch 290/1563, loss: 3.724, reward: 32.601, critic_reward: 32.142, revenue_rate: 0.8008, distance: 9.9334, memory: -0.0245, power: 0.2984, lr: 0.000100, took: 80.796s
[COMPETITIVE] Epoch 2, Batch 300/1563, loss: 4.385, reward: 33.092, critic_reward: 32.677, revenue_rate: 0.8119, distance: 9.9919, memory: -0.0215, power: 0.3054, lr: 0.000100, took: 84.766s
[COMPETITIVE] Epoch 2, Batch 310/1563, loss: 3.667, reward: 32.215, critic_reward: 32.335, revenue_rate: 0.7912, distance: 9.7693, memory: -0.0294, power: 0.2959, lr: 0.000100, took: 81.217s
[COMPETITIVE] Epoch 2, Batch 320/1563, loss: 3.547, reward: 30.970, critic_reward: 30.894, revenue_rate: 0.7590, distance: 9.3128, memory: -0.0414, power: 0.2819, lr: 0.000100, took: 77.921s
[COMPETITIVE] Epoch 2, Batch 330/1563, loss: 3.013, reward: 31.249, critic_reward: 31.724, revenue_rate: 0.7659, distance: 9.3464, memory: -0.0359, power: 0.2837, lr: 0.000100, took: 77.942s
[COMPETITIVE] Epoch 2, Batch 340/1563, loss: 2.364, reward: 31.136, critic_reward: 30.844, revenue_rate: 0.7626, distance: 9.2832, memory: -0.0451, power: 0.2825, lr: 0.000100, took: 75.599s
[COMPETITIVE] Epoch 2, Batch 350/1563, loss: 2.426, reward: 32.242, critic_reward: 32.298, revenue_rate: 0.7910, distance: 9.7456, memory: -0.0347, power: 0.2942, lr: 0.000100, took: 79.407s
[COMPETITIVE] Epoch 2, Batch 360/1563, loss: 3.451, reward: 33.324, critic_reward: 32.936, revenue_rate: 0.8176, distance: 10.1248, memory: -0.0192, power: 0.3080, lr: 0.000100, took: 83.170s
[COMPETITIVE] Epoch 2, Batch 370/1563, loss: 2.544, reward: 33.232, critic_reward: 33.519, revenue_rate: 0.8194, distance: 10.1604, memory: -0.0153, power: 0.3067, lr: 0.000100, took: 83.407s
[COMPETITIVE] Epoch 2, Batch 380/1563, loss: 4.207, reward: 33.503, critic_reward: 32.603, revenue_rate: 0.8225, distance: 10.1789, memory: -0.0122, power: 0.3112, lr: 0.000100, took: 84.141s
[COMPETITIVE] Epoch 2, Batch 390/1563, loss: 2.979, reward: 34.313, critic_reward: 34.434, revenue_rate: 0.8476, distance: 10.6696, memory: -0.0012, power: 0.3233, lr: 0.000100, took: 88.384s
[COMPETITIVE] Epoch 2, Batch 400/1563, loss: 3.787, reward: 34.345, critic_reward: 34.568, revenue_rate: 0.8483, distance: 10.6789, memory: -0.0025, power: 0.3256, lr: 0.000100, took: 88.199s
[COMPETITIVE] Epoch 2, Batch 410/1563, loss: 2.855, reward: 34.089, critic_reward: 34.443, revenue_rate: 0.8372, distance: 10.4801, memory: -0.0032, power: 0.3189, lr: 0.000100, took: 86.754s
[COMPETITIVE] Epoch 2, Batch 420/1563, loss: 2.639, reward: 32.790, critic_reward: 32.378, revenue_rate: 0.8053, distance: 9.8445, memory: -0.0261, power: 0.3011, lr: 0.000100, took: 81.207s
[COMPETITIVE] Epoch 2, Batch 430/1563, loss: 3.357, reward: 31.706, critic_reward: 32.558, revenue_rate: 0.7786, distance: 9.4679, memory: -0.0438, power: 0.2883, lr: 0.000100, took: 79.488s
[COMPETITIVE] Epoch 2, Batch 440/1563, loss: 2.874, reward: 31.222, critic_reward: 30.880, revenue_rate: 0.7657, distance: 9.2911, memory: -0.0470, power: 0.2812, lr: 0.000100, took: 75.435s
[COMPETITIVE] Epoch 2, Batch 450/1563, loss: 3.749, reward: 32.490, critic_reward: 32.537, revenue_rate: 0.7939, distance: 9.7530, memory: -0.0360, power: 0.2943, lr: 0.000100, took: 79.528s
[COMPETITIVE] Epoch 2, Batch 460/1563, loss: 3.570, reward: 31.803, critic_reward: 31.556, revenue_rate: 0.7823, distance: 9.5402, memory: -0.0334, power: 0.2912, lr: 0.000100, took: 78.397s
[COMPETITIVE] Epoch 2, Batch 470/1563, loss: 3.329, reward: 32.052, critic_reward: 31.532, revenue_rate: 0.7867, distance: 9.6727, memory: -0.0228, power: 0.2970, lr: 0.000100, took: 81.418s
[COMPETITIVE] Epoch 2, Batch 480/1563, loss: 4.514, reward: 31.922, critic_reward: 31.979, revenue_rate: 0.7837, distance: 9.5274, memory: -0.0265, power: 0.2908, lr: 0.000100, took: 84.803s
[COMPETITIVE] Epoch 2, Batch 490/1563, loss: 2.725, reward: 31.793, critic_reward: 32.091, revenue_rate: 0.7781, distance: 9.4851, memory: -0.0353, power: 0.2899, lr: 0.000100, took: 86.573s
[COMPETITIVE] Epoch 2, Batch 500/1563, loss: 4.020, reward: 30.406, critic_reward: 30.054, revenue_rate: 0.7443, distance: 8.9837, memory: -0.0554, power: 0.2699, lr: 0.000100, took: 81.038s
[COMPETITIVE] Epoch 2, Batch 510/1563, loss: 4.852, reward: 31.351, critic_reward: 30.558, revenue_rate: 0.7661, distance: 9.3775, memory: -0.0475, power: 0.2828, lr: 0.000100, took: 82.436s
[COMPETITIVE] Epoch 2, Batch 520/1563, loss: 10.704, reward: 31.999, critic_reward: 34.071, revenue_rate: 0.7850, distance: 9.6250, memory: -0.0356, power: 0.2912, lr: 0.000100, took: 87.991s
[COMPETITIVE] Epoch 2, Batch 530/1563, loss: 2.356, reward: 31.562, critic_reward: 31.730, revenue_rate: 0.7715, distance: 9.3604, memory: -0.0367, power: 0.2836, lr: 0.000100, took: 86.336s
[COMPETITIVE] Epoch 2, Batch 540/1563, loss: 2.897, reward: 30.932, critic_reward: 31.131, revenue_rate: 0.7558, distance: 9.0920, memory: -0.0491, power: 0.2761, lr: 0.000100, took: 85.304s
[COMPETITIVE] Epoch 2, Batch 550/1563, loss: 7.483, reward: 29.026, critic_reward: 27.844, revenue_rate: 0.7104, distance: 8.4069, memory: -0.0620, power: 0.2566, lr: 0.000100, took: 76.717s
[COMPETITIVE] Epoch 2, Batch 560/1563, loss: 5.224, reward: 29.870, critic_reward: 29.706, revenue_rate: 0.7269, distance: 8.6664, memory: -0.0602, power: 0.2639, lr: 0.000100, took: 76.685s
[COMPETITIVE] Epoch 2, Batch 570/1563, loss: 4.279, reward: 31.460, critic_reward: 31.111, revenue_rate: 0.7711, distance: 9.3453, memory: -0.0279, power: 0.2868, lr: 0.000100, took: 83.898s
[COMPETITIVE] Epoch 2, Batch 580/1563, loss: 3.626, reward: 32.864, critic_reward: 33.523, revenue_rate: 0.8089, distance: 10.0112, memory: -0.0133, power: 0.3032, lr: 0.000100, took: 90.219s
[COMPETITIVE] Epoch 2, Batch 590/1563, loss: 2.901, reward: 32.750, critic_reward: 32.527, revenue_rate: 0.8048, distance: 9.8154, memory: -0.0263, power: 0.2983, lr: 0.000100, took: 86.721s
[COMPETITIVE] Epoch 2, Batch 600/1563, loss: 2.487, reward: 33.408, critic_reward: 32.906, revenue_rate: 0.8183, distance: 9.9949, memory: -0.0156, power: 0.3035, lr: 0.000100, took: 91.572s
[COMPETITIVE] Epoch 2, Batch 610/1563, loss: 4.373, reward: 31.739, critic_reward: 33.098, revenue_rate: 0.7781, distance: 9.3070, memory: -0.0373, power: 0.2852, lr: 0.000100, took: 84.328s
[COMPETITIVE] Epoch 2, Batch 620/1563, loss: 3.457, reward: 31.399, critic_reward: 30.472, revenue_rate: 0.7687, distance: 9.2845, memory: -0.0409, power: 0.2802, lr: 0.000100, took: 82.703s
[COMPETITIVE] Epoch 2, Batch 630/1563, loss: 3.595, reward: 32.521, critic_reward: 33.216, revenue_rate: 0.7975, distance: 9.7071, memory: -0.0263, power: 0.2956, lr: 0.000100, took: 83.785s
[COMPETITIVE] Epoch 2, Batch 640/1563, loss: 2.254, reward: 32.996, critic_reward: 32.832, revenue_rate: 0.8121, distance: 10.0766, memory: -0.0197, power: 0.3063, lr: 0.000100, took: 89.073s
[COMPETITIVE] Epoch 2, Batch 650/1563, loss: 2.618, reward: 33.454, critic_reward: 33.704, revenue_rate: 0.8247, distance: 10.2386, memory: -0.0203, power: 0.3090, lr: 0.000100, took: 94.831s
[COMPETITIVE] Epoch 2, Batch 660/1563, loss: 3.120, reward: 32.790, critic_reward: 32.481, revenue_rate: 0.8080, distance: 9.9235, memory: -0.0238, power: 0.3017, lr: 0.000100, took: 88.070s
[COMPETITIVE] Epoch 2, Batch 670/1563, loss: 3.119, reward: 33.417, critic_reward: 33.226, revenue_rate: 0.8221, distance: 10.2062, memory: -0.0207, power: 0.3076, lr: 0.000100, took: 88.543s
[COMPETITIVE] Epoch 2, Batch 680/1563, loss: 2.745, reward: 33.164, critic_reward: 33.481, revenue_rate: 0.8124, distance: 9.9322, memory: -0.0213, power: 0.3048, lr: 0.000100, took: 90.336s
[COMPETITIVE] Epoch 2, Batch 690/1563, loss: 3.189, reward: 32.748, critic_reward: 32.796, revenue_rate: 0.8062, distance: 9.9453, memory: -0.0291, power: 0.2972, lr: 0.000100, took: 86.983s
[COMPETITIVE] Epoch 2, Batch 700/1563, loss: 2.720, reward: 32.698, critic_reward: 33.087, revenue_rate: 0.8005, distance: 9.7824, memory: -0.0290, power: 0.2968, lr: 0.000100, took: 87.117s
[COMPETITIVE] Epoch 2, Batch 710/1563, loss: 2.838, reward: 32.039, critic_reward: 31.597, revenue_rate: 0.7810, distance: 9.3786, memory: -0.0360, power: 0.2866, lr: 0.000100, took: 84.780s
[COMPETITIVE] Epoch 2, Batch 720/1563, loss: 5.125, reward: 30.985, critic_reward: 31.269, revenue_rate: 0.7589, distance: 9.0800, memory: -0.0493, power: 0.2764, lr: 0.000100, took: 76.005s
[COMPETITIVE] Epoch 2, Batch 730/1563, loss: 3.357, reward: 32.572, critic_reward: 32.508, revenue_rate: 0.8002, distance: 9.7226, memory: -0.0310, power: 0.2981, lr: 0.000100, took: 85.874s
[COMPETITIVE] Epoch 2, Batch 740/1563, loss: 3.096, reward: 34.618, critic_reward: 34.253, revenue_rate: 0.8543, distance: 10.6476, memory: 0.0006, power: 0.3262, lr: 0.000100, took: 94.752s
[COMPETITIVE] Epoch 2, Batch 750/1563, loss: 3.558, reward: 35.137, critic_reward: 34.823, revenue_rate: 0.8695, distance: 11.0253, memory: 0.0139, power: 0.3341, lr: 0.000100, took: 101.884s
[COMPETITIVE] Epoch 2, Batch 760/1563, loss: 2.416, reward: 33.657, critic_reward: 33.521, revenue_rate: 0.8264, distance: 10.2459, memory: -0.0125, power: 0.3114, lr: 0.000100, took: 92.822s
[COMPETITIVE] Epoch 2, Batch 770/1563, loss: 4.635, reward: 31.839, critic_reward: 32.688, revenue_rate: 0.7798, distance: 9.4638, memory: -0.0467, power: 0.2894, lr: 0.000100, took: 80.234s
[COMPETITIVE] Epoch 2, Batch 780/1563, loss: 2.127, reward: 32.170, critic_reward: 32.307, revenue_rate: 0.7900, distance: 9.5768, memory: -0.0350, power: 0.2921, lr: 0.000100, took: 74.996s
[COMPETITIVE] Epoch 2, Batch 790/1563, loss: 4.538, reward: 32.677, critic_reward: 31.857, revenue_rate: 0.7988, distance: 9.7410, memory: -0.0327, power: 0.2970, lr: 0.000100, took: 76.013s
[COMPETITIVE] Epoch 2, Batch 800/1563, loss: 5.792, reward: 32.684, critic_reward: 32.331, revenue_rate: 0.8017, distance: 9.8196, memory: -0.0297, power: 0.2987, lr: 0.000100, took: 77.581s
[COMPETITIVE] Epoch 2, Batch 810/1563, loss: 4.488, reward: 32.664, critic_reward: 32.168, revenue_rate: 0.8043, distance: 9.8192, memory: -0.0183, power: 0.2986, lr: 0.000100, took: 82.271s
[COMPETITIVE] Epoch 2, Batch 820/1563, loss: 3.484, reward: 32.680, critic_reward: 33.284, revenue_rate: 0.8036, distance: 9.8458, memory: -0.0326, power: 0.2971, lr: 0.000100, took: 82.904s
[COMPETITIVE] Epoch 2, Batch 830/1563, loss: 2.210, reward: 32.484, critic_reward: 32.204, revenue_rate: 0.7985, distance: 9.8884, memory: -0.0237, power: 0.2979, lr: 0.000100, took: 82.813s
[COMPETITIVE] Epoch 2, Batch 840/1563, loss: 2.479, reward: 34.034, critic_reward: 34.064, revenue_rate: 0.8372, distance: 10.5450, memory: -0.0059, power: 0.3167, lr: 0.000100, took: 88.168s
[COMPETITIVE] Epoch 2, Batch 850/1563, loss: 5.490, reward: 35.038, critic_reward: 34.611, revenue_rate: 0.8620, distance: 10.7999, memory: -0.0106, power: 0.3272, lr: 0.000100, took: 91.564s
[COMPETITIVE] Epoch 2, Batch 860/1563, loss: 3.586, reward: 33.742, critic_reward: 34.256, revenue_rate: 0.8297, distance: 10.2821, memory: -0.0103, power: 0.3126, lr: 0.000100, took: 88.424s
[COMPETITIVE] Epoch 2, Batch 870/1563, loss: 3.214, reward: 33.009, critic_reward: 33.310, revenue_rate: 0.8116, distance: 9.9425, memory: -0.0280, power: 0.3038, lr: 0.000100, took: 82.961s
[COMPETITIVE] Epoch 2, Batch 880/1563, loss: 4.088, reward: 33.280, critic_reward: 32.800, revenue_rate: 0.8132, distance: 9.9827, memory: -0.0273, power: 0.3023, lr: 0.000100, took: 84.151s
[COMPETITIVE] Epoch 2, Batch 890/1563, loss: 3.210, reward: 33.766, critic_reward: 33.722, revenue_rate: 0.8323, distance: 10.4304, memory: -0.0043, power: 0.3156, lr: 0.000100, took: 87.626s
[COMPETITIVE] Epoch 2, Batch 900/1563, loss: 5.125, reward: 33.798, critic_reward: 34.530, revenue_rate: 0.8367, distance: 10.5917, memory: -0.0003, power: 0.3175, lr: 0.000100, took: 89.361s
[COMPETITIVE] Epoch 2, Batch 910/1563, loss: 4.819, reward: 33.981, critic_reward: 34.240, revenue_rate: 0.8380, distance: 10.5147, memory: -0.0091, power: 0.3184, lr: 0.000100, took: 89.026s
[COMPETITIVE] Epoch 2, Batch 920/1563, loss: 2.364, reward: 32.750, critic_reward: 33.167, revenue_rate: 0.8078, distance: 9.9506, memory: -0.0150, power: 0.3014, lr: 0.000100, took: 83.844s
[COMPETITIVE] Epoch 2, Batch 930/1563, loss: 3.265, reward: 31.148, critic_reward: 31.404, revenue_rate: 0.7603, distance: 9.1007, memory: -0.0552, power: 0.2776, lr: 0.000100, took: 76.256s
[COMPETITIVE] Epoch 2, Batch 940/1563, loss: 5.244, reward: 30.665, critic_reward: 29.339, revenue_rate: 0.7505, distance: 8.9721, memory: -0.0481, power: 0.2730, lr: 0.000100, took: 73.709s
[COMPETITIVE] Epoch 2, Batch 950/1563, loss: 4.375, reward: 32.376, critic_reward: 32.728, revenue_rate: 0.7936, distance: 9.7473, memory: -0.0289, power: 0.2942, lr: 0.000100, took: 81.228s
[COMPETITIVE] Epoch 2, Batch 960/1563, loss: 3.655, reward: 33.325, critic_reward: 33.506, revenue_rate: 0.8264, distance: 10.3553, memory: -0.0026, power: 0.3127, lr: 0.000100, took: 88.363s
[COMPETITIVE] Epoch 2, Batch 970/1563, loss: 2.399, reward: 32.355, critic_reward: 32.453, revenue_rate: 0.7960, distance: 9.7595, memory: -0.0248, power: 0.2944, lr: 0.000100, took: 81.119s
[COMPETITIVE] Epoch 2, Batch 980/1563, loss: 2.886, reward: 30.937, critic_reward: 31.331, revenue_rate: 0.7568, distance: 9.1234, memory: -0.0424, power: 0.2781, lr: 0.000100, took: 75.163s
[COMPETITIVE] Epoch 2, Batch 990/1563, loss: 2.448, reward: 30.848, critic_reward: 30.813, revenue_rate: 0.7536, distance: 9.2009, memory: -0.0453, power: 0.2817, lr: 0.000100, took: 76.415s
[COMPETITIVE] Epoch 2, Batch 1000/1563, loss: 2.541, reward: 31.057, critic_reward: 31.265, revenue_rate: 0.7627, distance: 9.3250, memory: -0.0338, power: 0.2832, lr: 0.000100, took: 77.998s
[COMPETITIVE] Epoch 2, Batch 1010/1563, loss: 2.919, reward: 31.713, critic_reward: 32.159, revenue_rate: 0.7777, distance: 9.4894, memory: -0.0340, power: 0.2882, lr: 0.000100, took: 79.137s
[COMPETITIVE] Epoch 2, Batch 1020/1563, loss: 2.622, reward: 32.179, critic_reward: 31.783, revenue_rate: 0.7868, distance: 9.5857, memory: -0.0357, power: 0.2906, lr: 0.000100, took: 80.157s
[COMPETITIVE] Epoch 2, Batch 1030/1563, loss: 4.341, reward: 32.237, critic_reward: 32.747, revenue_rate: 0.7928, distance: 9.7590, memory: -0.0291, power: 0.2966, lr: 0.000100, took: 80.980s
[COMPETITIVE] Epoch 2, Batch 1040/1563, loss: 5.255, reward: 32.796, critic_reward: 32.281, revenue_rate: 0.8073, distance: 10.0284, memory: -0.0204, power: 0.3026, lr: 0.000100, took: 83.574s
[COMPETITIVE] Epoch 2, Batch 1050/1563, loss: 8.190, reward: 32.002, critic_reward: 32.777, revenue_rate: 0.7856, distance: 9.9006, memory: -0.0077, power: 0.2990, lr: 0.000100, took: 82.501s
[COMPETITIVE] Epoch 2, Batch 1060/1563, loss: 7.380, reward: 33.654, critic_reward: 31.867, revenue_rate: 0.8303, distance: 10.3889, memory: -0.0101, power: 0.3168, lr: 0.000100, took: 88.169s
[COMPETITIVE] Epoch 2, Batch 1070/1563, loss: 5.598, reward: 33.362, critic_reward: 34.255, revenue_rate: 0.8203, distance: 10.3018, memory: -0.0161, power: 0.3128, lr: 0.000100, took: 89.794s
[COMPETITIVE] Epoch 2, Batch 1080/1563, loss: 2.875, reward: 33.702, critic_reward: 33.857, revenue_rate: 0.8282, distance: 10.2366, memory: -0.0251, power: 0.3106, lr: 0.000100, took: 85.536s
[COMPETITIVE] Epoch 2, Batch 1090/1563, loss: 5.724, reward: 31.000, critic_reward: 32.521, revenue_rate: 0.7582, distance: 9.1090, memory: -0.0419, power: 0.2759, lr: 0.000100, took: 74.295s
[COMPETITIVE] Epoch 2, Batch 1100/1563, loss: 7.573, reward: 31.980, critic_reward: 29.886, revenue_rate: 0.7873, distance: 9.5733, memory: -0.0415, power: 0.2894, lr: 0.000100, took: 79.388s
[COMPETITIVE] Epoch 2, Batch 1110/1563, loss: 4.978, reward: 30.941, critic_reward: 32.333, revenue_rate: 0.7541, distance: 9.0382, memory: -0.0538, power: 0.2746, lr: 0.000100, took: 74.512s
[COMPETITIVE] Epoch 2, Batch 1120/1563, loss: 3.780, reward: 30.943, critic_reward: 30.374, revenue_rate: 0.7585, distance: 9.0873, memory: -0.0523, power: 0.2761, lr: 0.000100, took: 74.587s
[COMPETITIVE] Epoch 2, Batch 1130/1563, loss: 4.141, reward: 30.123, critic_reward: 31.116, revenue_rate: 0.7386, distance: 8.9003, memory: -0.0564, power: 0.2707, lr: 0.000100, took: 73.545s
[COMPETITIVE] Epoch 2, Batch 1140/1563, loss: 4.217, reward: 29.816, critic_reward: 29.065, revenue_rate: 0.7301, distance: 8.8574, memory: -0.0600, power: 0.2665, lr: 0.000100, took: 72.609s
[COMPETITIVE] Epoch 2, Batch 1150/1563, loss: 5.449, reward: 32.292, critic_reward: 30.954, revenue_rate: 0.7931, distance: 9.7075, memory: -0.0338, power: 0.2967, lr: 0.000100, took: 81.111s
[COMPETITIVE] Epoch 2, Batch 1160/1563, loss: 4.177, reward: 32.880, critic_reward: 34.078, revenue_rate: 0.8083, distance: 10.0376, memory: -0.0134, power: 0.3037, lr: 0.000100, took: 83.748s
[COMPETITIVE] Epoch 2, Batch 1170/1563, loss: 3.106, reward: 33.307, critic_reward: 33.042, revenue_rate: 0.8167, distance: 10.0577, memory: -0.0304, power: 0.3054, lr: 0.000100, took: 84.087s
[COMPETITIVE] Epoch 2, Batch 1180/1563, loss: 3.847, reward: 32.075, critic_reward: 32.369, revenue_rate: 0.7842, distance: 9.4935, memory: -0.0413, power: 0.2881, lr: 0.000100, took: 79.162s
[COMPETITIVE] Epoch 2, Batch 1190/1563, loss: 2.471, reward: 31.469, critic_reward: 31.340, revenue_rate: 0.7720, distance: 9.3843, memory: -0.0385, power: 0.2841, lr: 0.000100, took: 79.017s
[COMPETITIVE] Epoch 2, Batch 1200/1563, loss: 4.418, reward: 31.590, critic_reward: 32.247, revenue_rate: 0.7733, distance: 9.3153, memory: -0.0403, power: 0.2825, lr: 0.000100, took: 77.350s
[COMPETITIVE] Epoch 2, Batch 1210/1563, loss: 4.228, reward: 33.444, critic_reward: 32.244, revenue_rate: 0.8230, distance: 10.2198, memory: -0.0097, power: 0.3085, lr: 0.000100, took: 85.884s
[COMPETITIVE] Epoch 2, Batch 1220/1563, loss: 5.732, reward: 33.266, critic_reward: 34.913, revenue_rate: 0.8171, distance: 10.0647, memory: -0.0194, power: 0.3079, lr: 0.000100, took: 84.497s
[COMPETITIVE] Epoch 2, Batch 1230/1563, loss: 3.176, reward: 32.431, critic_reward: 32.340, revenue_rate: 0.7967, distance: 9.7269, memory: -0.0294, power: 0.2940, lr: 0.000100, took: 81.123s
[COMPETITIVE] Epoch 2, Batch 1240/1563, loss: 2.694, reward: 31.370, critic_reward: 31.340, revenue_rate: 0.7689, distance: 9.2332, memory: -0.0449, power: 0.2811, lr: 0.000100, took: 76.968s
[COMPETITIVE] Epoch 2, Batch 1250/1563, loss: 2.283, reward: 31.743, critic_reward: 31.795, revenue_rate: 0.7777, distance: 9.3365, memory: -0.0399, power: 0.2837, lr: 0.000100, took: 78.208s
[COMPETITIVE] Epoch 2, Batch 1260/1563, loss: 3.302, reward: 30.996, critic_reward: 31.528, revenue_rate: 0.7576, distance: 9.0753, memory: -0.0493, power: 0.2743, lr: 0.000100, took: 76.761s
[COMPETITIVE] Epoch 2, Batch 1270/1563, loss: 2.851, reward: 29.127, critic_reward: 28.864, revenue_rate: 0.7111, distance: 8.4500, memory: -0.0656, power: 0.2559, lr: 0.000100, took: 78.933s
[COMPETITIVE] Epoch 2, Batch 1280/1563, loss: 3.366, reward: 29.429, critic_reward: 28.709, revenue_rate: 0.7206, distance: 8.5477, memory: -0.0649, power: 0.2607, lr: 0.000100, took: 80.233s
[COMPETITIVE] Epoch 2, Batch 1290/1563, loss: 3.005, reward: 30.105, critic_reward: 30.206, revenue_rate: 0.7360, distance: 8.7724, memory: -0.0599, power: 0.2658, lr: 0.000100, took: 81.312s
[COMPETITIVE] Epoch 2, Batch 1300/1563, loss: 3.704, reward: 31.310, critic_reward: 30.638, revenue_rate: 0.7643, distance: 9.1829, memory: -0.0432, power: 0.2791, lr: 0.000100, took: 87.811s
[COMPETITIVE] Epoch 2, Batch 1310/1563, loss: 2.490, reward: 33.265, critic_reward: 33.135, revenue_rate: 0.8173, distance: 10.0379, memory: -0.0178, power: 0.3058, lr: 0.000100, took: 96.382s
[COMPETITIVE] Epoch 2, Batch 1320/1563, loss: 4.441, reward: 33.208, critic_reward: 33.720, revenue_rate: 0.8151, distance: 9.9642, memory: -0.0293, power: 0.3023, lr: 0.000100, took: 99.104s
[COMPETITIVE] Epoch 2, Batch 1330/1563, loss: 5.666, reward: 32.651, critic_reward: 31.689, revenue_rate: 0.8014, distance: 9.7615, memory: -0.0356, power: 0.2944, lr: 0.000100, took: 94.978s
[COMPETITIVE] Epoch 2, Batch 1340/1563, loss: 5.530, reward: 33.459, critic_reward: 33.097, revenue_rate: 0.8211, distance: 10.0326, memory: -0.0175, power: 0.3039, lr: 0.000100, took: 99.505s
[COMPETITIVE] Epoch 2, Batch 1350/1563, loss: 4.022, reward: 32.824, critic_reward: 33.150, revenue_rate: 0.8053, distance: 9.6475, memory: -0.0245, power: 0.2956, lr: 0.000100, took: 96.157s
[COMPETITIVE] Epoch 2, Batch 1360/1563, loss: 3.553, reward: 33.763, critic_reward: 33.889, revenue_rate: 0.8329, distance: 10.2053, memory: -0.0226, power: 0.3108, lr: 0.000100, took: 100.175s
[COMPETITIVE] Epoch 2, Batch 1370/1563, loss: 2.976, reward: 34.372, critic_reward: 34.129, revenue_rate: 0.8491, distance: 10.6184, memory: -0.0010, power: 0.3213, lr: 0.000100, took: 103.612s
[COMPETITIVE] Epoch 2, Batch 1380/1563, loss: 4.011, reward: 34.704, critic_reward: 34.589, revenue_rate: 0.8558, distance: 10.7299, memory: 0.0127, power: 0.3283, lr: 0.000100, took: 106.457s
[COMPETITIVE] Epoch 2, Batch 1390/1563, loss: 4.458, reward: 33.755, critic_reward: 33.932, revenue_rate: 0.8286, distance: 10.2782, memory: -0.0094, power: 0.3112, lr: 0.000100, took: 103.098s
[COMPETITIVE] Epoch 2, Batch 1400/1563, loss: 3.735, reward: 32.652, critic_reward: 32.803, revenue_rate: 0.8011, distance: 9.8128, memory: -0.0275, power: 0.2980, lr: 0.000100, took: 96.255s
[COMPETITIVE] Epoch 2, Batch 1410/1563, loss: 2.745, reward: 32.498, critic_reward: 32.542, revenue_rate: 0.7994, distance: 9.7818, memory: -0.0222, power: 0.2995, lr: 0.000100, took: 96.565s
[COMPETITIVE] Epoch 2, Batch 1420/1563, loss: 2.555, reward: 33.337, critic_reward: 33.284, revenue_rate: 0.8185, distance: 10.0665, memory: -0.0118, power: 0.3078, lr: 0.000100, took: 101.601s
[COMPETITIVE] Epoch 2, Batch 1430/1563, loss: 4.326, reward: 33.618, critic_reward: 33.399, revenue_rate: 0.8282, distance: 10.3003, memory: -0.0121, power: 0.3126, lr: 0.000100, took: 101.327s
[COMPETITIVE] Epoch 2, Batch 1440/1563, loss: 6.048, reward: 34.018, critic_reward: 34.608, revenue_rate: 0.8336, distance: 10.2603, memory: -0.0120, power: 0.3118, lr: 0.000100, took: 102.309s
[COMPETITIVE] Epoch 2, Batch 1450/1563, loss: 5.078, reward: 33.147, critic_reward: 33.766, revenue_rate: 0.8088, distance: 9.9695, memory: -0.0321, power: 0.3022, lr: 0.000100, took: 97.184s
[COMPETITIVE] Epoch 2, Batch 1460/1563, loss: 3.789, reward: 31.132, critic_reward: 32.252, revenue_rate: 0.7606, distance: 9.1458, memory: -0.0495, power: 0.2785, lr: 0.000100, took: 88.635s
[COMPETITIVE] Epoch 2, Batch 1470/1563, loss: 3.263, reward: 27.379, critic_reward: 27.863, revenue_rate: 0.6664, distance: 7.8212, memory: -0.0923, power: 0.2380, lr: 0.000100, took: 75.558s
[COMPETITIVE] Epoch 2, Batch 1480/1563, loss: 2.460, reward: 27.033, critic_reward: 26.887, revenue_rate: 0.6589, distance: 7.6952, memory: -0.0899, power: 0.2358, lr: 0.000100, took: 74.479s
[COMPETITIVE] Epoch 2, Batch 1490/1563, loss: 3.488, reward: 29.693, critic_reward: 28.965, revenue_rate: 0.7264, distance: 8.6750, memory: -0.0676, power: 0.2647, lr: 0.000100, took: 87.683s
[COMPETITIVE] Epoch 2, Batch 1500/1563, loss: 4.374, reward: 31.890, critic_reward: 32.433, revenue_rate: 0.7819, distance: 9.5319, memory: -0.0419, power: 0.2868, lr: 0.000100, took: 92.955s
[COMPETITIVE] Epoch 2, Batch 1510/1563, loss: 6.288, reward: 32.372, critic_reward: 32.130, revenue_rate: 0.7912, distance: 9.6380, memory: -0.0325, power: 0.2939, lr: 0.000100, took: 95.029s
[COMPETITIVE] Epoch 2, Batch 1520/1563, loss: 4.756, reward: 31.220, critic_reward: 31.576, revenue_rate: 0.7623, distance: 9.1422, memory: -0.0456, power: 0.2781, lr: 0.000100, took: 89.951s
[COMPETITIVE] Epoch 2, Batch 1530/1563, loss: 4.632, reward: 31.273, critic_reward: 32.078, revenue_rate: 0.7635, distance: 9.0771, memory: -0.0519, power: 0.2786, lr: 0.000100, took: 89.667s
[COMPETITIVE] Epoch 2, Batch 1540/1563, loss: 3.521, reward: 31.296, critic_reward: 30.656, revenue_rate: 0.7662, distance: 9.2140, memory: -0.0446, power: 0.2807, lr: 0.000100, took: 90.378s
[COMPETITIVE] Epoch 2, Batch 1550/1563, loss: 8.770, reward: 33.192, critic_reward: 31.685, revenue_rate: 0.8144, distance: 10.0277, memory: -0.0255, power: 0.3034, lr: 0.000100, took: 98.382s
[COMPETITIVE] Epoch 2, Batch 1560/1563, loss: 4.091, reward: 32.811, critic_reward: 32.894, revenue_rate: 0.8038, distance: 9.8640, memory: -0.0239, power: 0.2987, lr: 0.000100, took: 95.090s
[COMPETITIVE] 开始验证...
[COMPETITIVE] 验证完成 - Epoch 2, reward: 30.484, revenue_rate: 0.7445, distance: 8.8188, memory: -0.0574, power: 0.2679
[COMPETITIVE] 开始训练 Epoch 3/3
[COMPETITIVE] Epoch 3, Batch 10/1563, loss: 3.136, reward: 29.198, critic_reward: 29.091, revenue_rate: 0.7128, distance: 8.4027, memory: -0.0629, power: 0.2552, lr: 0.000100, took: 88.364s
[COMPETITIVE] Epoch 3, Batch 20/1563, loss: 5.213, reward: 29.146, critic_reward: 27.676, revenue_rate: 0.7135, distance: 8.5195, memory: -0.0609, power: 0.2566, lr: 0.000100, took: 74.867s
[COMPETITIVE] Epoch 3, Batch 30/1563, loss: 9.081, reward: 32.888, critic_reward: 34.163, revenue_rate: 0.8096, distance: 9.9030, memory: -0.0230, power: 0.3008, lr: 0.000100, took: 86.233s
[COMPETITIVE] Epoch 3, Batch 40/1563, loss: 5.107, reward: 33.282, critic_reward: 32.602, revenue_rate: 0.8192, distance: 10.0582, memory: -0.0254, power: 0.3063, lr: 0.000100, took: 79.977s
[COMPETITIVE] Epoch 3, Batch 50/1563, loss: 3.013, reward: 34.022, critic_reward: 34.046, revenue_rate: 0.8331, distance: 10.2766, memory: -0.0110, power: 0.3126, lr: 0.000100, took: 83.302s
[COMPETITIVE] Epoch 3, Batch 60/1563, loss: 2.810, reward: 33.230, critic_reward: 33.454, revenue_rate: 0.8143, distance: 10.0428, memory: -0.0217, power: 0.3055, lr: 0.000100, took: 82.362s
[COMPETITIVE] Epoch 3, Batch 70/1563, loss: 2.502, reward: 32.564, critic_reward: 32.638, revenue_rate: 0.7977, distance: 9.7349, memory: -0.0242, power: 0.2965, lr: 0.000100, took: 82.321s
[COMPETITIVE] Epoch 3, Batch 80/1563, loss: 3.587, reward: 31.944, critic_reward: 31.884, revenue_rate: 0.7846, distance: 9.6211, memory: -0.0236, power: 0.2889, lr: 0.000100, took: 78.157s
[COMPETITIVE] Epoch 3, Batch 90/1563, loss: 2.843, reward: 31.724, critic_reward: 31.409, revenue_rate: 0.7769, distance: 9.3863, memory: -0.0397, power: 0.2863, lr: 0.000100, took: 76.415s
[COMPETITIVE] Epoch 3, Batch 100/1563, loss: 1.899, reward: 31.781, critic_reward: 31.867, revenue_rate: 0.7781, distance: 9.3557, memory: -0.0404, power: 0.2838, lr: 0.000100, took: 76.550s
[COMPETITIVE] Epoch 3, Batch 110/1563, loss: 4.459, reward: 31.535, critic_reward: 31.327, revenue_rate: 0.7714, distance: 9.2468, memory: -0.0454, power: 0.2830, lr: 0.000100, took: 76.169s
[COMPETITIVE] Epoch 3, Batch 120/1563, loss: 5.642, reward: 30.326, critic_reward: 31.254, revenue_rate: 0.7402, distance: 8.8079, memory: -0.0565, power: 0.2667, lr: 0.000100, took: 74.177s
[COMPETITIVE] Epoch 3, Batch 130/1563, loss: 4.167, reward: 30.801, critic_reward: 30.045, revenue_rate: 0.7519, distance: 8.9804, memory: -0.0603, power: 0.2723, lr: 0.000100, took: 74.739s
[COMPETITIVE] Epoch 3, Batch 140/1563, loss: 4.151, reward: 31.305, critic_reward: 32.222, revenue_rate: 0.7615, distance: 9.0816, memory: -0.0521, power: 0.2773, lr: 0.000100, took: 76.033s
[COMPETITIVE] Epoch 3, Batch 150/1563, loss: 4.226, reward: 31.064, critic_reward: 31.039, revenue_rate: 0.7588, distance: 9.1188, memory: -0.0514, power: 0.2764, lr: 0.000100, took: 75.632s
[COMPETITIVE] Epoch 3, Batch 160/1563, loss: 4.719, reward: 34.168, critic_reward: 33.437, revenue_rate: 0.8415, distance: 10.4711, memory: -0.0121, power: 0.3172, lr: 0.000100, took: 88.405s
[COMPETITIVE] Epoch 3, Batch 170/1563, loss: 7.180, reward: 35.530, critic_reward: 36.729, revenue_rate: 0.8746, distance: 11.1188, memory: 0.0036, power: 0.3362, lr: 0.000100, took: 91.127s
[COMPETITIVE] Epoch 3, Batch 180/1563, loss: 2.810, reward: 36.065, critic_reward: 36.012, revenue_rate: 0.8916, distance: 11.4909, memory: 0.0188, power: 0.3482, lr: 0.000100, took: 99.067s
[COMPETITIVE] Epoch 3, Batch 190/1563, loss: 2.492, reward: 33.230, critic_reward: 33.639, revenue_rate: 0.8154, distance: 10.0018, memory: -0.0189, power: 0.3043, lr: 0.000100, took: 82.822s
[COMPETITIVE] Epoch 3, Batch 200/1563, loss: 3.831, reward: 32.129, critic_reward: 31.114, revenue_rate: 0.7852, distance: 9.4902, memory: -0.0376, power: 0.2863, lr: 0.000100, took: 77.567s
[COMPETITIVE] Epoch 3, Batch 210/1563, loss: 3.176, reward: 32.613, critic_reward: 32.996, revenue_rate: 0.7975, distance: 9.5552, memory: -0.0320, power: 0.2938, lr: 0.000100, took: 78.988s
[COMPETITIVE] Epoch 3, Batch 220/1563, loss: 2.482, reward: 32.051, critic_reward: 32.064, revenue_rate: 0.7819, distance: 9.4610, memory: -0.0326, power: 0.2854, lr: 0.000100, took: 76.599s
[COMPETITIVE] Epoch 3, Batch 230/1563, loss: 2.779, reward: 32.652, critic_reward: 32.950, revenue_rate: 0.8014, distance: 9.7526, memory: -0.0315, power: 0.2960, lr: 0.000100, took: 83.714s
[COMPETITIVE] Epoch 3, Batch 240/1563, loss: 2.449, reward: 31.923, critic_reward: 31.728, revenue_rate: 0.7795, distance: 9.4250, memory: -0.0387, power: 0.2857, lr: 0.000100, took: 77.401s
[COMPETITIVE] Epoch 3, Batch 250/1563, loss: 3.036, reward: 31.397, critic_reward: 32.059, revenue_rate: 0.7614, distance: 9.0209, memory: -0.0567, power: 0.2749, lr: 0.000100, took: 74.721s
[COMPETITIVE] Epoch 3, Batch 260/1563, loss: 5.495, reward: 31.668, critic_reward: 30.433, revenue_rate: 0.7744, distance: 9.2303, memory: -0.0452, power: 0.2806, lr: 0.000100, took: 76.273s
[COMPETITIVE] Epoch 3, Batch 270/1563, loss: 2.273, reward: 32.757, critic_reward: 33.030, revenue_rate: 0.8028, distance: 9.7654, memory: -0.0237, power: 0.2966, lr: 0.000100, took: 79.751s
[COMPETITIVE] Epoch 3, Batch 280/1563, loss: 3.358, reward: 32.794, critic_reward: 33.004, revenue_rate: 0.8063, distance: 9.9426, memory: -0.0086, power: 0.3006, lr: 0.000100, took: 82.380s
[COMPETITIVE] Epoch 3, Batch 290/1563, loss: 6.586, reward: 32.948, critic_reward: 31.687, revenue_rate: 0.8114, distance: 9.9001, memory: -0.0286, power: 0.3004, lr: 0.000100, took: 82.555s
[COMPETITIVE] Epoch 3, Batch 300/1563, loss: 5.249, reward: 32.614, critic_reward: 32.860, revenue_rate: 0.7975, distance: 9.6357, memory: -0.0362, power: 0.2961, lr: 0.000100, took: 78.620s
[COMPETITIVE] Epoch 3, Batch 310/1563, loss: 2.771, reward: 32.127, critic_reward: 31.911, revenue_rate: 0.7908, distance: 9.5703, memory: -0.0307, power: 0.2920, lr: 0.000100, took: 78.157s
[COMPETITIVE] Epoch 3, Batch 320/1563, loss: 4.427, reward: 34.484, critic_reward: 34.681, revenue_rate: 0.8470, distance: 10.5259, memory: 0.0024, power: 0.3220, lr: 0.000100, took: 87.459s
[COMPETITIVE] Epoch 3, Batch 330/1563, loss: 6.741, reward: 34.821, critic_reward: 34.271, revenue_rate: 0.8578, distance: 10.7444, memory: 0.0012, power: 0.3272, lr: 0.000100, took: 89.603s
[COMPETITIVE] Epoch 3, Batch 340/1563, loss: 3.930, reward: 34.280, critic_reward: 34.124, revenue_rate: 0.8461, distance: 10.5647, memory: -0.0116, power: 0.3190, lr: 0.000100, took: 87.100s
[COMPETITIVE] Epoch 3, Batch 350/1563, loss: 5.527, reward: 33.756, critic_reward: 34.243, revenue_rate: 0.8317, distance: 10.2924, memory: -0.0130, power: 0.3115, lr: 0.000100, took: 84.972s
[COMPETITIVE] Epoch 3, Batch 360/1563, loss: 4.187, reward: 34.338, critic_reward: 34.360, revenue_rate: 0.8427, distance: 10.4715, memory: -0.0016, power: 0.3172, lr: 0.000100, took: 85.906s
[COMPETITIVE] Epoch 3, Batch 370/1563, loss: 2.799, reward: 33.728, critic_reward: 33.851, revenue_rate: 0.8261, distance: 10.1051, memory: -0.0210, power: 0.3063, lr: 0.000100, took: 83.479s
[COMPETITIVE] Epoch 3, Batch 380/1563, loss: 2.733, reward: 33.144, critic_reward: 33.231, revenue_rate: 0.8160, distance: 9.9389, memory: -0.0153, power: 0.3027, lr: 0.000100, took: 85.725s
[COMPETITIVE] Epoch 3, Batch 390/1563, loss: 4.844, reward: 33.576, critic_reward: 32.487, revenue_rate: 0.8271, distance: 10.1799, memory: -0.0084, power: 0.3088, lr: 0.000100, took: 81.025s
[COMPETITIVE] Epoch 3, Batch 400/1563, loss: 2.710, reward: 34.035, critic_reward: 34.382, revenue_rate: 0.8391, distance: 10.4228, memory: -0.0010, power: 0.3183, lr: 0.000100, took: 83.279s
[COMPETITIVE] Epoch 3, Batch 410/1563, loss: 2.152, reward: 33.855, critic_reward: 33.330, revenue_rate: 0.8362, distance: 10.5177, memory: -0.0055, power: 0.3164, lr: 0.000100, took: 81.812s
[COMPETITIVE] Epoch 3, Batch 420/1563, loss: 3.533, reward: 34.110, critic_reward: 34.998, revenue_rate: 0.8376, distance: 10.3706, memory: -0.0051, power: 0.3160, lr: 0.000100, took: 84.510s
[COMPETITIVE] Epoch 3, Batch 430/1563, loss: 2.955, reward: 34.251, critic_reward: 33.654, revenue_rate: 0.8415, distance: 10.4675, memory: -0.0122, power: 0.3184, lr: 0.000100, took: 84.966s
[COMPETITIVE] Epoch 3, Batch 440/1563, loss: 3.594, reward: 33.956, critic_reward: 34.875, revenue_rate: 0.8307, distance: 10.1882, memory: -0.0177, power: 0.3109, lr: 0.000100, took: 82.409s
[COMPETITIVE] Epoch 3, Batch 450/1563, loss: 3.171, reward: 33.195, critic_reward: 32.937, revenue_rate: 0.8155, distance: 9.9556, memory: -0.0290, power: 0.3012, lr: 0.000100, took: 80.996s
[COMPETITIVE] Epoch 3, Batch 460/1563, loss: 2.726, reward: 31.989, critic_reward: 32.613, revenue_rate: 0.7819, distance: 9.3713, memory: -0.0357, power: 0.2861, lr: 0.000100, took: 76.738s
[COMPETITIVE] Epoch 3, Batch 470/1563, loss: 2.181, reward: 31.650, critic_reward: 31.245, revenue_rate: 0.7771, distance: 9.3335, memory: -0.0352, power: 0.2846, lr: 0.000100, took: 77.579s
[COMPETITIVE] Epoch 3, Batch 480/1563, loss: 2.754, reward: 32.721, critic_reward: 32.775, revenue_rate: 0.7985, distance: 9.5979, memory: -0.0381, power: 0.2947, lr: 0.000100, took: 83.473s
[COMPETITIVE] Epoch 3, Batch 490/1563, loss: 2.951, reward: 31.155, critic_reward: 31.034, revenue_rate: 0.7609, distance: 9.0881, memory: -0.0480, power: 0.2756, lr: 0.000100, took: 74.488s
[COMPETITIVE] Epoch 3, Batch 500/1563, loss: 4.258, reward: 32.706, critic_reward: 32.105, revenue_rate: 0.8028, distance: 9.7449, memory: -0.0389, power: 0.2946, lr: 0.000100, took: 80.951s
[COMPETITIVE] Epoch 3, Batch 510/1563, loss: 3.339, reward: 33.684, critic_reward: 33.434, revenue_rate: 0.8265, distance: 10.1316, memory: -0.0160, power: 0.3089, lr: 0.000100, took: 86.680s
[COMPETITIVE] Epoch 3, Batch 520/1563, loss: 4.022, reward: 34.340, critic_reward: 34.584, revenue_rate: 0.8446, distance: 10.5116, memory: -0.0167, power: 0.3168, lr: 0.000100, took: 88.099s
[COMPETITIVE] Epoch 3, Batch 530/1563, loss: 4.303, reward: 32.157, critic_reward: 33.255, revenue_rate: 0.7889, distance: 9.5915, memory: -0.0321, power: 0.2914, lr: 0.000100, took: 79.556s
[COMPETITIVE] Epoch 3, Batch 540/1563, loss: 3.310, reward: 31.178, critic_reward: 30.667, revenue_rate: 0.7631, distance: 9.1861, memory: -0.0458, power: 0.2798, lr: 0.000100, took: 75.607s
[COMPETITIVE] Epoch 3, Batch 550/1563, loss: 3.292, reward: 32.115, critic_reward: 32.716, revenue_rate: 0.7880, distance: 9.6945, memory: -0.0411, power: 0.2945, lr: 0.000100, took: 79.987s
[COMPETITIVE] Epoch 3, Batch 560/1563, loss: 2.766, reward: 32.514, critic_reward: 32.401, revenue_rate: 0.8003, distance: 9.8564, memory: -0.0202, power: 0.2995, lr: 0.000100, took: 82.829s
[COMPETITIVE] Epoch 3, Batch 570/1563, loss: 3.834, reward: 33.778, critic_reward: 34.602, revenue_rate: 0.8290, distance: 10.1903, memory: -0.0106, power: 0.3108, lr: 0.000100, took: 87.567s
[COMPETITIVE] Epoch 3, Batch 580/1563, loss: 2.699, reward: 33.497, critic_reward: 33.206, revenue_rate: 0.8229, distance: 10.2397, memory: -0.0160, power: 0.3092, lr: 0.000100, took: 86.277s
[COMPETITIVE] Epoch 3, Batch 590/1563, loss: 2.646, reward: 33.708, critic_reward: 34.059, revenue_rate: 0.8307, distance: 10.2605, memory: -0.0171, power: 0.3121, lr: 0.000100, took: 81.902s
[COMPETITIVE] Epoch 3, Batch 600/1563, loss: 2.339, reward: 33.170, critic_reward: 33.006, revenue_rate: 0.8173, distance: 10.0277, memory: -0.0303, power: 0.3029, lr: 0.000100, took: 79.787s
[COMPETITIVE] Epoch 3, Batch 610/1563, loss: 2.875, reward: 33.788, critic_reward: 34.197, revenue_rate: 0.8309, distance: 10.2273, memory: -0.0203, power: 0.3099, lr: 0.000100, took: 85.254s
[COMPETITIVE] Epoch 3, Batch 620/1563, loss: 2.740, reward: 34.553, critic_reward: 34.864, revenue_rate: 0.8534, distance: 10.6014, memory: -0.0023, power: 0.3228, lr: 0.000100, took: 92.803s
[COMPETITIVE] Epoch 3, Batch 630/1563, loss: 2.785, reward: 34.853, critic_reward: 34.562, revenue_rate: 0.8592, distance: 10.7495, memory: -0.0013, power: 0.3265, lr: 0.000100, took: 90.390s
[COMPETITIVE] Epoch 3, Batch 640/1563, loss: 2.694, reward: 33.948, critic_reward: 34.397, revenue_rate: 0.8378, distance: 10.3372, memory: -0.0078, power: 0.3137, lr: 0.000100, took: 87.594s
[COMPETITIVE] Epoch 3, Batch 650/1563, loss: 3.384, reward: 34.132, critic_reward: 34.354, revenue_rate: 0.8419, distance: 10.3710, memory: -0.0168, power: 0.3143, lr: 0.000100, took: 88.715s
[COMPETITIVE] Epoch 3, Batch 660/1563, loss: 3.781, reward: 31.837, critic_reward: 32.541, revenue_rate: 0.7799, distance: 9.4343, memory: -0.0360, power: 0.2873, lr: 0.000100, took: 80.054s
[COMPETITIVE] Epoch 3, Batch 670/1563, loss: 4.529, reward: 32.292, critic_reward: 31.245, revenue_rate: 0.7932, distance: 9.6568, memory: -0.0321, power: 0.2929, lr: 0.000100, took: 78.380s
[COMPETITIVE] Epoch 3, Batch 680/1563, loss: 3.564, reward: 34.509, critic_reward: 34.452, revenue_rate: 0.8488, distance: 10.5256, memory: -0.0144, power: 0.3212, lr: 0.000100, took: 86.983s
[COMPETITIVE] Epoch 3, Batch 690/1563, loss: 2.632, reward: 34.747, critic_reward: 34.907, revenue_rate: 0.8562, distance: 10.6784, memory: 0.0045, power: 0.3256, lr: 0.000100, took: 90.363s
[COMPETITIVE] Epoch 3, Batch 700/1563, loss: 2.634, reward: 34.321, critic_reward: 34.166, revenue_rate: 0.8433, distance: 10.5193, memory: -0.0082, power: 0.3176, lr: 0.000100, took: 88.148s
[COMPETITIVE] Epoch 3, Batch 710/1563, loss: 5.467, reward: 34.608, critic_reward: 35.742, revenue_rate: 0.8523, distance: 10.6742, memory: 0.0000, power: 0.3235, lr: 0.000100, took: 88.762s
[COMPETITIVE] Epoch 3, Batch 720/1563, loss: 4.616, reward: 34.719, critic_reward: 33.918, revenue_rate: 0.8514, distance: 10.6421, memory: -0.0043, power: 0.3214, lr: 0.000100, took: 90.161s
[COMPETITIVE] Epoch 3, Batch 730/1563, loss: 4.271, reward: 34.689, critic_reward: 35.177, revenue_rate: 0.8545, distance: 10.6897, memory: -0.0035, power: 0.3219, lr: 0.000100, took: 89.139s
[COMPETITIVE] Epoch 3, Batch 740/1563, loss: 3.040, reward: 32.989, critic_reward: 33.726, revenue_rate: 0.8104, distance: 9.9180, memory: -0.0280, power: 0.3010, lr: 0.000100, took: 82.100s
[COMPETITIVE] Epoch 3, Batch 750/1563, loss: 4.194, reward: 33.005, critic_reward: 32.265, revenue_rate: 0.8102, distance: 9.9206, memory: -0.0272, power: 0.3013, lr: 0.000100, took: 83.062s
[COMPETITIVE] Epoch 3, Batch 760/1563, loss: 2.430, reward: 33.694, critic_reward: 33.904, revenue_rate: 0.8281, distance: 10.1429, memory: -0.0148, power: 0.3086, lr: 0.000100, took: 87.717s
[COMPETITIVE] Epoch 3, Batch 770/1563, loss: 2.739, reward: 33.498, critic_reward: 33.145, revenue_rate: 0.8201, distance: 10.0892, memory: -0.0253, power: 0.3070, lr: 0.000100, took: 85.237s
[COMPETITIVE] Epoch 3, Batch 780/1563, loss: 3.112, reward: 32.670, critic_reward: 32.607, revenue_rate: 0.7988, distance: 9.7299, memory: -0.0306, power: 0.2961, lr: 0.000100, took: 83.756s
[COMPETITIVE] Epoch 3, Batch 790/1563, loss: 5.273, reward: 31.743, critic_reward: 31.852, revenue_rate: 0.7748, distance: 9.3019, memory: -0.0489, power: 0.2833, lr: 0.000100, took: 78.095s
[COMPETITIVE] Epoch 3, Batch 800/1563, loss: 4.032, reward: 30.541, critic_reward: 30.847, revenue_rate: 0.7467, distance: 8.8713, memory: -0.0549, power: 0.2717, lr: 0.000100, took: 73.600s
[COMPETITIVE] Epoch 3, Batch 810/1563, loss: 2.867, reward: 30.134, critic_reward: 29.761, revenue_rate: 0.7360, distance: 8.7672, memory: -0.0557, power: 0.2674, lr: 0.000100, took: 73.395s
[COMPETITIVE] Epoch 3, Batch 820/1563, loss: 4.296, reward: 30.228, critic_reward: 31.116, revenue_rate: 0.7359, distance: 8.7409, memory: -0.0625, power: 0.2665, lr: 0.000100, took: 73.541s
[COMPETITIVE] Epoch 3, Batch 830/1563, loss: 4.086, reward: 29.795, critic_reward: 29.198, revenue_rate: 0.7250, distance: 8.5169, memory: -0.0641, power: 0.2639, lr: 0.000100, took: 74.759s
[COMPETITIVE] Epoch 3, Batch 840/1563, loss: 7.543, reward: 31.847, critic_reward: 30.423, revenue_rate: 0.7766, distance: 9.3259, memory: -0.0417, power: 0.2857, lr: 0.000100, took: 80.191s
[COMPETITIVE] Epoch 3, Batch 850/1563, loss: 3.208, reward: 32.012, critic_reward: 32.504, revenue_rate: 0.7850, distance: 9.4857, memory: -0.0353, power: 0.2874, lr: 0.000100, took: 81.163s
[COMPETITIVE] Epoch 3, Batch 860/1563, loss: 3.037, reward: 31.939, critic_reward: 31.248, revenue_rate: 0.7817, distance: 9.4017, memory: -0.0371, power: 0.2875, lr: 0.000100, took: 80.426s
[COMPETITIVE] Epoch 3, Batch 870/1563, loss: 3.607, reward: 31.238, critic_reward: 32.339, revenue_rate: 0.7638, distance: 9.1257, memory: -0.0402, power: 0.2783, lr: 0.000100, took: 77.150s
[COMPETITIVE] Epoch 3, Batch 880/1563, loss: 3.360, reward: 30.736, critic_reward: 29.979, revenue_rate: 0.7489, distance: 8.8599, memory: -0.0545, power: 0.2701, lr: 0.000100, took: 74.988s
[COMPETITIVE] Epoch 3, Batch 890/1563, loss: 4.731, reward: 30.676, critic_reward: 31.099, revenue_rate: 0.7516, distance: 8.9623, memory: -0.0597, power: 0.2712, lr: 0.000100, took: 76.359s
[COMPETITIVE] Epoch 3, Batch 900/1563, loss: 3.481, reward: 29.097, critic_reward: 29.304, revenue_rate: 0.7065, distance: 8.2910, memory: -0.0774, power: 0.2521, lr: 0.000100, took: 68.762s
[COMPETITIVE] Epoch 3, Batch 910/1563, loss: 4.129, reward: 29.894, critic_reward: 28.966, revenue_rate: 0.7331, distance: 8.7076, memory: -0.0657, power: 0.2628, lr: 0.000100, took: 72.614s
[COMPETITIVE] Epoch 3, Batch 920/1563, loss: 4.303, reward: 31.300, critic_reward: 32.383, revenue_rate: 0.7656, distance: 9.1419, memory: -0.0456, power: 0.2800, lr: 0.000100, took: 76.959s
[COMPETITIVE] Epoch 3, Batch 930/1563, loss: 5.028, reward: 32.538, critic_reward: 31.819, revenue_rate: 0.7970, distance: 9.7033, memory: -0.0331, power: 0.2929, lr: 0.000100, took: 81.251s
[COMPETITIVE] Epoch 3, Batch 940/1563, loss: 2.915, reward: 33.195, critic_reward: 33.153, revenue_rate: 0.8182, distance: 10.0261, memory: -0.0207, power: 0.3049, lr: 0.000100, took: 85.148s
[COMPETITIVE] Epoch 3, Batch 950/1563, loss: 6.118, reward: 33.839, critic_reward: 32.807, revenue_rate: 0.8280, distance: 10.2394, memory: -0.0228, power: 0.3089, lr: 0.000100, took: 88.770s
[COMPETITIVE] Epoch 3, Batch 960/1563, loss: 3.739, reward: 33.046, critic_reward: 34.066, revenue_rate: 0.8112, distance: 9.8911, memory: -0.0325, power: 0.2997, lr: 0.000100, took: 84.559s
[COMPETITIVE] Epoch 3, Batch 970/1563, loss: 3.327, reward: 31.900, critic_reward: 31.212, revenue_rate: 0.7844, distance: 9.5339, memory: -0.0341, power: 0.2898, lr: 0.000100, took: 79.355s
[COMPETITIVE] Epoch 3, Batch 980/1563, loss: 2.459, reward: 31.242, critic_reward: 31.188, revenue_rate: 0.7666, distance: 9.2826, memory: -0.0365, power: 0.2832, lr: 0.000100, took: 77.221s
[COMPETITIVE] Epoch 3, Batch 990/1563, loss: 2.655, reward: 34.218, critic_reward: 34.209, revenue_rate: 0.8417, distance: 10.4831, memory: -0.0103, power: 0.3181, lr: 0.000100, took: 84.804s
[COMPETITIVE] Epoch 3, Batch 1000/1563, loss: 3.212, reward: 35.110, critic_reward: 35.707, revenue_rate: 0.8691, distance: 10.9580, memory: 0.0126, power: 0.3319, lr: 0.000100, took: 85.486s
[COMPETITIVE] Epoch 3, Batch 1010/1563, loss: 2.857, reward: 33.292, critic_reward: 33.879, revenue_rate: 0.8176, distance: 10.0810, memory: -0.0176, power: 0.3044, lr: 0.000100, took: 79.943s
[COMPETITIVE] Epoch 3, Batch 1020/1563, loss: 4.562, reward: 31.769, critic_reward: 30.770, revenue_rate: 0.7753, distance: 9.2846, memory: -0.0487, power: 0.2809, lr: 0.000100, took: 75.033s
[COMPETITIVE] Epoch 3, Batch 1030/1563, loss: 5.383, reward: 32.553, critic_reward: 32.543, revenue_rate: 0.7938, distance: 9.6105, memory: -0.0435, power: 0.2911, lr: 0.000100, took: 74.331s
[COMPETITIVE] Epoch 3, Batch 1040/1563, loss: 4.112, reward: 32.681, critic_reward: 33.415, revenue_rate: 0.7997, distance: 9.6978, memory: -0.0411, power: 0.2943, lr: 0.000100, took: 75.307s
[COMPETITIVE] Epoch 3, Batch 1050/1563, loss: 2.970, reward: 31.997, critic_reward: 31.739, revenue_rate: 0.7853, distance: 9.5721, memory: -0.0331, power: 0.2901, lr: 0.000100, took: 74.077s
[COMPETITIVE] Epoch 3, Batch 1060/1563, loss: 2.393, reward: 32.036, critic_reward: 32.147, revenue_rate: 0.7855, distance: 9.5217, memory: -0.0252, power: 0.2930, lr: 0.000100, took: 76.591s
[COMPETITIVE] Epoch 3, Batch 1070/1563, loss: 2.963, reward: 32.390, critic_reward: 32.739, revenue_rate: 0.7967, distance: 9.7409, memory: -0.0286, power: 0.2966, lr: 0.000100, took: 77.960s
[COMPETITIVE] Epoch 3, Batch 1080/1563, loss: 4.037, reward: 33.053, critic_reward: 32.485, revenue_rate: 0.8110, distance: 9.9160, memory: -0.0314, power: 0.3006, lr: 0.000100, took: 84.689s
[COMPETITIVE] Epoch 3, Batch 1090/1563, loss: 2.851, reward: 31.977, critic_reward: 32.230, revenue_rate: 0.7843, distance: 9.5920, memory: -0.0336, power: 0.2887, lr: 0.000100, took: 83.230s
[COMPETITIVE] Epoch 3, Batch 1100/1563, loss: 5.055, reward: 31.000, critic_reward: 32.433, revenue_rate: 0.7595, distance: 9.0650, memory: -0.0503, power: 0.2727, lr: 0.000100, took: 72.595s
[COMPETITIVE] Epoch 3, Batch 1110/1563, loss: 4.793, reward: 29.931, critic_reward: 29.549, revenue_rate: 0.7292, distance: 8.6349, memory: -0.0615, power: 0.2631, lr: 0.000100, took: 71.349s
[COMPETITIVE] Epoch 3, Batch 1120/1563, loss: 5.321, reward: 30.904, critic_reward: 29.711, revenue_rate: 0.7535, distance: 9.0567, memory: -0.0462, power: 0.2744, lr: 0.000100, took: 74.317s
[COMPETITIVE] Epoch 3, Batch 1130/1563, loss: 2.429, reward: 31.601, critic_reward: 31.942, revenue_rate: 0.7724, distance: 9.2597, memory: -0.0400, power: 0.2826, lr: 0.000100, took: 77.683s
[COMPETITIVE] Epoch 3, Batch 1140/1563, loss: 4.359, reward: 32.764, critic_reward: 32.563, revenue_rate: 0.8027, distance: 9.9064, memory: -0.0186, power: 0.2985, lr: 0.000100, took: 83.990s
[COMPETITIVE] Epoch 3, Batch 1150/1563, loss: 2.654, reward: 32.855, critic_reward: 32.865, revenue_rate: 0.8036, distance: 9.8214, memory: -0.0298, power: 0.2991, lr: 0.000100, took: 81.963s
[COMPETITIVE] Epoch 3, Batch 1160/1563, loss: 3.687, reward: 31.007, critic_reward: 31.829, revenue_rate: 0.7560, distance: 9.0296, memory: -0.0507, power: 0.2760, lr: 0.000100, took: 74.466s
[COMPETITIVE] Epoch 3, Batch 1170/1563, loss: 3.075, reward: 29.163, critic_reward: 28.938, revenue_rate: 0.7089, distance: 8.3505, memory: -0.0746, power: 0.2546, lr: 0.000100, took: 66.410s
[COMPETITIVE] Epoch 3, Batch 1180/1563, loss: 3.484, reward: 28.821, critic_reward: 29.429, revenue_rate: 0.7025, distance: 8.2250, memory: -0.0735, power: 0.2516, lr: 0.000100, took: 66.981s
[COMPETITIVE] Epoch 3, Batch 1190/1563, loss: 4.306, reward: 30.200, critic_reward: 28.955, revenue_rate: 0.7358, distance: 8.7926, memory: -0.0621, power: 0.2667, lr: 0.000100, took: 68.340s
[COMPETITIVE] Epoch 3, Batch 1200/1563, loss: 9.354, reward: 31.789, critic_reward: 34.108, revenue_rate: 0.7764, distance: 9.3010, memory: -0.0410, power: 0.2863, lr: 0.000100, took: 72.847s
[COMPETITIVE] Epoch 3, Batch 1210/1563, loss: 4.366, reward: 31.588, critic_reward: 30.335, revenue_rate: 0.7740, distance: 9.2972, memory: -0.0437, power: 0.2846, lr: 0.000100, took: 77.445s
[COMPETITIVE] Epoch 3, Batch 1220/1563, loss: 3.474, reward: 32.568, critic_reward: 33.174, revenue_rate: 0.7998, distance: 9.7362, memory: -0.0370, power: 0.2956, lr: 0.000100, took: 78.794s
[COMPETITIVE] Epoch 3, Batch 1230/1563, loss: 5.326, reward: 33.316, critic_reward: 32.141, revenue_rate: 0.8166, distance: 10.0424, memory: -0.0175, power: 0.3080, lr: 0.000100, took: 80.737s
[COMPETITIVE] Epoch 3, Batch 1240/1563, loss: 4.176, reward: 34.490, critic_reward: 34.591, revenue_rate: 0.8488, distance: 10.6574, memory: -0.0058, power: 0.3241, lr: 0.000100, took: 90.859s
[COMPETITIVE] Epoch 3, Batch 1250/1563, loss: 3.287, reward: 33.205, critic_reward: 33.712, revenue_rate: 0.8166, distance: 10.0248, memory: -0.0158, power: 0.3054, lr: 0.000100, took: 83.655s
[COMPETITIVE] Epoch 3, Batch 1260/1563, loss: 3.892, reward: 33.378, critic_reward: 32.284, revenue_rate: 0.8219, distance: 10.0784, memory: -0.0234, power: 0.3079, lr: 0.000100, took: 83.913s
[COMPETITIVE] Epoch 3, Batch 1270/1563, loss: 4.225, reward: 34.285, critic_reward: 35.567, revenue_rate: 0.8473, distance: 10.6476, memory: -0.0019, power: 0.3232, lr: 0.000100, took: 87.890s
[COMPETITIVE] Epoch 3, Batch 1280/1563, loss: 2.952, reward: 33.954, critic_reward: 33.145, revenue_rate: 0.8382, distance: 10.4008, memory: -0.0079, power: 0.3153, lr: 0.000100, took: 85.856s
[COMPETITIVE] Epoch 3, Batch 1290/1563, loss: 3.224, reward: 34.299, critic_reward: 34.360, revenue_rate: 0.8418, distance: 10.4990, memory: -0.0074, power: 0.3170, lr: 0.000100, took: 91.886s
[COMPETITIVE] Epoch 3, Batch 1300/1563, loss: 2.486, reward: 34.433, critic_reward: 34.580, revenue_rate: 0.8480, distance: 10.6243, memory: 0.0009, power: 0.3218, lr: 0.000100, took: 90.816s
[COMPETITIVE] Epoch 3, Batch 1310/1563, loss: 2.786, reward: 34.103, critic_reward: 34.198, revenue_rate: 0.8380, distance: 10.3225, memory: -0.0104, power: 0.3159, lr: 0.000100, took: 89.577s
[COMPETITIVE] Epoch 3, Batch 1320/1563, loss: 2.594, reward: 34.604, critic_reward: 34.758, revenue_rate: 0.8534, distance: 10.7162, memory: -0.0025, power: 0.3225, lr: 0.000100, took: 89.588s
[COMPETITIVE] Epoch 3, Batch 1330/1563, loss: 3.395, reward: 34.916, critic_reward: 34.198, revenue_rate: 0.8621, distance: 10.8598, memory: 0.0054, power: 0.3274, lr: 0.000100, took: 89.613s
[COMPETITIVE] Epoch 3, Batch 1340/1563, loss: 2.373, reward: 35.776, critic_reward: 35.976, revenue_rate: 0.8909, distance: 11.4315, memory: 0.0254, power: 0.3478, lr: 0.000100, took: 96.947s
[COMPETITIVE] Epoch 3, Batch 1350/1563, loss: 3.130, reward: 36.352, critic_reward: 36.165, revenue_rate: 0.9015, distance: 11.6588, memory: 0.0298, power: 0.3497, lr: 0.000100, took: 101.190s
[COMPETITIVE] Epoch 3, Batch 1360/1563, loss: 2.317, reward: 35.520, critic_reward: 35.737, revenue_rate: 0.8767, distance: 11.1452, memory: 0.0217, power: 0.3373, lr: 0.000100, took: 94.681s
[COMPETITIVE] Epoch 3, Batch 1370/1563, loss: 2.298, reward: 34.697, critic_reward: 34.645, revenue_rate: 0.8518, distance: 10.6030, memory: -0.0018, power: 0.3210, lr: 0.000100, took: 87.621s
[COMPETITIVE] Epoch 3, Batch 1380/1563, loss: 3.587, reward: 34.108, critic_reward: 33.976, revenue_rate: 0.8407, distance: 10.4020, memory: -0.0099, power: 0.3162, lr: 0.000100, took: 87.721s
[COMPETITIVE] Epoch 3, Batch 1390/1563, loss: 4.139, reward: 34.585, critic_reward: 34.094, revenue_rate: 0.8512, distance: 10.6027, memory: -0.0115, power: 0.3196, lr: 0.000100, took: 92.635s
[COMPETITIVE] Epoch 3, Batch 1400/1563, loss: 2.836, reward: 35.715, critic_reward: 35.711, revenue_rate: 0.8773, distance: 10.9993, memory: 0.0090, power: 0.3324, lr: 0.000100, took: 92.184s
[COMPETITIVE] Epoch 3, Batch 1410/1563, loss: 2.295, reward: 35.444, critic_reward: 34.957, revenue_rate: 0.8776, distance: 11.2360, memory: 0.0203, power: 0.3367, lr: 0.000100, took: 96.376s
[COMPETITIVE] Epoch 3, Batch 1420/1563, loss: 3.128, reward: 36.643, critic_reward: 36.501, revenue_rate: 0.9080, distance: 11.7376, memory: 0.0248, power: 0.3558, lr: 0.000100, took: 100.614s
[COMPETITIVE] Epoch 3, Batch 1430/1563, loss: 2.892, reward: 35.511, critic_reward: 35.978, revenue_rate: 0.8757, distance: 11.2082, memory: 0.0216, power: 0.3420, lr: 0.000100, took: 96.520s
[COMPETITIVE] Epoch 3, Batch 1440/1563, loss: 3.338, reward: 35.090, critic_reward: 34.481, revenue_rate: 0.8622, distance: 10.8780, memory: 0.0108, power: 0.3299, lr: 0.000100, took: 93.795s
[COMPETITIVE] Epoch 3, Batch 1450/1563, loss: 2.554, reward: 35.108, critic_reward: 35.422, revenue_rate: 0.8698, distance: 11.0093, memory: 0.0094, power: 0.3337, lr: 0.000100, took: 94.858s
[COMPETITIVE] Epoch 3, Batch 1460/1563, loss: 2.453, reward: 34.188, critic_reward: 34.325, revenue_rate: 0.8404, distance: 10.3690, memory: -0.0098, power: 0.3176, lr: 0.000100, took: 87.392s
[COMPETITIVE] Epoch 3, Batch 1470/1563, loss: 2.808, reward: 32.415, critic_reward: 33.014, revenue_rate: 0.7984, distance: 9.7627, memory: -0.0208, power: 0.2967, lr: 0.000100, took: 80.912s
[COMPETITIVE] Epoch 3, Batch 1480/1563, loss: 5.591, reward: 32.990, critic_reward: 31.858, revenue_rate: 0.8066, distance: 9.8003, memory: -0.0254, power: 0.2985, lr: 0.000100, took: 81.400s
[COMPETITIVE] Epoch 3, Batch 1490/1563, loss: 3.113, reward: 33.615, critic_reward: 33.386, revenue_rate: 0.8260, distance: 10.1578, memory: -0.0194, power: 0.3088, lr: 0.000100, took: 87.077s
[COMPETITIVE] Epoch 3, Batch 1500/1563, loss: 3.693, reward: 35.367, critic_reward: 35.902, revenue_rate: 0.8706, distance: 10.9117, memory: -0.0012, power: 0.3322, lr: 0.000100, took: 92.585s
[COMPETITIVE] Epoch 3, Batch 1510/1563, loss: 3.362, reward: 35.681, critic_reward: 35.357, revenue_rate: 0.8828, distance: 11.1958, memory: 0.0199, power: 0.3385, lr: 0.000100, took: 96.164s
[COMPETITIVE] Epoch 3, Batch 1520/1563, loss: 3.308, reward: 34.231, critic_reward: 34.597, revenue_rate: 0.8428, distance: 10.5120, memory: -0.0031, power: 0.3180, lr: 0.000100, took: 88.883s
[COMPETITIVE] Epoch 3, Batch 1530/1563, loss: 2.576, reward: 32.383, critic_reward: 32.655, revenue_rate: 0.7920, distance: 9.4597, memory: -0.0362, power: 0.2904, lr: 0.000100, took: 79.373s
[COMPETITIVE] Epoch 3, Batch 1540/1563, loss: 2.617, reward: 31.324, critic_reward: 31.101, revenue_rate: 0.7652, distance: 9.0744, memory: -0.0544, power: 0.2779, lr: 0.000100, took: 75.763s
[COMPETITIVE] Epoch 3, Batch 1550/1563, loss: 3.561, reward: 31.058, critic_reward: 30.529, revenue_rate: 0.7589, distance: 9.0688, memory: -0.0450, power: 0.2753, lr: 0.000100, took: 75.168s
[COMPETITIVE] Epoch 3, Batch 1560/1563, loss: 3.718, reward: 33.015, critic_reward: 32.550, revenue_rate: 0.8123, distance: 9.8461, memory: -0.0270, power: 0.2980, lr: 0.000100, took: 81.988s
[COMPETITIVE] 开始验证...
[COMPETITIVE] 验证完成 - Epoch 3, reward: 34.181, revenue_rate: 0.8397, distance: 10.2739, memory: -0.0154, power: 0.3120
[COMPETITIVE] 已保存新模型到 constellation_smp\constellation_smp100\multi_constellation_comparison_2025_08_25_17_02_29\constellation_gpnindrnn_competitive_2025_08_26_05_01_59 (验证集奖励: 34.1813)
[COMPETITIVE] 训练完成
训练结束时间: 2025-08-26 16:54:49
训练总耗时: 11:46:19.662532
训练过程统计:
  最终训练奖励: 34.3963
  最佳验证奖励: 34.1813
  训练轮数完成: 4689
  奖励提升: 24.8693
  平均每轮提升: 0.0053
生成训练曲线图...
✓ 训练曲线图已保存到: constellation_smp\constellation_smp100\multi_constellation_comparison_2025_08_25_17_02_29\constellation_gpnindrnn_competitive_2025_08_26_05_01_59\train_loss_reward.png
开始测试 competitive 模式...
测试配置:
  测试数据大小: 10000
  测试批次数: 157
  可视化样本数: 5
测试开始时间: 2025-08-26 16:55:33
测试结束时间: 2025-08-26 17:20:30
测试耗时: 0:24:57.200777

COMPETITIVE 模式完整结果:
==================================================
训练结果:
  最佳验证奖励: 34.1813
  模型保存路径: constellation_smp\constellation_smp100\multi_constellation_comparison_2025_08_25_17_02_29\constellation_gpnindrnn_competitive_2025_08_26_05_01_59
测试结果:
  平均收益率: 0.8380
  平均距离: 10.2708
  平均内存使用: -0.0126
  平均功耗: 0.3114
模型信息:
  Actor参数: 2,211,337
  Critic参数: 494,285
  总参数: 2,705,622
综合性能评分: 3.0823
文件输出:
  权重文件: actor.pt, critic.pt
  训练日志: log.txt
  测试结果: test_competitive/
==================================================

================================================================================
开始训练星座模式: HYBRID
================================================================================
hybrid 模式模型信息:
  Actor参数数量: 2,408,201
  Critic参数数量: 691,149
  总参数数量: 3,099,350
详细训练配置:
  数据集大小: 训练100000, 验证10000
  学习率: Actor=0.0001, Critic=0.0002
  批次大小: 64
  训练轮数: 3
  梯度裁剪: 1
  权重衰减: 0.0001
  随机种子: 12346
  内存总量: 0.3
  功率总量: 5
开始训练 hybrid 模式...
训练开始时间: 2025-08-26 17:27:00
详细训练过程:
[HYBRID] 开始训练 Epoch 1/3
[HYBRID] Epoch 1, Batch 10/1563, loss: 686.887, reward: 14.450, critic_reward: 14.499, revenue_rate: 0.3642, distance: 5.4001, memory: -0.1043, power: 0.1641, lr: 0.000100, took: 48.558s
[HYBRID] Epoch 1, Batch 20/1563, loss: 163.878, reward: 16.977, critic_reward: 18.631, revenue_rate: 0.4258, distance: 6.0132, memory: -0.1425, power: 0.1834, lr: 0.000100, took: 48.267s
[HYBRID] Epoch 1, Batch 30/1563, loss: 8.674, reward: 15.512, critic_reward: 15.543, revenue_rate: 0.3842, distance: 5.1238, memory: -0.1727, power: 0.1560, lr: 0.000100, took: 40.769s
[HYBRID] Epoch 1, Batch 40/1563, loss: 9.243, reward: 20.200, critic_reward: 19.306, revenue_rate: 0.5010, distance: 6.5554, memory: -0.1278, power: 0.2005, lr: 0.000100, took: 53.374s
[HYBRID] Epoch 1, Batch 50/1563, loss: 10.545, reward: 25.373, critic_reward: 24.393, revenue_rate: 0.6295, distance: 8.3440, memory: -0.0822, power: 0.2531, lr: 0.000100, took: 68.510s
[HYBRID] Epoch 1, Batch 60/1563, loss: 6.409, reward: 27.106, critic_reward: 26.920, revenue_rate: 0.6713, distance: 8.6938, memory: -0.0690, power: 0.2619, lr: 0.000100, took: 70.946s
[HYBRID] Epoch 1, Batch 70/1563, loss: 7.773, reward: 25.691, critic_reward: 26.312, revenue_rate: 0.6324, distance: 7.8871, memory: -0.0894, power: 0.2374, lr: 0.000100, took: 66.349s
[HYBRID] Epoch 1, Batch 80/1563, loss: 5.270, reward: 26.352, critic_reward: 26.350, revenue_rate: 0.6471, distance: 8.0657, memory: -0.0883, power: 0.2433, lr: 0.000100, took: 65.742s
[HYBRID] Epoch 1, Batch 90/1563, loss: 7.528, reward: 25.414, critic_reward: 25.339, revenue_rate: 0.6277, distance: 7.9411, memory: -0.0857, power: 0.2400, lr: 0.000100, took: 64.039s
[HYBRID] Epoch 1, Batch 100/1563, loss: 5.727, reward: 25.666, critic_reward: 25.771, revenue_rate: 0.6295, distance: 7.8126, memory: -0.0893, power: 0.2355, lr: 0.000100, took: 63.167s
[HYBRID] Epoch 1, Batch 110/1563, loss: 10.764, reward: 23.758, critic_reward: 24.279, revenue_rate: 0.5825, distance: 7.1071, memory: -0.1102, power: 0.2157, lr: 0.000100, took: 57.558s
[HYBRID] Epoch 1, Batch 120/1563, loss: 16.226, reward: 26.191, critic_reward: 24.415, revenue_rate: 0.6413, distance: 7.9835, memory: -0.0843, power: 0.2415, lr: 0.000100, took: 64.838s
[HYBRID] Epoch 1, Batch 130/1563, loss: 7.257, reward: 28.698, critic_reward: 29.391, revenue_rate: 0.7120, distance: 9.3431, memory: -0.0446, power: 0.2802, lr: 0.000100, took: 76.789s
[HYBRID] Epoch 1, Batch 140/1563, loss: 5.230, reward: 28.243, critic_reward: 28.328, revenue_rate: 0.6927, distance: 8.8291, memory: -0.0715, power: 0.2656, lr: 0.000100, took: 71.887s
[HYBRID] Epoch 1, Batch 150/1563, loss: 5.888, reward: 27.212, critic_reward: 26.969, revenue_rate: 0.6701, distance: 8.2611, memory: -0.0759, power: 0.2510, lr: 0.000100, took: 67.598s
[HYBRID] Epoch 1, Batch 160/1563, loss: 6.490, reward: 27.422, critic_reward: 26.884, revenue_rate: 0.6716, distance: 8.3364, memory: -0.0792, power: 0.2521, lr: 0.000100, took: 67.573s
[HYBRID] Epoch 1, Batch 170/1563, loss: 4.552, reward: 27.041, critic_reward: 27.345, revenue_rate: 0.6672, distance: 8.1827, memory: -0.0797, power: 0.2470, lr: 0.000100, took: 66.230s
[HYBRID] Epoch 1, Batch 180/1563, loss: 5.186, reward: 27.880, critic_reward: 27.574, revenue_rate: 0.6862, distance: 8.4738, memory: -0.0744, power: 0.2581, lr: 0.000100, took: 70.021s
[HYBRID] Epoch 1, Batch 190/1563, loss: 12.277, reward: 29.278, critic_reward: 29.844, revenue_rate: 0.7246, distance: 9.2520, memory: -0.0439, power: 0.2778, lr: 0.000100, took: 75.976s
[HYBRID] Epoch 1, Batch 200/1563, loss: 13.108, reward: 29.774, critic_reward: 31.225, revenue_rate: 0.7359, distance: 9.2630, memory: -0.0530, power: 0.2821, lr: 0.000100, took: 78.959s
[HYBRID] Epoch 1, Batch 210/1563, loss: 5.332, reward: 30.091, critic_reward: 30.004, revenue_rate: 0.7429, distance: 9.3043, memory: -0.0493, power: 0.2805, lr: 0.000100, took: 77.005s
[HYBRID] Epoch 1, Batch 220/1563, loss: 5.967, reward: 29.685, critic_reward: 29.803, revenue_rate: 0.7298, distance: 9.0355, memory: -0.0582, power: 0.2727, lr: 0.000100, took: 74.372s
[HYBRID] Epoch 1, Batch 230/1563, loss: 3.505, reward: 28.800, critic_reward: 28.611, revenue_rate: 0.7060, distance: 8.6082, memory: -0.0617, power: 0.2635, lr: 0.000100, took: 70.926s
[HYBRID] Epoch 1, Batch 240/1563, loss: 3.886, reward: 28.945, critic_reward: 29.248, revenue_rate: 0.7106, distance: 8.7485, memory: -0.0681, power: 0.2640, lr: 0.000100, took: 71.778s
[HYBRID] Epoch 1, Batch 250/1563, loss: 6.822, reward: 30.310, critic_reward: 29.269, revenue_rate: 0.7487, distance: 9.5777, memory: -0.0418, power: 0.2909, lr: 0.000100, took: 79.241s
[HYBRID] Epoch 1, Batch 260/1563, loss: 5.853, reward: 29.996, critic_reward: 30.235, revenue_rate: 0.7368, distance: 9.1698, memory: -0.0464, power: 0.2784, lr: 0.000100, took: 75.936s
[HYBRID] Epoch 1, Batch 270/1563, loss: 5.375, reward: 26.281, critic_reward: 26.111, revenue_rate: 0.6460, distance: 8.0419, memory: -0.0850, power: 0.2435, lr: 0.000100, took: 66.095s
[HYBRID] Epoch 1, Batch 280/1563, loss: 5.151, reward: 27.142, critic_reward: 27.403, revenue_rate: 0.6660, distance: 8.1372, memory: -0.0827, power: 0.2455, lr: 0.000100, took: 66.227s
[HYBRID] Epoch 1, Batch 290/1563, loss: 5.254, reward: 29.039, critic_reward: 28.594, revenue_rate: 0.7130, distance: 8.7722, memory: -0.0673, power: 0.2623, lr: 0.000100, took: 71.629s
[HYBRID] Epoch 1, Batch 300/1563, loss: 6.269, reward: 30.190, critic_reward: 30.148, revenue_rate: 0.7408, distance: 9.1008, memory: -0.0519, power: 0.2744, lr: 0.000100, took: 75.116s
[HYBRID] Epoch 1, Batch 310/1563, loss: 3.560, reward: 28.818, critic_reward: 28.655, revenue_rate: 0.7058, distance: 8.6452, memory: -0.0644, power: 0.2616, lr: 0.000100, took: 70.769s
[HYBRID] Epoch 1, Batch 320/1563, loss: 3.666, reward: 28.323, critic_reward: 28.062, revenue_rate: 0.6907, distance: 8.3860, memory: -0.0726, power: 0.2556, lr: 0.000100, took: 69.232s
[HYBRID] Epoch 1, Batch 330/1563, loss: 6.545, reward: 28.176, critic_reward: 27.696, revenue_rate: 0.6951, distance: 8.8126, memory: -0.0585, power: 0.2660, lr: 0.000100, took: 74.971s
[HYBRID] Epoch 1, Batch 340/1563, loss: 4.571, reward: 28.433, critic_reward: 28.273, revenue_rate: 0.6977, distance: 8.6804, memory: -0.0641, power: 0.2647, lr: 0.000100, took: 71.141s
[HYBRID] Epoch 1, Batch 350/1563, loss: 6.987, reward: 30.419, critic_reward: 31.496, revenue_rate: 0.7432, distance: 9.0047, memory: -0.0491, power: 0.2779, lr: 0.000100, took: 75.251s
[HYBRID] Epoch 1, Batch 360/1563, loss: 4.464, reward: 30.126, critic_reward: 30.328, revenue_rate: 0.7380, distance: 9.0730, memory: -0.0518, power: 0.2751, lr: 0.000100, took: 74.552s
[HYBRID] Epoch 1, Batch 370/1563, loss: 5.962, reward: 31.068, critic_reward: 31.780, revenue_rate: 0.7645, distance: 9.4870, memory: -0.0387, power: 0.2876, lr: 0.000100, took: 78.585s
[HYBRID] Epoch 1, Batch 380/1563, loss: 10.281, reward: 32.418, critic_reward: 32.607, revenue_rate: 0.7941, distance: 9.9463, memory: -0.0268, power: 0.2988, lr: 0.000100, took: 82.991s
[HYBRID] Epoch 1, Batch 390/1563, loss: 9.696, reward: 31.453, critic_reward: 29.783, revenue_rate: 0.7736, distance: 9.6702, memory: -0.0253, power: 0.2936, lr: 0.000100, took: 79.950s
[HYBRID] Epoch 1, Batch 400/1563, loss: 4.529, reward: 31.506, critic_reward: 32.181, revenue_rate: 0.7737, distance: 9.5910, memory: -0.0245, power: 0.2924, lr: 0.000100, took: 80.171s
[HYBRID] Epoch 1, Batch 410/1563, loss: 5.138, reward: 30.872, critic_reward: 30.689, revenue_rate: 0.7605, distance: 9.4540, memory: -0.0338, power: 0.2858, lr: 0.000100, took: 78.974s
[HYBRID] Epoch 1, Batch 420/1563, loss: 3.960, reward: 29.481, critic_reward: 29.295, revenue_rate: 0.7240, distance: 8.8585, memory: -0.0571, power: 0.2662, lr: 0.000100, took: 71.937s
[HYBRID] Epoch 1, Batch 430/1563, loss: 3.767, reward: 26.439, critic_reward: 26.470, revenue_rate: 0.6447, distance: 7.6185, memory: -0.0893, power: 0.2328, lr: 0.000100, took: 63.343s
[HYBRID] Epoch 1, Batch 440/1563, loss: 3.580, reward: 27.865, critic_reward: 27.849, revenue_rate: 0.6831, distance: 8.3187, memory: -0.0797, power: 0.2508, lr: 0.000100, took: 71.770s
[HYBRID] Epoch 1, Batch 450/1563, loss: 4.027, reward: 29.555, critic_reward: 29.779, revenue_rate: 0.7256, distance: 8.9569, memory: -0.0618, power: 0.2716, lr: 0.000100, took: 87.816s
[HYBRID] Epoch 1, Batch 460/1563, loss: 4.380, reward: 28.692, critic_reward: 28.461, revenue_rate: 0.7064, distance: 8.8168, memory: -0.0576, power: 0.2701, lr: 0.000100, took: 98.286s
[HYBRID] Epoch 1, Batch 470/1563, loss: 8.082, reward: 29.773, critic_reward: 28.152, revenue_rate: 0.7305, distance: 9.0091, memory: -0.0600, power: 0.2730, lr: 0.000100, took: 97.288s
[HYBRID] Epoch 1, Batch 480/1563, loss: 8.738, reward: 29.350, critic_reward: 30.030, revenue_rate: 0.7219, distance: 8.7895, memory: -0.0646, power: 0.2672, lr: 0.000100, took: 95.460s
[HYBRID] Epoch 1, Batch 490/1563, loss: 4.879, reward: 30.349, critic_reward: 29.614, revenue_rate: 0.7449, distance: 9.0975, memory: -0.0602, power: 0.2765, lr: 0.000100, took: 100.478s
[HYBRID] Epoch 1, Batch 500/1563, loss: 4.040, reward: 31.222, critic_reward: 30.858, revenue_rate: 0.7678, distance: 9.5620, memory: -0.0292, power: 0.2926, lr: 0.000100, took: 105.439s
[HYBRID] Epoch 1, Batch 510/1563, loss: 3.493, reward: 31.395, critic_reward: 31.292, revenue_rate: 0.7757, distance: 9.6820, memory: -0.0373, power: 0.2917, lr: 0.000100, took: 106.079s
[HYBRID] Epoch 1, Batch 520/1563, loss: 4.938, reward: 30.483, critic_reward: 30.332, revenue_rate: 0.7499, distance: 9.2196, memory: -0.0485, power: 0.2780, lr: 0.000100, took: 100.833s
[HYBRID] Epoch 1, Batch 530/1563, loss: 5.398, reward: 29.078, critic_reward: 30.023, revenue_rate: 0.7106, distance: 8.6280, memory: -0.0604, power: 0.2606, lr: 0.000100, took: 95.369s
[HYBRID] Epoch 1, Batch 540/1563, loss: 10.200, reward: 29.501, critic_reward: 30.002, revenue_rate: 0.7208, distance: 8.7075, memory: -0.0689, power: 0.2667, lr: 0.000100, took: 101.375s
[HYBRID] Epoch 1, Batch 550/1563, loss: 3.171, reward: 28.977, critic_reward: 29.109, revenue_rate: 0.7115, distance: 8.7686, memory: -0.0661, power: 0.2649, lr: 0.000100, took: 93.568s
[HYBRID] Epoch 1, Batch 560/1563, loss: 4.416, reward: 28.340, critic_reward: 27.702, revenue_rate: 0.6964, distance: 8.4892, memory: -0.0608, power: 0.2573, lr: 0.000100, took: 93.875s
[HYBRID] Epoch 1, Batch 570/1563, loss: 3.823, reward: 30.865, critic_reward: 30.816, revenue_rate: 0.7591, distance: 9.3618, memory: -0.0408, power: 0.2828, lr: 0.000100, took: 112.622s
[HYBRID] Epoch 1, Batch 580/1563, loss: 5.001, reward: 33.075, critic_reward: 33.213, revenue_rate: 0.8153, distance: 10.3202, memory: -0.0211, power: 0.3102, lr: 0.000100, took: 111.932s
[HYBRID] Epoch 1, Batch 590/1563, loss: 15.682, reward: 32.519, critic_reward: 31.289, revenue_rate: 0.8014, distance: 10.0886, memory: -0.0235, power: 0.3053, lr: 0.000100, took: 109.155s
[HYBRID] Epoch 1, Batch 600/1563, loss: 4.850, reward: 30.911, critic_reward: 30.817, revenue_rate: 0.7619, distance: 9.5177, memory: -0.0327, power: 0.2876, lr: 0.000100, took: 103.027s
[HYBRID] Epoch 1, Batch 610/1563, loss: 12.452, reward: 30.665, critic_reward: 32.454, revenue_rate: 0.7544, distance: 9.4898, memory: -0.0405, power: 0.2850, lr: 0.000100, took: 103.545s
[HYBRID] Epoch 1, Batch 620/1563, loss: 18.180, reward: 32.190, critic_reward: 30.587, revenue_rate: 0.7943, distance: 10.0464, memory: -0.0154, power: 0.3047, lr: 0.000100, took: 111.620s
[HYBRID] Epoch 1, Batch 630/1563, loss: 7.603, reward: 32.931, critic_reward: 32.947, revenue_rate: 0.8131, distance: 10.3729, memory: -0.0072, power: 0.3150, lr: 0.000100, took: 116.042s
[HYBRID] Epoch 1, Batch 640/1563, loss: 11.511, reward: 31.704, critic_reward: 31.135, revenue_rate: 0.7791, distance: 9.7324, memory: -0.0401, power: 0.2940, lr: 0.000100, took: 106.178s
[HYBRID] Epoch 1, Batch 650/1563, loss: 19.545, reward: 31.177, critic_reward: 33.300, revenue_rate: 0.7663, distance: 9.3397, memory: -0.0417, power: 0.2842, lr: 0.000100, took: 106.101s
[HYBRID] Epoch 1, Batch 660/1563, loss: 12.553, reward: 29.172, critic_reward: 27.859, revenue_rate: 0.7136, distance: 8.6093, memory: -0.0589, power: 0.2613, lr: 0.000100, took: 94.123s
[HYBRID] Epoch 1, Batch 670/1563, loss: 8.352, reward: 28.326, critic_reward: 29.414, revenue_rate: 0.6905, distance: 8.2630, memory: -0.0798, power: 0.2506, lr: 0.000100, took: 90.950s
[HYBRID] Epoch 1, Batch 680/1563, loss: 8.373, reward: 29.876, critic_reward: 27.721, revenue_rate: 0.7315, distance: 8.7880, memory: -0.0517, power: 0.2676, lr: 0.000100, took: 97.891s
[HYBRID] Epoch 1, Batch 690/1563, loss: 9.440, reward: 32.100, critic_reward: 34.108, revenue_rate: 0.7893, distance: 9.8155, memory: -0.0392, power: 0.2946, lr: 0.000100, took: 107.096s
[HYBRID] Epoch 1, Batch 700/1563, loss: 5.006, reward: 32.367, critic_reward: 32.734, revenue_rate: 0.7988, distance: 9.9221, memory: -0.0218, power: 0.3000, lr: 0.000100, took: 109.935s
[HYBRID] Epoch 1, Batch 710/1563, loss: 3.896, reward: 32.076, critic_reward: 31.778, revenue_rate: 0.7871, distance: 9.8084, memory: -0.0259, power: 0.2973, lr: 0.000100, took: 111.199s
[HYBRID] Epoch 1, Batch 720/1563, loss: 4.242, reward: 31.229, critic_reward: 31.332, revenue_rate: 0.7701, distance: 9.5898, memory: -0.0329, power: 0.2886, lr: 0.000100, took: 106.535s
[HYBRID] Epoch 1, Batch 730/1563, loss: 5.809, reward: 31.551, critic_reward: 31.343, revenue_rate: 0.7758, distance: 9.7202, memory: -0.0294, power: 0.2944, lr: 0.000100, took: 105.703s
[HYBRID] Epoch 1, Batch 740/1563, loss: 9.047, reward: 31.809, critic_reward: 32.114, revenue_rate: 0.7819, distance: 9.7293, memory: -0.0354, power: 0.2937, lr: 0.000100, took: 109.642s
[HYBRID] Epoch 1, Batch 750/1563, loss: 5.675, reward: 32.999, critic_reward: 32.891, revenue_rate: 0.8135, distance: 10.2240, memory: -0.0103, power: 0.3104, lr: 0.000100, took: 111.501s
[HYBRID] Epoch 1, Batch 760/1563, loss: 16.756, reward: 32.624, critic_reward: 34.658, revenue_rate: 0.8050, distance: 10.0990, memory: -0.0255, power: 0.3033, lr: 0.000100, took: 111.758s
[HYBRID] Epoch 1, Batch 770/1563, loss: 6.552, reward: 31.771, critic_reward: 32.367, revenue_rate: 0.7783, distance: 9.5939, memory: -0.0369, power: 0.2903, lr: 0.000100, took: 107.330s
[HYBRID] Epoch 1, Batch 780/1563, loss: 5.090, reward: 31.581, critic_reward: 30.654, revenue_rate: 0.7775, distance: 9.6819, memory: -0.0327, power: 0.2931, lr: 0.000100, took: 103.725s
[HYBRID] Epoch 1, Batch 790/1563, loss: 8.331, reward: 32.354, critic_reward: 33.220, revenue_rate: 0.7990, distance: 9.9915, memory: -0.0176, power: 0.3018, lr: 0.000100, took: 93.986s
[HYBRID] Epoch 1, Batch 800/1563, loss: 6.430, reward: 31.721, critic_reward: 31.883, revenue_rate: 0.7805, distance: 9.6704, memory: -0.0297, power: 0.2929, lr: 0.000100, took: 85.703s
[HYBRID] Epoch 1, Batch 810/1563, loss: 7.642, reward: 31.519, critic_reward: 30.387, revenue_rate: 0.7725, distance: 9.5053, memory: -0.0415, power: 0.2895, lr: 0.000100, took: 83.576s
[HYBRID] Epoch 1, Batch 820/1563, loss: 5.171, reward: 30.899, critic_reward: 31.752, revenue_rate: 0.7577, distance: 9.3579, memory: -0.0427, power: 0.2838, lr: 0.000100, took: 86.920s
[HYBRID] Epoch 1, Batch 830/1563, loss: 3.571, reward: 28.244, critic_reward: 28.057, revenue_rate: 0.6894, distance: 8.2580, memory: -0.0822, power: 0.2500, lr: 0.000100, took: 73.543s
[HYBRID] Epoch 1, Batch 840/1563, loss: 3.901, reward: 28.739, critic_reward: 28.858, revenue_rate: 0.7018, distance: 8.4213, memory: -0.0619, power: 0.2566, lr: 0.000100, took: 74.621s
[HYBRID] Epoch 1, Batch 850/1563, loss: 3.614, reward: 30.708, critic_reward: 30.661, revenue_rate: 0.7568, distance: 9.2742, memory: -0.0380, power: 0.2801, lr: 0.000100, took: 80.655s
[HYBRID] Epoch 1, Batch 860/1563, loss: 3.884, reward: 33.402, critic_reward: 33.459, revenue_rate: 0.8197, distance: 10.2047, memory: -0.0054, power: 0.3105, lr: 0.000100, took: 91.377s
[HYBRID] Epoch 1, Batch 870/1563, loss: 4.113, reward: 34.412, critic_reward: 34.562, revenue_rate: 0.8484, distance: 10.8382, memory: 0.0023, power: 0.3284, lr: 0.000100, took: 93.895s
[HYBRID] Epoch 1, Batch 880/1563, loss: 9.625, reward: 33.745, critic_reward: 33.463, revenue_rate: 0.8297, distance: 10.5085, memory: -0.0043, power: 0.3200, lr: 0.000100, took: 93.495s
[HYBRID] Epoch 1, Batch 890/1563, loss: 7.132, reward: 29.733, critic_reward: 31.141, revenue_rate: 0.7280, distance: 8.8453, memory: -0.0561, power: 0.2691, lr: 0.000100, took: 77.411s
[HYBRID] Epoch 1, Batch 900/1563, loss: 5.632, reward: 29.597, critic_reward: 28.440, revenue_rate: 0.7265, distance: 8.8282, memory: -0.0627, power: 0.2670, lr: 0.000100, took: 78.206s
[HYBRID] Epoch 1, Batch 910/1563, loss: 3.577, reward: 32.458, critic_reward: 32.252, revenue_rate: 0.7972, distance: 9.8333, memory: -0.0245, power: 0.2985, lr: 0.000100, took: 84.223s
[HYBRID] Epoch 1, Batch 920/1563, loss: 5.332, reward: 32.572, critic_reward: 32.245, revenue_rate: 0.8061, distance: 10.0504, memory: -0.0194, power: 0.3070, lr: 0.000100, took: 86.626s
[HYBRID] Epoch 1, Batch 930/1563, loss: 5.581, reward: 33.378, critic_reward: 32.852, revenue_rate: 0.8229, distance: 10.3202, memory: -0.0143, power: 0.3129, lr: 0.000100, took: 88.362s
[HYBRID] Epoch 1, Batch 940/1563, loss: 8.025, reward: 32.998, critic_reward: 34.254, revenue_rate: 0.8149, distance: 10.0967, memory: -0.0136, power: 0.3064, lr: 0.000100, took: 87.549s
[HYBRID] Epoch 1, Batch 950/1563, loss: 3.317, reward: 32.328, critic_reward: 32.017, revenue_rate: 0.7955, distance: 9.8808, memory: -0.0364, power: 0.2967, lr: 0.000100, took: 84.479s
[HYBRID] Epoch 1, Batch 960/1563, loss: 5.430, reward: 33.053, critic_reward: 31.818, revenue_rate: 0.8112, distance: 10.0653, memory: -0.0203, power: 0.3051, lr: 0.000100, took: 92.005s
[HYBRID] Epoch 1, Batch 970/1563, loss: 3.659, reward: 33.067, critic_reward: 33.110, revenue_rate: 0.8152, distance: 10.2339, memory: -0.0122, power: 0.3109, lr: 0.000100, took: 92.369s
[HYBRID] Epoch 1, Batch 980/1563, loss: 2.639, reward: 32.425, critic_reward: 32.767, revenue_rate: 0.7981, distance: 10.0737, memory: -0.0140, power: 0.3049, lr: 0.000100, took: 92.200s
[HYBRID] Epoch 1, Batch 990/1563, loss: 3.571, reward: 32.026, critic_reward: 31.590, revenue_rate: 0.7857, distance: 9.6398, memory: -0.0383, power: 0.2927, lr: 0.000100, took: 87.454s
[HYBRID] Epoch 1, Batch 1000/1563, loss: 3.860, reward: 31.231, critic_reward: 31.224, revenue_rate: 0.7620, distance: 9.1553, memory: -0.0446, power: 0.2799, lr: 0.000100, took: 79.465s
[HYBRID] Epoch 1, Batch 1010/1563, loss: 7.244, reward: 30.777, critic_reward: 30.380, revenue_rate: 0.7518, distance: 9.0732, memory: -0.0418, power: 0.2754, lr: 0.000100, took: 77.434s
[HYBRID] Epoch 1, Batch 1020/1563, loss: 10.308, reward: 32.134, critic_reward: 33.923, revenue_rate: 0.7919, distance: 9.7602, memory: -0.0360, power: 0.2950, lr: 0.000100, took: 77.520s
[HYBRID] Epoch 1, Batch 1030/1563, loss: 6.112, reward: 32.477, critic_reward: 31.955, revenue_rate: 0.7958, distance: 9.8663, memory: -0.0242, power: 0.2992, lr: 0.000100, took: 78.557s
[HYBRID] Epoch 1, Batch 1040/1563, loss: 6.067, reward: 33.896, critic_reward: 32.933, revenue_rate: 0.8380, distance: 10.5284, memory: -0.0048, power: 0.3199, lr: 0.000100, took: 87.703s
[HYBRID] Epoch 1, Batch 1050/1563, loss: 4.672, reward: 32.150, critic_reward: 32.327, revenue_rate: 0.7939, distance: 10.0396, memory: -0.0248, power: 0.3058, lr: 0.000100, took: 86.121s
[HYBRID] Epoch 1, Batch 1060/1563, loss: 4.088, reward: 31.604, critic_reward: 31.516, revenue_rate: 0.7802, distance: 9.7915, memory: -0.0332, power: 0.2968, lr: 0.000100, took: 86.533s
[HYBRID] Epoch 1, Batch 1070/1563, loss: 6.733, reward: 34.109, critic_reward: 35.186, revenue_rate: 0.8445, distance: 10.7906, memory: 0.0054, power: 0.3252, lr: 0.000100, took: 92.981s
[HYBRID] Epoch 1, Batch 1080/1563, loss: 5.418, reward: 32.922, critic_reward: 34.070, revenue_rate: 0.8105, distance: 10.1784, memory: -0.0148, power: 0.3085, lr: 0.000100, took: 86.191s
[HYBRID] Epoch 1, Batch 1090/1563, loss: 5.221, reward: 31.513, critic_reward: 30.581, revenue_rate: 0.7729, distance: 9.5713, memory: -0.0348, power: 0.2880, lr: 0.000100, took: 79.698s
[HYBRID] Epoch 1, Batch 1100/1563, loss: 2.791, reward: 32.605, critic_reward: 32.952, revenue_rate: 0.8033, distance: 10.0444, memory: -0.0211, power: 0.3049, lr: 0.000100, took: 84.407s
[HYBRID] Epoch 1, Batch 1110/1563, loss: 4.305, reward: 33.543, critic_reward: 33.656, revenue_rate: 0.8269, distance: 10.3979, memory: -0.0120, power: 0.3146, lr: 0.000100, took: 90.666s
[HYBRID] Epoch 1, Batch 1120/1563, loss: 4.421, reward: 32.224, critic_reward: 32.178, revenue_rate: 0.7897, distance: 9.7654, memory: -0.0312, power: 0.2965, lr: 0.000100, took: 82.248s
[HYBRID] Epoch 1, Batch 1130/1563, loss: 4.940, reward: 30.802, critic_reward: 31.161, revenue_rate: 0.7558, distance: 9.1799, memory: -0.0422, power: 0.2780, lr: 0.000100, took: 77.369s
[HYBRID] Epoch 1, Batch 1140/1563, loss: 11.543, reward: 31.334, critic_reward: 29.757, revenue_rate: 0.7704, distance: 9.5068, memory: -0.0395, power: 0.2904, lr: 0.000100, took: 81.225s
[HYBRID] Epoch 1, Batch 1150/1563, loss: 7.229, reward: 32.356, critic_reward: 34.058, revenue_rate: 0.7971, distance: 9.9139, memory: -0.0189, power: 0.3009, lr: 0.000100, took: 84.748s
[HYBRID] Epoch 1, Batch 1160/1563, loss: 3.689, reward: 31.323, critic_reward: 31.898, revenue_rate: 0.7684, distance: 9.3490, memory: -0.0371, power: 0.2852, lr: 0.000100, took: 78.970s
[HYBRID] Epoch 1, Batch 1170/1563, loss: 5.064, reward: 30.705, critic_reward: 31.197, revenue_rate: 0.7513, distance: 9.0427, memory: -0.0505, power: 0.2762, lr: 0.000100, took: 76.331s
[HYBRID] Epoch 1, Batch 1180/1563, loss: 3.344, reward: 30.721, critic_reward: 30.473, revenue_rate: 0.7553, distance: 9.1634, memory: -0.0374, power: 0.2781, lr: 0.000100, took: 76.649s
[HYBRID] Epoch 1, Batch 1190/1563, loss: 4.537, reward: 31.832, critic_reward: 32.156, revenue_rate: 0.7789, distance: 9.5102, memory: -0.0443, power: 0.2887, lr: 0.000100, took: 80.174s
[HYBRID] Epoch 1, Batch 1200/1563, loss: 3.591, reward: 30.843, critic_reward: 30.971, revenue_rate: 0.7556, distance: 9.0855, memory: -0.0496, power: 0.2788, lr: 0.000100, took: 76.756s
[HYBRID] Epoch 1, Batch 1210/1563, loss: 4.362, reward: 31.313, critic_reward: 30.666, revenue_rate: 0.7710, distance: 9.4717, memory: -0.0393, power: 0.2853, lr: 0.000100, took: 79.984s
[HYBRID] Epoch 1, Batch 1220/1563, loss: 4.822, reward: 31.121, critic_reward: 31.370, revenue_rate: 0.7624, distance: 9.2507, memory: -0.0425, power: 0.2811, lr: 0.000100, took: 77.573s
[HYBRID] Epoch 1, Batch 1230/1563, loss: 10.205, reward: 30.842, critic_reward: 31.341, revenue_rate: 0.7559, distance: 9.1422, memory: -0.0539, power: 0.2763, lr: 0.000100, took: 78.563s
[HYBRID] Epoch 1, Batch 1240/1563, loss: 5.362, reward: 30.038, critic_reward: 29.681, revenue_rate: 0.7347, distance: 8.7377, memory: -0.0557, power: 0.2671, lr: 0.000100, took: 73.182s
[HYBRID] Epoch 1, Batch 1250/1563, loss: 2.473, reward: 30.319, critic_reward: 30.359, revenue_rate: 0.7467, distance: 9.0054, memory: -0.0487, power: 0.2725, lr: 0.000100, took: 75.164s
[HYBRID] Epoch 1, Batch 1260/1563, loss: 3.055, reward: 31.340, critic_reward: 31.471, revenue_rate: 0.7674, distance: 9.3245, memory: -0.0411, power: 0.2845, lr: 0.000100, took: 78.165s
[HYBRID] Epoch 1, Batch 1270/1563, loss: 2.989, reward: 32.010, critic_reward: 32.428, revenue_rate: 0.7839, distance: 9.6629, memory: -0.0340, power: 0.2936, lr: 0.000100, took: 81.204s
[HYBRID] Epoch 1, Batch 1280/1563, loss: 4.118, reward: 32.745, critic_reward: 32.509, revenue_rate: 0.8077, distance: 10.1045, memory: -0.0182, power: 0.3068, lr: 0.000100, took: 86.058s
[HYBRID] Epoch 1, Batch 1290/1563, loss: 3.919, reward: 34.859, critic_reward: 35.439, revenue_rate: 0.8702, distance: 11.3437, memory: 0.0262, power: 0.3438, lr: 0.000100, took: 98.248s
[HYBRID] Epoch 1, Batch 1300/1563, loss: 7.740, reward: 35.641, critic_reward: 36.444, revenue_rate: 0.8890, distance: 11.8695, memory: 0.0461, power: 0.3606, lr: 0.000100, took: 102.540s
[HYBRID] Epoch 1, Batch 1310/1563, loss: 4.036, reward: 32.653, critic_reward: 31.983, revenue_rate: 0.8040, distance: 10.1667, memory: -0.0127, power: 0.3097, lr: 0.000100, took: 86.264s
[HYBRID] Epoch 1, Batch 1320/1563, loss: 3.882, reward: 30.783, critic_reward: 30.990, revenue_rate: 0.7534, distance: 9.1441, memory: -0.0524, power: 0.2779, lr: 0.000100, took: 76.748s
[HYBRID] Epoch 1, Batch 1330/1563, loss: 4.751, reward: 30.397, critic_reward: 30.175, revenue_rate: 0.7428, distance: 8.9399, memory: -0.0568, power: 0.2716, lr: 0.000100, took: 76.612s
[HYBRID] Epoch 1, Batch 1340/1563, loss: 7.409, reward: 30.134, critic_reward: 31.764, revenue_rate: 0.7347, distance: 8.7421, memory: -0.0644, power: 0.2673, lr: 0.000100, took: 73.733s
[HYBRID] Epoch 1, Batch 1350/1563, loss: 4.290, reward: 29.963, critic_reward: 29.776, revenue_rate: 0.7323, distance: 8.8197, memory: -0.0639, power: 0.2680, lr: 0.000100, took: 73.600s
[HYBRID] Epoch 1, Batch 1360/1563, loss: 2.717, reward: 29.784, critic_reward: 29.826, revenue_rate: 0.7262, distance: 8.6763, memory: -0.0562, power: 0.2649, lr: 0.000100, took: 72.881s
[HYBRID] Epoch 1, Batch 1370/1563, loss: 3.179, reward: 28.699, critic_reward: 28.644, revenue_rate: 0.6980, distance: 8.2822, memory: -0.0741, power: 0.2535, lr: 0.000100, took: 69.199s
[HYBRID] Epoch 1, Batch 1380/1563, loss: 3.303, reward: 30.472, critic_reward: 30.752, revenue_rate: 0.7450, distance: 9.0278, memory: -0.0533, power: 0.2721, lr: 0.000100, took: 75.831s
[HYBRID] Epoch 1, Batch 1390/1563, loss: 3.496, reward: 29.731, critic_reward: 29.797, revenue_rate: 0.7247, distance: 8.7092, memory: -0.0633, power: 0.2631, lr: 0.000100, took: 72.758s
[HYBRID] Epoch 1, Batch 1400/1563, loss: 8.749, reward: 30.439, critic_reward: 30.477, revenue_rate: 0.7449, distance: 9.1116, memory: -0.0474, power: 0.2742, lr: 0.000100, took: 75.604s
[HYBRID] Epoch 1, Batch 1410/1563, loss: 9.959, reward: 31.320, critic_reward: 31.949, revenue_rate: 0.7685, distance: 9.3850, memory: -0.0320, power: 0.2860, lr: 0.000100, took: 79.606s
[HYBRID] Epoch 1, Batch 1420/1563, loss: 4.184, reward: 31.650, critic_reward: 32.609, revenue_rate: 0.7747, distance: 9.4073, memory: -0.0357, power: 0.2881, lr: 0.000100, took: 79.945s
[HYBRID] Epoch 1, Batch 1430/1563, loss: 5.805, reward: 29.197, critic_reward: 30.272, revenue_rate: 0.7130, distance: 8.5481, memory: -0.0606, power: 0.2617, lr: 0.000100, took: 71.119s
[HYBRID] Epoch 1, Batch 1440/1563, loss: 6.884, reward: 25.074, critic_reward: 24.498, revenue_rate: 0.6100, distance: 7.1166, memory: -0.1005, power: 0.2203, lr: 0.000100, took: 59.565s
[HYBRID] Epoch 1, Batch 1450/1563, loss: 5.436, reward: 25.728, critic_reward: 25.580, revenue_rate: 0.6269, distance: 7.4862, memory: -0.0942, power: 0.2286, lr: 0.000100, took: 61.988s
[HYBRID] Epoch 1, Batch 1460/1563, loss: 4.872, reward: 27.623, critic_reward: 25.997, revenue_rate: 0.6734, distance: 8.1026, memory: -0.0744, power: 0.2467, lr: 0.000100, took: 69.841s
[HYBRID] Epoch 1, Batch 1470/1563, loss: 6.407, reward: 32.436, critic_reward: 34.246, revenue_rate: 0.7975, distance: 9.9914, memory: -0.0235, power: 0.3030, lr: 0.000100, took: 84.368s
[HYBRID] Epoch 1, Batch 1480/1563, loss: 3.448, reward: 31.169, critic_reward: 30.693, revenue_rate: 0.7687, distance: 9.4583, memory: -0.0402, power: 0.2857, lr: 0.000100, took: 79.840s
[HYBRID] Epoch 1, Batch 1490/1563, loss: 5.901, reward: 30.098, critic_reward: 31.570, revenue_rate: 0.7345, distance: 8.8144, memory: -0.0563, power: 0.2709, lr: 0.000100, took: 74.066s
[HYBRID] Epoch 1, Batch 1500/1563, loss: 3.879, reward: 29.300, critic_reward: 29.100, revenue_rate: 0.7164, distance: 8.4706, memory: -0.0648, power: 0.2587, lr: 0.000100, took: 71.409s
[HYBRID] Epoch 1, Batch 1510/1563, loss: 5.988, reward: 29.496, critic_reward: 29.914, revenue_rate: 0.7187, distance: 8.6281, memory: -0.0663, power: 0.2601, lr: 0.000100, took: 71.614s
[HYBRID] Epoch 1, Batch 1520/1563, loss: 3.366, reward: 30.026, critic_reward: 29.845, revenue_rate: 0.7329, distance: 8.6423, memory: -0.0570, power: 0.2652, lr: 0.000100, took: 73.158s
[HYBRID] Epoch 1, Batch 1530/1563, loss: 5.845, reward: 31.261, critic_reward: 31.796, revenue_rate: 0.7662, distance: 9.2601, memory: -0.0466, power: 0.2813, lr: 0.000100, took: 78.204s
[HYBRID] Epoch 1, Batch 1540/1563, loss: 3.210, reward: 31.112, critic_reward: 30.777, revenue_rate: 0.7603, distance: 9.1213, memory: -0.0429, power: 0.2775, lr: 0.000100, took: 76.552s
[HYBRID] Epoch 1, Batch 1550/1563, loss: 3.388, reward: 30.311, critic_reward: 30.443, revenue_rate: 0.7401, distance: 8.8510, memory: -0.0546, power: 0.2688, lr: 0.000100, took: 74.366s
[HYBRID] Epoch 1, Batch 1560/1563, loss: 4.648, reward: 30.540, critic_reward: 30.627, revenue_rate: 0.7436, distance: 8.9029, memory: -0.0488, power: 0.2733, lr: 0.000100, took: 75.366s
[HYBRID] 开始验证...
[HYBRID] 验证完成 - Epoch 1, reward: 31.640, revenue_rate: 0.7765, distance: 9.4674, memory: -0.0373, power: 0.2878
[HYBRID] 已保存新模型到 constellation_smp\constellation_smp100\multi_constellation_comparison_2025_08_25_17_02_29\constellation_gpnindrnn_hybrid_2025_08_26_17_20_30 (验证集奖励: 31.6401)
[HYBRID] 开始训练 Epoch 2/3
[HYBRID] Epoch 2, Batch 10/1563, loss: 3.121, reward: 31.749, critic_reward: 31.246, revenue_rate: 0.7812, distance: 9.6280, memory: -0.0349, power: 0.2908, lr: 0.000100, took: 86.316s
[HYBRID] Epoch 2, Batch 20/1563, loss: 5.211, reward: 33.117, critic_reward: 34.128, revenue_rate: 0.8148, distance: 10.0660, memory: -0.0249, power: 0.3046, lr: 0.000100, took: 85.193s
[HYBRID] Epoch 2, Batch 30/1563, loss: 3.767, reward: 33.097, critic_reward: 33.880, revenue_rate: 0.8120, distance: 9.9469, memory: -0.0181, power: 0.3042, lr: 0.000100, took: 85.529s
[HYBRID] Epoch 2, Batch 40/1563, loss: 3.698, reward: 31.703, critic_reward: 31.238, revenue_rate: 0.7786, distance: 9.4643, memory: -0.0355, power: 0.2871, lr: 0.000100, took: 80.383s
[HYBRID] Epoch 2, Batch 50/1563, loss: 4.028, reward: 30.786, critic_reward: 30.643, revenue_rate: 0.7530, distance: 9.0815, memory: -0.0505, power: 0.2755, lr: 0.000100, took: 76.150s
[HYBRID] Epoch 2, Batch 60/1563, loss: 3.685, reward: 30.684, critic_reward: 31.009, revenue_rate: 0.7515, distance: 9.0718, memory: -0.0516, power: 0.2765, lr: 0.000100, took: 76.151s
[HYBRID] Epoch 2, Batch 70/1563, loss: 3.118, reward: 29.715, critic_reward: 29.048, revenue_rate: 0.7269, distance: 8.7230, memory: -0.0568, power: 0.2646, lr: 0.000100, took: 75.013s
[HYBRID] Epoch 2, Batch 80/1563, loss: 3.756, reward: 32.201, critic_reward: 32.531, revenue_rate: 0.7899, distance: 9.7465, memory: -0.0410, power: 0.2937, lr: 0.000100, took: 82.037s
[HYBRID] Epoch 2, Batch 90/1563, loss: 3.317, reward: 32.830, critic_reward: 33.384, revenue_rate: 0.8083, distance: 9.9443, memory: -0.0288, power: 0.3000, lr: 0.000100, took: 84.552s
[HYBRID] Epoch 2, Batch 100/1563, loss: 2.156, reward: 32.686, critic_reward: 32.398, revenue_rate: 0.8054, distance: 9.8999, memory: -0.0214, power: 0.2994, lr: 0.000100, took: 83.600s
[HYBRID] Epoch 2, Batch 110/1563, loss: 3.784, reward: 33.727, critic_reward: 34.131, revenue_rate: 0.8333, distance: 10.5022, memory: -0.0025, power: 0.3166, lr: 0.000100, took: 88.750s
[HYBRID] Epoch 2, Batch 120/1563, loss: 5.098, reward: 34.184, critic_reward: 34.309, revenue_rate: 0.8462, distance: 10.7332, memory: 0.0088, power: 0.3253, lr: 0.000100, took: 91.868s
[HYBRID] Epoch 2, Batch 130/1563, loss: 2.262, reward: 34.609, critic_reward: 34.373, revenue_rate: 0.8563, distance: 10.7636, memory: -0.0003, power: 0.3290, lr: 0.000100, took: 93.184s
[HYBRID] Epoch 2, Batch 140/1563, loss: 7.427, reward: 33.883, critic_reward: 35.594, revenue_rate: 0.8333, distance: 10.3977, memory: -0.0172, power: 0.3146, lr: 0.000100, took: 88.666s
[HYBRID] Epoch 2, Batch 150/1563, loss: 7.379, reward: 33.055, critic_reward: 32.039, revenue_rate: 0.8118, distance: 9.9704, memory: -0.0238, power: 0.3016, lr: 0.000100, took: 84.963s
[HYBRID] Epoch 2, Batch 160/1563, loss: 14.206, reward: 31.737, critic_reward: 34.212, revenue_rate: 0.7753, distance: 9.3141, memory: -0.0372, power: 0.2856, lr: 0.000100, took: 79.548s
[HYBRID] Epoch 2, Batch 170/1563, loss: 3.834, reward: 31.237, critic_reward: 31.778, revenue_rate: 0.7627, distance: 9.1785, memory: -0.0451, power: 0.2789, lr: 0.000100, took: 79.756s
[HYBRID] Epoch 2, Batch 180/1563, loss: 5.375, reward: 32.183, critic_reward: 30.944, revenue_rate: 0.7918, distance: 9.7693, memory: -0.0334, power: 0.2957, lr: 0.000100, took: 82.602s
[HYBRID] Epoch 2, Batch 190/1563, loss: 9.937, reward: 32.106, critic_reward: 34.286, revenue_rate: 0.7877, distance: 9.6446, memory: -0.0288, power: 0.2925, lr: 0.000100, took: 81.267s
[HYBRID] Epoch 2, Batch 200/1563, loss: 5.953, reward: 33.072, critic_reward: 33.170, revenue_rate: 0.8138, distance: 10.0796, memory: -0.0165, power: 0.3087, lr: 0.000100, took: 86.141s
[HYBRID] Epoch 2, Batch 210/1563, loss: 3.371, reward: 33.220, critic_reward: 33.182, revenue_rate: 0.8149, distance: 9.9567, memory: -0.0280, power: 0.3044, lr: 0.000100, took: 85.578s
[HYBRID] Epoch 2, Batch 220/1563, loss: 2.794, reward: 32.635, critic_reward: 33.112, revenue_rate: 0.7984, distance: 9.8350, memory: -0.0323, power: 0.2944, lr: 0.000100, took: 82.160s
[HYBRID] Epoch 2, Batch 230/1563, loss: 3.737, reward: 32.407, critic_reward: 32.935, revenue_rate: 0.7962, distance: 9.8678, memory: -0.0245, power: 0.2996, lr: 0.000100, took: 83.512s
[HYBRID] Epoch 2, Batch 240/1563, loss: 3.984, reward: 30.715, critic_reward: 29.907, revenue_rate: 0.7517, distance: 9.0919, memory: -0.0462, power: 0.2765, lr: 0.000100, took: 76.383s
[HYBRID] Epoch 2, Batch 250/1563, loss: 3.320, reward: 31.213, critic_reward: 31.153, revenue_rate: 0.7629, distance: 9.2261, memory: -0.0366, power: 0.2814, lr: 0.000100, took: 77.595s
[HYBRID] Epoch 2, Batch 260/1563, loss: 4.146, reward: 30.965, critic_reward: 30.535, revenue_rate: 0.7553, distance: 9.0750, memory: -0.0478, power: 0.2774, lr: 0.000100, took: 76.171s
[HYBRID] Epoch 2, Batch 270/1563, loss: 4.602, reward: 31.068, critic_reward: 31.065, revenue_rate: 0.7625, distance: 9.2678, memory: -0.0455, power: 0.2815, lr: 0.000100, took: 78.198s
[HYBRID] Epoch 2, Batch 280/1563, loss: 3.941, reward: 32.147, critic_reward: 32.877, revenue_rate: 0.7904, distance: 9.7581, memory: -0.0274, power: 0.2970, lr: 0.000100, took: 82.785s
[HYBRID] Epoch 2, Batch 290/1563, loss: 3.754, reward: 32.026, critic_reward: 31.699, revenue_rate: 0.7863, distance: 9.6174, memory: -0.0279, power: 0.2917, lr: 0.000100, took: 83.935s
[HYBRID] Epoch 2, Batch 300/1563, loss: 2.634, reward: 32.064, critic_reward: 32.360, revenue_rate: 0.7837, distance: 9.4526, memory: -0.0361, power: 0.2891, lr: 0.000100, took: 81.136s
[HYBRID] Epoch 2, Batch 310/1563, loss: 2.566, reward: 32.208, critic_reward: 32.275, revenue_rate: 0.7880, distance: 9.5279, memory: -0.0454, power: 0.2910, lr: 0.000100, took: 81.190s
[HYBRID] Epoch 2, Batch 320/1563, loss: 3.951, reward: 30.147, critic_reward: 30.580, revenue_rate: 0.7368, distance: 8.8265, memory: -0.0497, power: 0.2697, lr: 0.000100, took: 73.975s
[HYBRID] Epoch 2, Batch 330/1563, loss: 3.905, reward: 30.062, critic_reward: 29.344, revenue_rate: 0.7341, distance: 8.7581, memory: -0.0541, power: 0.2686, lr: 0.000100, took: 73.191s
[HYBRID] Epoch 2, Batch 340/1563, loss: 7.164, reward: 33.081, critic_reward: 33.612, revenue_rate: 0.8113, distance: 9.9667, memory: -0.0202, power: 0.3040, lr: 0.000100, took: 85.186s
[HYBRID] Epoch 2, Batch 350/1563, loss: 2.822, reward: 33.432, critic_reward: 33.106, revenue_rate: 0.8227, distance: 10.1680, memory: -0.0182, power: 0.3092, lr: 0.000100, took: 86.411s
[HYBRID] Epoch 2, Batch 360/1563, loss: 3.651, reward: 33.364, critic_reward: 33.586, revenue_rate: 0.8198, distance: 10.1722, memory: -0.0146, power: 0.3081, lr: 0.000100, took: 87.344s
[HYBRID] Epoch 2, Batch 370/1563, loss: 2.804, reward: 32.765, critic_reward: 32.832, revenue_rate: 0.8029, distance: 9.7578, memory: -0.0327, power: 0.2992, lr: 0.000100, took: 83.767s
[HYBRID] Epoch 2, Batch 380/1563, loss: 3.357, reward: 30.858, critic_reward: 30.900, revenue_rate: 0.7531, distance: 8.9551, memory: -0.0552, power: 0.2742, lr: 0.000100, took: 75.734s
[HYBRID] Epoch 2, Batch 390/1563, loss: 3.985, reward: 31.224, critic_reward: 30.807, revenue_rate: 0.7648, distance: 9.2285, memory: -0.0496, power: 0.2792, lr: 0.000100, took: 76.656s
[HYBRID] Epoch 2, Batch 400/1563, loss: 3.402, reward: 32.761, critic_reward: 32.850, revenue_rate: 0.8031, distance: 9.8818, memory: -0.0259, power: 0.3004, lr: 0.000100, took: 85.721s
[HYBRID] Epoch 2, Batch 410/1563, loss: 2.543, reward: 31.405, critic_reward: 31.658, revenue_rate: 0.7652, distance: 9.1729, memory: -0.0436, power: 0.2795, lr: 0.000100, took: 77.165s
[HYBRID] Epoch 2, Batch 420/1563, loss: 4.708, reward: 33.248, critic_reward: 32.852, revenue_rate: 0.8153, distance: 10.1098, memory: -0.0286, power: 0.3056, lr: 0.000100, took: 85.881s
[HYBRID] Epoch 2, Batch 430/1563, loss: 2.295, reward: 33.386, critic_reward: 33.457, revenue_rate: 0.8173, distance: 10.0324, memory: -0.0163, power: 0.3080, lr: 0.000100, took: 85.717s
[HYBRID] Epoch 2, Batch 440/1563, loss: 2.237, reward: 32.798, critic_reward: 33.113, revenue_rate: 0.8074, distance: 9.8402, memory: -0.0252, power: 0.2979, lr: 0.000100, took: 83.737s
[HYBRID] Epoch 2, Batch 450/1563, loss: 7.152, reward: 32.484, critic_reward: 32.234, revenue_rate: 0.7940, distance: 9.6696, memory: -0.0398, power: 0.2939, lr: 0.000100, took: 81.781s
[HYBRID] Epoch 2, Batch 460/1563, loss: 3.664, reward: 32.045, critic_reward: 32.253, revenue_rate: 0.7866, distance: 9.6139, memory: -0.0342, power: 0.2922, lr: 0.000100, took: 81.187s
[HYBRID] Epoch 2, Batch 470/1563, loss: 12.928, reward: 31.222, critic_reward: 33.449, revenue_rate: 0.7634, distance: 9.1877, memory: -0.0418, power: 0.2819, lr: 0.000100, took: 77.743s
[HYBRID] Epoch 2, Batch 480/1563, loss: 5.949, reward: 33.062, critic_reward: 32.575, revenue_rate: 0.8107, distance: 9.9143, memory: -0.0301, power: 0.3021, lr: 0.000100, took: 85.038s
[HYBRID] Epoch 2, Batch 490/1563, loss: 4.241, reward: 33.219, critic_reward: 32.957, revenue_rate: 0.8157, distance: 9.9751, memory: -0.0264, power: 0.3024, lr: 0.000100, took: 84.348s
[HYBRID] Epoch 2, Batch 500/1563, loss: 3.448, reward: 33.360, critic_reward: 32.891, revenue_rate: 0.8215, distance: 10.1919, memory: -0.0115, power: 0.3072, lr: 0.000100, took: 88.626s
[HYBRID] Epoch 2, Batch 510/1563, loss: 3.812, reward: 33.653, critic_reward: 34.230, revenue_rate: 0.8270, distance: 10.1626, memory: -0.0222, power: 0.3098, lr: 0.000100, took: 87.513s
[HYBRID] Epoch 2, Batch 520/1563, loss: 4.077, reward: 29.367, critic_reward: 30.074, revenue_rate: 0.7189, distance: 8.5561, memory: -0.0594, power: 0.2571, lr: 0.000100, took: 70.893s
[HYBRID] Epoch 2, Batch 530/1563, loss: 3.636, reward: 26.703, critic_reward: 26.376, revenue_rate: 0.6496, distance: 7.6233, memory: -0.0946, power: 0.2315, lr: 0.000100, took: 62.576s
[HYBRID] Epoch 2, Batch 540/1563, loss: 4.773, reward: 29.058, critic_reward: 28.023, revenue_rate: 0.7108, distance: 8.3422, memory: -0.0705, power: 0.2557, lr: 0.000100, took: 69.452s
[HYBRID] Epoch 2, Batch 550/1563, loss: 4.478, reward: 32.205, critic_reward: 31.637, revenue_rate: 0.7912, distance: 9.5753, memory: -0.0351, power: 0.2912, lr: 0.000100, took: 80.785s
[HYBRID] Epoch 2, Batch 560/1563, loss: 3.291, reward: 32.738, critic_reward: 33.088, revenue_rate: 0.8028, distance: 9.8249, memory: -0.0337, power: 0.2982, lr: 0.000100, took: 83.150s
[HYBRID] Epoch 2, Batch 570/1563, loss: 3.586, reward: 29.624, critic_reward: 29.274, revenue_rate: 0.7223, distance: 8.5127, memory: -0.0673, power: 0.2589, lr: 0.000100, took: 71.706s
[HYBRID] Epoch 2, Batch 580/1563, loss: 3.968, reward: 29.479, critic_reward: 29.201, revenue_rate: 0.7209, distance: 8.5430, memory: -0.0628, power: 0.2594, lr: 0.000100, took: 71.231s
[HYBRID] Epoch 2, Batch 590/1563, loss: 3.509, reward: 31.539, critic_reward: 32.116, revenue_rate: 0.7710, distance: 9.3246, memory: -0.0376, power: 0.2829, lr: 0.000100, took: 78.219s
[HYBRID] Epoch 2, Batch 600/1563, loss: 2.880, reward: 32.103, critic_reward: 32.358, revenue_rate: 0.7866, distance: 9.5384, memory: -0.0244, power: 0.2907, lr: 0.000100, took: 80.380s
[HYBRID] Epoch 2, Batch 610/1563, loss: 3.226, reward: 32.781, critic_reward: 32.400, revenue_rate: 0.8048, distance: 9.8666, memory: -0.0242, power: 0.2997, lr: 0.000100, took: 84.973s
[HYBRID] Epoch 2, Batch 620/1563, loss: 3.607, reward: 32.980, critic_reward: 33.776, revenue_rate: 0.8089, distance: 9.9240, memory: -0.0313, power: 0.3009, lr: 0.000100, took: 87.425s
[HYBRID] Epoch 2, Batch 630/1563, loss: 2.952, reward: 30.531, critic_reward: 31.096, revenue_rate: 0.7440, distance: 8.8819, memory: -0.0519, power: 0.2709, lr: 0.000100, took: 74.450s
[HYBRID] Epoch 2, Batch 640/1563, loss: 2.701, reward: 28.054, critic_reward: 27.824, revenue_rate: 0.6832, distance: 8.0308, memory: -0.0747, power: 0.2437, lr: 0.000100, took: 66.598s
[HYBRID] Epoch 2, Batch 650/1563, loss: 2.640, reward: 26.998, critic_reward: 27.210, revenue_rate: 0.6585, distance: 7.7038, memory: -0.0881, power: 0.2341, lr: 0.000100, took: 63.566s
[HYBRID] Epoch 2, Batch 660/1563, loss: 3.076, reward: 29.438, critic_reward: 29.201, revenue_rate: 0.7196, distance: 8.5987, memory: -0.0576, power: 0.2619, lr: 0.000100, took: 71.607s
[HYBRID] Epoch 2, Batch 670/1563, loss: 5.073, reward: 30.602, critic_reward: 30.697, revenue_rate: 0.7464, distance: 8.8808, memory: -0.0519, power: 0.2703, lr: 0.000100, took: 75.355s
[HYBRID] Epoch 2, Batch 680/1563, loss: 4.692, reward: 30.397, critic_reward: 29.777, revenue_rate: 0.7412, distance: 8.8170, memory: -0.0661, power: 0.2676, lr: 0.000100, took: 74.184s
[HYBRID] Epoch 2, Batch 690/1563, loss: 1.888, reward: 31.639, critic_reward: 31.530, revenue_rate: 0.7734, distance: 9.3197, memory: -0.0328, power: 0.2835, lr: 0.000100, took: 78.437s
[HYBRID] Epoch 2, Batch 700/1563, loss: 3.159, reward: 33.349, critic_reward: 33.041, revenue_rate: 0.8205, distance: 10.1118, memory: -0.0232, power: 0.3062, lr: 0.000100, took: 85.378s
[HYBRID] Epoch 2, Batch 710/1563, loss: 4.027, reward: 34.339, critic_reward: 35.281, revenue_rate: 0.8432, distance: 10.5889, memory: -0.0109, power: 0.3205, lr: 0.000100, took: 90.589s
[HYBRID] Epoch 2, Batch 720/1563, loss: 4.634, reward: 32.899, critic_reward: 32.002, revenue_rate: 0.8116, distance: 9.9912, memory: -0.0127, power: 0.3043, lr: 0.000100, took: 85.555s
[HYBRID] Epoch 2, Batch 730/1563, loss: 3.128, reward: 32.138, critic_reward: 31.876, revenue_rate: 0.7894, distance: 9.5655, memory: -0.0344, power: 0.2902, lr: 0.000100, took: 80.557s
[HYBRID] Epoch 2, Batch 740/1563, loss: 6.376, reward: 31.341, critic_reward: 31.930, revenue_rate: 0.7682, distance: 9.2732, memory: -0.0370, power: 0.2809, lr: 0.000100, took: 79.552s
[HYBRID] Epoch 2, Batch 750/1563, loss: 8.085, reward: 33.296, critic_reward: 33.693, revenue_rate: 0.8198, distance: 10.2035, memory: -0.0121, power: 0.3087, lr: 0.000100, took: 86.849s
[HYBRID] Epoch 2, Batch 760/1563, loss: 5.409, reward: 32.778, critic_reward: 32.680, revenue_rate: 0.8062, distance: 9.8875, memory: -0.0141, power: 0.3032, lr: 0.000100, took: 85.117s
[HYBRID] Epoch 2, Batch 770/1563, loss: 3.713, reward: 32.921, critic_reward: 33.675, revenue_rate: 0.8091, distance: 9.9281, memory: -0.0216, power: 0.3005, lr: 0.000100, took: 84.325s
[HYBRID] Epoch 2, Batch 780/1563, loss: 3.326, reward: 31.834, critic_reward: 31.310, revenue_rate: 0.7768, distance: 9.4672, memory: -0.0386, power: 0.2867, lr: 0.000100, took: 79.438s
[HYBRID] Epoch 2, Batch 790/1563, loss: 5.153, reward: 32.285, critic_reward: 31.922, revenue_rate: 0.7938, distance: 9.6588, memory: -0.0245, power: 0.2939, lr: 0.000100, took: 81.791s
[HYBRID] Epoch 2, Batch 800/1563, loss: 2.896, reward: 32.433, critic_reward: 32.500, revenue_rate: 0.7977, distance: 9.7985, memory: -0.0235, power: 0.2964, lr: 0.000100, took: 82.338s
[HYBRID] Epoch 2, Batch 810/1563, loss: 2.952, reward: 32.186, critic_reward: 32.181, revenue_rate: 0.7880, distance: 9.5751, memory: -0.0327, power: 0.2913, lr: 0.000100, took: 80.391s
[HYBRID] Epoch 2, Batch 820/1563, loss: 4.168, reward: 31.040, critic_reward: 30.828, revenue_rate: 0.7588, distance: 9.0754, memory: -0.0531, power: 0.2768, lr: 0.000100, took: 76.154s
[HYBRID] Epoch 2, Batch 830/1563, loss: 2.798, reward: 30.463, critic_reward: 30.147, revenue_rate: 0.7429, distance: 8.8071, memory: -0.0559, power: 0.2684, lr: 0.000100, took: 74.457s
[HYBRID] Epoch 2, Batch 840/1563, loss: 3.260, reward: 32.716, critic_reward: 32.766, revenue_rate: 0.8020, distance: 9.7892, memory: -0.0222, power: 0.2991, lr: 0.000100, took: 85.397s
[HYBRID] Epoch 2, Batch 850/1563, loss: 4.886, reward: 33.240, critic_reward: 33.944, revenue_rate: 0.8176, distance: 9.9997, memory: -0.0093, power: 0.3050, lr: 0.000100, took: 88.902s
[HYBRID] Epoch 2, Batch 860/1563, loss: 4.837, reward: 31.808, critic_reward: 31.256, revenue_rate: 0.7760, distance: 9.4257, memory: -0.0488, power: 0.2860, lr: 0.000100, took: 79.340s
[HYBRID] Epoch 2, Batch 870/1563, loss: 2.081, reward: 30.497, critic_reward: 30.382, revenue_rate: 0.7435, distance: 8.8100, memory: -0.0580, power: 0.2670, lr: 0.000100, took: 73.384s
[HYBRID] Epoch 2, Batch 880/1563, loss: 5.711, reward: 29.565, critic_reward: 30.301, revenue_rate: 0.7210, distance: 8.5059, memory: -0.0711, power: 0.2582, lr: 0.000100, took: 71.531s
[HYBRID] Epoch 2, Batch 890/1563, loss: 6.534, reward: 31.162, critic_reward: 30.250, revenue_rate: 0.7614, distance: 9.1388, memory: -0.0408, power: 0.2779, lr: 0.000100, took: 77.221s
[HYBRID] Epoch 2, Batch 900/1563, loss: 2.971, reward: 33.122, critic_reward: 33.537, revenue_rate: 0.8137, distance: 10.0277, memory: -0.0307, power: 0.3022, lr: 0.000100, took: 83.988s
[HYBRID] Epoch 2, Batch 910/1563, loss: 2.835, reward: 32.036, critic_reward: 31.671, revenue_rate: 0.7850, distance: 9.5360, memory: -0.0339, power: 0.2898, lr: 0.000100, took: 80.573s
[HYBRID] Epoch 2, Batch 920/1563, loss: 2.747, reward: 32.926, critic_reward: 32.788, revenue_rate: 0.8060, distance: 9.8113, memory: -0.0290, power: 0.2994, lr: 0.000100, took: 83.198s
[HYBRID] Epoch 2, Batch 930/1563, loss: 2.622, reward: 32.243, critic_reward: 32.308, revenue_rate: 0.7919, distance: 9.5319, memory: -0.0338, power: 0.2908, lr: 0.000100, took: 81.184s
[HYBRID] Epoch 2, Batch 940/1563, loss: 3.324, reward: 32.975, critic_reward: 32.635, revenue_rate: 0.8100, distance: 9.9440, memory: -0.0268, power: 0.3005, lr: 0.000100, took: 84.627s
[HYBRID] Epoch 2, Batch 950/1563, loss: 3.110, reward: 31.731, critic_reward: 32.279, revenue_rate: 0.7784, distance: 9.4259, memory: -0.0313, power: 0.2898, lr: 0.000100, took: 80.103s
[HYBRID] Epoch 2, Batch 960/1563, loss: 5.773, reward: 31.987, critic_reward: 32.621, revenue_rate: 0.7833, distance: 9.4750, memory: -0.0373, power: 0.2880, lr: 0.000100, took: 82.984s
[HYBRID] Epoch 2, Batch 970/1563, loss: 5.214, reward: 32.486, critic_reward: 33.021, revenue_rate: 0.7958, distance: 9.6297, memory: -0.0416, power: 0.2923, lr: 0.000100, took: 81.756s
[HYBRID] Epoch 2, Batch 980/1563, loss: 3.825, reward: 31.972, critic_reward: 31.870, revenue_rate: 0.7843, distance: 9.5048, memory: -0.0342, power: 0.2890, lr: 0.000100, took: 79.961s
[HYBRID] Epoch 2, Batch 990/1563, loss: 2.918, reward: 29.524, critic_reward: 29.797, revenue_rate: 0.7187, distance: 8.4133, memory: -0.0585, power: 0.2575, lr: 0.000100, took: 71.091s
[HYBRID] Epoch 2, Batch 1000/1563, loss: 1.986, reward: 28.319, critic_reward: 27.953, revenue_rate: 0.6884, distance: 8.0715, memory: -0.0752, power: 0.2464, lr: 0.000100, took: 67.593s
[HYBRID] Epoch 2, Batch 1010/1563, loss: 2.712, reward: 29.386, critic_reward: 29.520, revenue_rate: 0.7145, distance: 8.5043, memory: -0.0621, power: 0.2580, lr: 0.000100, took: 71.003s
[HYBRID] Epoch 2, Batch 1020/1563, loss: 2.509, reward: 31.033, critic_reward: 31.055, revenue_rate: 0.7602, distance: 9.0829, memory: -0.0448, power: 0.2769, lr: 0.000100, took: 76.827s
[HYBRID] Epoch 2, Batch 1030/1563, loss: 3.352, reward: 30.877, critic_reward: 30.536, revenue_rate: 0.7556, distance: 9.0197, memory: -0.0417, power: 0.2759, lr: 0.000100, took: 75.980s
[HYBRID] Epoch 2, Batch 1040/1563, loss: 4.473, reward: 32.778, critic_reward: 32.767, revenue_rate: 0.8029, distance: 9.8391, memory: -0.0343, power: 0.2989, lr: 0.000100, took: 83.605s
[HYBRID] Epoch 2, Batch 1050/1563, loss: 3.591, reward: 33.587, critic_reward: 33.412, revenue_rate: 0.8240, distance: 10.1915, memory: -0.0218, power: 0.3090, lr: 0.000100, took: 87.169s
[HYBRID] Epoch 2, Batch 1060/1563, loss: 2.974, reward: 33.201, critic_reward: 33.499, revenue_rate: 0.8203, distance: 10.2814, memory: -0.0039, power: 0.3118, lr: 0.000100, took: 87.164s
[HYBRID] Epoch 2, Batch 1070/1563, loss: 7.543, reward: 33.147, critic_reward: 31.844, revenue_rate: 0.8136, distance: 10.0231, memory: -0.0204, power: 0.3037, lr: 0.000100, took: 87.157s
[HYBRID] Epoch 2, Batch 1080/1563, loss: 3.046, reward: 33.343, critic_reward: 33.393, revenue_rate: 0.8195, distance: 10.1367, memory: -0.0163, power: 0.3085, lr: 0.000100, took: 87.092s
[HYBRID] Epoch 2, Batch 1090/1563, loss: 6.635, reward: 34.204, critic_reward: 35.810, revenue_rate: 0.8419, distance: 10.4750, memory: -0.0097, power: 0.3186, lr: 0.000100, took: 90.273s
[HYBRID] Epoch 2, Batch 1100/1563, loss: 4.870, reward: 32.418, critic_reward: 31.223, revenue_rate: 0.7978, distance: 9.8332, memory: -0.0219, power: 0.2985, lr: 0.000100, took: 83.581s
[HYBRID] Epoch 2, Batch 1110/1563, loss: 3.849, reward: 32.517, critic_reward: 33.526, revenue_rate: 0.8004, distance: 9.8269, memory: -0.0258, power: 0.2986, lr: 0.000100, took: 83.526s
[HYBRID] Epoch 2, Batch 1120/1563, loss: 5.466, reward: 31.881, critic_reward: 32.189, revenue_rate: 0.7823, distance: 9.4228, memory: -0.0457, power: 0.2841, lr: 0.000100, took: 79.784s
[HYBRID] Epoch 2, Batch 1130/1563, loss: 3.777, reward: 31.543, critic_reward: 31.320, revenue_rate: 0.7731, distance: 9.3330, memory: -0.0473, power: 0.2846, lr: 0.000100, took: 79.174s
[HYBRID] Epoch 2, Batch 1140/1563, loss: 3.588, reward: 32.330, critic_reward: 32.806, revenue_rate: 0.7936, distance: 9.6379, memory: -0.0297, power: 0.2955, lr: 0.000100, took: 82.201s
[HYBRID] Epoch 2, Batch 1150/1563, loss: 4.706, reward: 33.900, critic_reward: 33.871, revenue_rate: 0.8372, distance: 10.5977, memory: -0.0020, power: 0.3186, lr: 0.000100, took: 90.263s
[HYBRID] Epoch 2, Batch 1160/1563, loss: 6.172, reward: 33.487, critic_reward: 35.021, revenue_rate: 0.8231, distance: 10.0798, memory: -0.0180, power: 0.3090, lr: 0.000100, took: 86.897s
[HYBRID] Epoch 2, Batch 1170/1563, loss: 3.480, reward: 32.694, critic_reward: 32.351, revenue_rate: 0.8026, distance: 9.8144, memory: -0.0236, power: 0.2990, lr: 0.000100, took: 83.510s
[HYBRID] Epoch 2, Batch 1180/1563, loss: 2.515, reward: 31.899, critic_reward: 32.205, revenue_rate: 0.7811, distance: 9.5076, memory: -0.0312, power: 0.2870, lr: 0.000100, took: 82.581s
[HYBRID] Epoch 2, Batch 1190/1563, loss: 3.926, reward: 31.201, critic_reward: 30.794, revenue_rate: 0.7660, distance: 9.2253, memory: -0.0394, power: 0.2827, lr: 0.000100, took: 77.955s
[HYBRID] Epoch 2, Batch 1200/1563, loss: 3.097, reward: 31.331, critic_reward: 31.553, revenue_rate: 0.7648, distance: 9.1204, memory: -0.0499, power: 0.2790, lr: 0.000100, took: 77.445s
[HYBRID] Epoch 2, Batch 1210/1563, loss: 4.500, reward: 31.347, critic_reward: 32.201, revenue_rate: 0.7703, distance: 9.3011, memory: -0.0433, power: 0.2835, lr: 0.000100, took: 79.421s
[HYBRID] Epoch 2, Batch 1220/1563, loss: 7.034, reward: 32.441, critic_reward: 31.786, revenue_rate: 0.7955, distance: 9.6586, memory: -0.0355, power: 0.2937, lr: 0.000100, took: 82.305s
[HYBRID] Epoch 2, Batch 1230/1563, loss: 5.117, reward: 33.164, critic_reward: 32.576, revenue_rate: 0.8174, distance: 10.0883, memory: -0.0158, power: 0.3031, lr: 0.000100, took: 85.984s
[HYBRID] Epoch 2, Batch 1240/1563, loss: 6.287, reward: 33.043, critic_reward: 33.839, revenue_rate: 0.8127, distance: 9.9715, memory: -0.0222, power: 0.3030, lr: 0.000100, took: 84.791s
[HYBRID] Epoch 2, Batch 1250/1563, loss: 4.761, reward: 32.924, critic_reward: 31.773, revenue_rate: 0.8088, distance: 9.9801, memory: -0.0177, power: 0.3005, lr: 0.000100, took: 86.207s
[HYBRID] Epoch 2, Batch 1260/1563, loss: 3.805, reward: 32.984, critic_reward: 33.561, revenue_rate: 0.8080, distance: 9.9476, memory: -0.0207, power: 0.3003, lr: 0.000100, took: 84.732s
[HYBRID] Epoch 2, Batch 1270/1563, loss: 6.386, reward: 33.090, critic_reward: 34.347, revenue_rate: 0.8122, distance: 9.9362, memory: -0.0274, power: 0.3021, lr: 0.000100, took: 84.774s
[HYBRID] Epoch 2, Batch 1280/1563, loss: 3.295, reward: 32.670, critic_reward: 32.096, revenue_rate: 0.8016, distance: 9.7795, memory: -0.0238, power: 0.2982, lr: 0.000100, took: 83.771s
[HYBRID] Epoch 2, Batch 1290/1563, loss: 2.377, reward: 32.159, critic_reward: 32.214, revenue_rate: 0.7874, distance: 9.5105, memory: -0.0403, power: 0.2893, lr: 0.000100, took: 82.573s
[HYBRID] Epoch 2, Batch 1300/1563, loss: 2.846, reward: 31.201, critic_reward: 30.888, revenue_rate: 0.7668, distance: 9.2338, memory: -0.0420, power: 0.2809, lr: 0.000100, took: 77.731s
[HYBRID] Epoch 2, Batch 1310/1563, loss: 2.508, reward: 31.718, critic_reward: 31.348, revenue_rate: 0.7765, distance: 9.3686, memory: -0.0420, power: 0.2853, lr: 0.000100, took: 79.213s
[HYBRID] Epoch 2, Batch 1320/1563, loss: 5.414, reward: 32.816, critic_reward: 32.865, revenue_rate: 0.8047, distance: 9.8376, memory: -0.0288, power: 0.2981, lr: 0.000100, took: 84.054s
[HYBRID] Epoch 2, Batch 1330/1563, loss: 3.891, reward: 32.469, critic_reward: 32.092, revenue_rate: 0.7979, distance: 9.7684, memory: -0.0275, power: 0.2964, lr: 0.000100, took: 82.966s
[HYBRID] Epoch 2, Batch 1340/1563, loss: 5.936, reward: 32.056, critic_reward: 30.795, revenue_rate: 0.7858, distance: 9.6350, memory: -0.0346, power: 0.2936, lr: 0.000100, took: 82.083s
[HYBRID] Epoch 2, Batch 1350/1563, loss: 4.109, reward: 34.048, critic_reward: 34.445, revenue_rate: 0.8423, distance: 10.6216, memory: -0.0008, power: 0.3224, lr: 0.000100, took: 90.873s
[HYBRID] Epoch 2, Batch 1360/1563, loss: 4.773, reward: 35.020, critic_reward: 35.996, revenue_rate: 0.8607, distance: 10.8786, memory: 0.0135, power: 0.3300, lr: 0.000100, took: 93.508s
[HYBRID] Epoch 2, Batch 1370/1563, loss: 3.962, reward: 34.840, critic_reward: 34.128, revenue_rate: 0.8606, distance: 10.9302, memory: -0.0017, power: 0.3301, lr: 0.000100, took: 93.868s
[HYBRID] Epoch 2, Batch 1380/1563, loss: 3.012, reward: 33.636, critic_reward: 34.304, revenue_rate: 0.8233, distance: 10.1632, memory: -0.0181, power: 0.3099, lr: 0.000100, took: 86.860s
[HYBRID] Epoch 2, Batch 1390/1563, loss: 2.860, reward: 31.113, critic_reward: 31.031, revenue_rate: 0.7589, distance: 9.1278, memory: -0.0511, power: 0.2755, lr: 0.000100, took: 76.512s
[HYBRID] Epoch 2, Batch 1400/1563, loss: 3.816, reward: 29.443, critic_reward: 29.389, revenue_rate: 0.7147, distance: 8.4278, memory: -0.0733, power: 0.2564, lr: 0.000100, took: 72.407s
[HYBRID] Epoch 2, Batch 1410/1563, loss: 5.962, reward: 29.752, critic_reward: 29.048, revenue_rate: 0.7237, distance: 8.4434, memory: -0.0696, power: 0.2591, lr: 0.000100, took: 71.761s
[HYBRID] Epoch 2, Batch 1420/1563, loss: 4.283, reward: 31.439, critic_reward: 31.895, revenue_rate: 0.7703, distance: 9.2712, memory: -0.0503, power: 0.2795, lr: 0.000100, took: 77.443s
[HYBRID] Epoch 2, Batch 1430/1563, loss: 2.518, reward: 33.008, critic_reward: 32.826, revenue_rate: 0.8119, distance: 9.9109, memory: -0.0247, power: 0.3006, lr: 0.000100, took: 84.164s
[HYBRID] Epoch 2, Batch 1440/1563, loss: 4.502, reward: 33.484, critic_reward: 32.654, revenue_rate: 0.8212, distance: 10.1223, memory: -0.0101, power: 0.3089, lr: 0.000100, took: 86.969s
[HYBRID] Epoch 2, Batch 1450/1563, loss: 5.376, reward: 33.040, critic_reward: 32.805, revenue_rate: 0.8097, distance: 9.9502, memory: -0.0196, power: 0.3031, lr: 0.000100, took: 84.383s
[HYBRID] Epoch 2, Batch 1460/1563, loss: 4.501, reward: 33.238, critic_reward: 33.649, revenue_rate: 0.8166, distance: 9.9851, memory: -0.0183, power: 0.3020, lr: 0.000100, took: 84.790s
[HYBRID] Epoch 2, Batch 1470/1563, loss: 2.802, reward: 33.601, critic_reward: 32.915, revenue_rate: 0.8273, distance: 10.1816, memory: -0.0112, power: 0.3085, lr: 0.000100, took: 86.601s
[HYBRID] Epoch 2, Batch 1480/1563, loss: 2.227, reward: 33.613, critic_reward: 33.466, revenue_rate: 0.8242, distance: 10.1847, memory: -0.0200, power: 0.3084, lr: 0.000100, took: 87.108s
[HYBRID] Epoch 2, Batch 1490/1563, loss: 3.880, reward: 33.212, critic_reward: 34.072, revenue_rate: 0.8154, distance: 9.9252, memory: -0.0259, power: 0.3022, lr: 0.000100, took: 84.968s
[HYBRID] Epoch 2, Batch 1500/1563, loss: 5.003, reward: 33.678, critic_reward: 32.756, revenue_rate: 0.8255, distance: 10.2159, memory: -0.0193, power: 0.3082, lr: 0.000100, took: 88.878s
[HYBRID] Epoch 2, Batch 1510/1563, loss: 3.647, reward: 32.809, critic_reward: 33.662, revenue_rate: 0.8051, distance: 9.8583, memory: -0.0189, power: 0.2987, lr: 0.000100, took: 83.668s
[HYBRID] Epoch 2, Batch 1520/1563, loss: 2.533, reward: 31.842, critic_reward: 31.504, revenue_rate: 0.7805, distance: 9.3730, memory: -0.0351, power: 0.2863, lr: 0.000100, took: 79.576s
[HYBRID] Epoch 2, Batch 1530/1563, loss: 3.058, reward: 32.351, critic_reward: 32.784, revenue_rate: 0.7902, distance: 9.5513, memory: -0.0343, power: 0.2902, lr: 0.000100, took: 81.347s
[HYBRID] Epoch 2, Batch 1540/1563, loss: 3.095, reward: 31.589, critic_reward: 31.395, revenue_rate: 0.7721, distance: 9.3123, memory: -0.0381, power: 0.2829, lr: 0.000100, took: 78.213s
[HYBRID] Epoch 2, Batch 1550/1563, loss: 4.573, reward: 33.466, critic_reward: 32.516, revenue_rate: 0.8205, distance: 10.0593, memory: -0.0259, power: 0.3058, lr: 0.000100, took: 85.855s
[HYBRID] Epoch 2, Batch 1560/1563, loss: 2.963, reward: 34.559, critic_reward: 34.731, revenue_rate: 0.8468, distance: 10.4869, memory: -0.0097, power: 0.3197, lr: 0.000100, took: 90.749s
[HYBRID] 开始验证...
[HYBRID] 验证完成 - Epoch 2, reward: 34.400, revenue_rate: 0.8467, distance: 10.4887, memory: -0.0082, power: 0.3180
[HYBRID] 已保存新模型到 constellation_smp\constellation_smp100\multi_constellation_comparison_2025_08_25_17_02_29\constellation_gpnindrnn_hybrid_2025_08_26_17_20_30 (验证集奖励: 34.4002)
[HYBRID] 开始训练 Epoch 3/3
[HYBRID] Epoch 3, Batch 10/1563, loss: 3.370, reward: 33.474, critic_reward: 33.637, revenue_rate: 0.8233, distance: 10.0956, memory: -0.0167, power: 0.3085, lr: 0.000100, took: 90.943s
[HYBRID] Epoch 3, Batch 20/1563, loss: 2.535, reward: 33.306, critic_reward: 33.175, revenue_rate: 0.8169, distance: 9.8939, memory: -0.0274, power: 0.3019, lr: 0.000100, took: 85.635s
[HYBRID] Epoch 3, Batch 30/1563, loss: 3.018, reward: 33.684, critic_reward: 33.699, revenue_rate: 0.8288, distance: 10.2169, memory: -0.0151, power: 0.3098, lr: 0.000100, took: 87.328s
[HYBRID] Epoch 3, Batch 40/1563, loss: 5.356, reward: 33.340, critic_reward: 32.643, revenue_rate: 0.8222, distance: 10.0879, memory: -0.0165, power: 0.3055, lr: 0.000100, took: 85.960s
[HYBRID] Epoch 3, Batch 50/1563, loss: 2.524, reward: 31.472, critic_reward: 31.871, revenue_rate: 0.7700, distance: 9.2557, memory: -0.0429, power: 0.2806, lr: 0.000100, took: 77.534s
[HYBRID] Epoch 3, Batch 60/1563, loss: 4.032, reward: 31.791, critic_reward: 30.680, revenue_rate: 0.7784, distance: 9.4043, memory: -0.0438, power: 0.2842, lr: 0.000100, took: 79.220s
[HYBRID] Epoch 3, Batch 70/1563, loss: 3.334, reward: 33.791, critic_reward: 34.612, revenue_rate: 0.8293, distance: 10.2146, memory: -0.0074, power: 0.3112, lr: 0.000100, took: 87.426s
[HYBRID] Epoch 3, Batch 80/1563, loss: 2.327, reward: 33.253, critic_reward: 33.504, revenue_rate: 0.8158, distance: 9.9359, memory: -0.0215, power: 0.3032, lr: 0.000100, took: 87.514s
[HYBRID] Epoch 3, Batch 90/1563, loss: 3.200, reward: 33.460, critic_reward: 33.711, revenue_rate: 0.8182, distance: 10.0598, memory: -0.0253, power: 0.3050, lr: 0.000100, took: 85.380s
[HYBRID] Epoch 3, Batch 100/1563, loss: 3.341, reward: 32.276, critic_reward: 32.263, revenue_rate: 0.7902, distance: 9.6024, memory: -0.0177, power: 0.2903, lr: 0.000100, took: 82.130s
[HYBRID] Epoch 3, Batch 110/1563, loss: 1.843, reward: 31.765, critic_reward: 31.914, revenue_rate: 0.7797, distance: 9.3607, memory: -0.0290, power: 0.2853, lr: 0.000100, took: 79.562s
[HYBRID] Epoch 3, Batch 120/1563, loss: 2.506, reward: 31.532, critic_reward: 31.572, revenue_rate: 0.7688, distance: 9.1668, memory: -0.0453, power: 0.2794, lr: 0.000100, took: 77.173s
[HYBRID] Epoch 3, Batch 130/1563, loss: 2.292, reward: 31.497, critic_reward: 31.314, revenue_rate: 0.7724, distance: 9.2191, memory: -0.0362, power: 0.2818, lr: 0.000100, took: 77.807s
[HYBRID] Epoch 3, Batch 140/1563, loss: 3.912, reward: 31.827, critic_reward: 32.404, revenue_rate: 0.7778, distance: 9.2946, memory: -0.0429, power: 0.2865, lr: 0.000100, took: 79.178s
[HYBRID] Epoch 3, Batch 150/1563, loss: 2.161, reward: 30.254, critic_reward: 30.324, revenue_rate: 0.7416, distance: 8.8120, memory: -0.0586, power: 0.2656, lr: 0.000100, took: 74.143s
[HYBRID] Epoch 3, Batch 160/1563, loss: 4.075, reward: 30.943, critic_reward: 29.946, revenue_rate: 0.7568, distance: 9.0171, memory: -0.0393, power: 0.2750, lr: 0.000100, took: 75.802s
[HYBRID] Epoch 3, Batch 170/1563, loss: 4.230, reward: 33.349, critic_reward: 32.839, revenue_rate: 0.8190, distance: 10.0180, memory: -0.0209, power: 0.3035, lr: 0.000100, took: 85.620s
[HYBRID] Epoch 3, Batch 180/1563, loss: 3.605, reward: 33.487, critic_reward: 33.801, revenue_rate: 0.8216, distance: 10.1669, memory: -0.0191, power: 0.3091, lr: 0.000100, took: 86.751s
[HYBRID] Epoch 3, Batch 190/1563, loss: 3.185, reward: 33.094, critic_reward: 33.195, revenue_rate: 0.8148, distance: 10.1156, memory: -0.0140, power: 0.3068, lr: 0.000100, took: 88.562s
[HYBRID] Epoch 3, Batch 200/1563, loss: 3.061, reward: 32.010, critic_reward: 31.734, revenue_rate: 0.7836, distance: 9.5574, memory: -0.0380, power: 0.2922, lr: 0.000100, took: 81.565s
[HYBRID] Epoch 3, Batch 210/1563, loss: 3.409, reward: 29.972, critic_reward: 30.262, revenue_rate: 0.7322, distance: 8.8351, memory: -0.0580, power: 0.2689, lr: 0.000100, took: 74.556s
[HYBRID] Epoch 3, Batch 220/1563, loss: 2.786, reward: 30.631, critic_reward: 30.661, revenue_rate: 0.7477, distance: 9.0719, memory: -0.0498, power: 0.2764, lr: 0.000100, took: 76.385s
[HYBRID] Epoch 3, Batch 230/1563, loss: 3.979, reward: 32.128, critic_reward: 31.596, revenue_rate: 0.7885, distance: 9.5537, memory: -0.0350, power: 0.2907, lr: 0.000100, took: 81.722s
[HYBRID] Epoch 3, Batch 240/1563, loss: 2.752, reward: 34.404, critic_reward: 34.058, revenue_rate: 0.8477, distance: 10.4585, memory: -0.0131, power: 0.3202, lr: 0.000100, took: 90.287s
[HYBRID] Epoch 3, Batch 250/1563, loss: 2.499, reward: 34.612, critic_reward: 34.624, revenue_rate: 0.8531, distance: 10.6936, memory: 0.0037, power: 0.3237, lr: 0.000100, took: 90.972s
[HYBRID] Epoch 3, Batch 260/1563, loss: 4.640, reward: 33.560, critic_reward: 32.237, revenue_rate: 0.8205, distance: 10.1304, memory: -0.0150, power: 0.3091, lr: 0.000100, took: 86.600s
[HYBRID] Epoch 3, Batch 270/1563, loss: 4.354, reward: 33.887, critic_reward: 34.686, revenue_rate: 0.8318, distance: 10.2920, memory: -0.0064, power: 0.3136, lr: 0.000100, took: 88.925s
[HYBRID] Epoch 3, Batch 280/1563, loss: 5.153, reward: 34.318, critic_reward: 33.529, revenue_rate: 0.8427, distance: 10.6114, memory: 0.0016, power: 0.3225, lr: 0.000100, took: 90.802s
[HYBRID] Epoch 3, Batch 290/1563, loss: 3.028, reward: 34.133, critic_reward: 33.794, revenue_rate: 0.8428, distance: 10.7044, memory: -0.0070, power: 0.3241, lr: 0.000100, took: 91.757s
[HYBRID] Epoch 3, Batch 300/1563, loss: 2.762, reward: 33.793, critic_reward: 33.939, revenue_rate: 0.8345, distance: 10.5621, memory: 0.0043, power: 0.3223, lr: 0.000100, took: 93.997s
[HYBRID] Epoch 3, Batch 310/1563, loss: 4.386, reward: 33.224, critic_reward: 32.407, revenue_rate: 0.8174, distance: 10.3143, memory: -0.0096, power: 0.3128, lr: 0.000100, took: 87.809s
[HYBRID] Epoch 3, Batch 320/1563, loss: 2.400, reward: 33.437, critic_reward: 33.440, revenue_rate: 0.8231, distance: 10.1820, memory: -0.0134, power: 0.3092, lr: 0.000100, took: 87.512s
[HYBRID] Epoch 3, Batch 330/1563, loss: 2.532, reward: 33.177, critic_reward: 32.928, revenue_rate: 0.8164, distance: 10.1015, memory: -0.0186, power: 0.3056, lr: 0.000100, took: 85.953s
[HYBRID] Epoch 3, Batch 340/1563, loss: 3.047, reward: 34.209, critic_reward: 33.875, revenue_rate: 0.8436, distance: 10.6449, memory: 0.0010, power: 0.3230, lr: 0.000100, took: 91.056s
[HYBRID] Epoch 3, Batch 350/1563, loss: 3.379, reward: 35.563, critic_reward: 35.892, revenue_rate: 0.8799, distance: 11.4339, memory: 0.0226, power: 0.3464, lr: 0.000100, took: 99.184s
[HYBRID] Epoch 3, Batch 360/1563, loss: 2.805, reward: 35.028, critic_reward: 35.135, revenue_rate: 0.8658, distance: 11.0026, memory: 0.0192, power: 0.3318, lr: 0.000100, took: 95.536s
[HYBRID] Epoch 3, Batch 370/1563, loss: 3.965, reward: 33.656, critic_reward: 33.192, revenue_rate: 0.8269, distance: 10.2135, memory: -0.0190, power: 0.3097, lr: 0.000100, took: 87.518s
[HYBRID] Epoch 3, Batch 380/1563, loss: 2.628, reward: 32.684, critic_reward: 32.736, revenue_rate: 0.7997, distance: 9.7132, memory: -0.0290, power: 0.2966, lr: 0.000100, took: 82.767s
[HYBRID] Epoch 3, Batch 390/1563, loss: 3.269, reward: 32.124, critic_reward: 32.140, revenue_rate: 0.7857, distance: 9.5997, memory: -0.0266, power: 0.2897, lr: 0.000100, took: 80.784s
[HYBRID] Epoch 3, Batch 400/1563, loss: 7.809, reward: 33.466, critic_reward: 33.016, revenue_rate: 0.8213, distance: 10.1968, memory: -0.0265, power: 0.3079, lr: 0.000100, took: 89.197s
[HYBRID] Epoch 3, Batch 410/1563, loss: 9.415, reward: 33.256, critic_reward: 34.998, revenue_rate: 0.8167, distance: 10.0625, memory: -0.0113, power: 0.3072, lr: 0.000100, took: 86.140s
[HYBRID] Epoch 3, Batch 420/1563, loss: 2.747, reward: 33.356, critic_reward: 33.991, revenue_rate: 0.8197, distance: 10.0016, memory: -0.0115, power: 0.3057, lr: 0.000100, took: 85.766s
[HYBRID] Epoch 3, Batch 430/1563, loss: 4.089, reward: 32.226, critic_reward: 31.398, revenue_rate: 0.7900, distance: 9.5334, memory: -0.0349, power: 0.2906, lr: 0.000100, took: 81.338s
[HYBRID] Epoch 3, Batch 440/1563, loss: 4.214, reward: 33.443, critic_reward: 33.924, revenue_rate: 0.8194, distance: 10.0622, memory: -0.0257, power: 0.3044, lr: 0.000100, took: 85.662s
[HYBRID] Epoch 3, Batch 450/1563, loss: 3.430, reward: 32.988, critic_reward: 33.663, revenue_rate: 0.8105, distance: 9.8989, memory: -0.0220, power: 0.2999, lr: 0.000100, took: 84.284s
[HYBRID] Epoch 3, Batch 460/1563, loss: 3.722, reward: 32.370, critic_reward: 31.913, revenue_rate: 0.7904, distance: 9.6583, memory: -0.0356, power: 0.2908, lr: 0.000100, took: 80.964s
[HYBRID] Epoch 3, Batch 470/1563, loss: 3.708, reward: 32.776, critic_reward: 33.454, revenue_rate: 0.8030, distance: 9.8159, memory: -0.0319, power: 0.2983, lr: 0.000100, took: 83.197s
[HYBRID] Epoch 3, Batch 480/1563, loss: 3.972, reward: 31.611, critic_reward: 32.151, revenue_rate: 0.7748, distance: 9.3594, memory: -0.0393, power: 0.2861, lr: 0.000100, took: 79.338s
[HYBRID] Epoch 3, Batch 490/1563, loss: 3.248, reward: 31.582, critic_reward: 31.359, revenue_rate: 0.7723, distance: 9.3192, memory: -0.0447, power: 0.2831, lr: 0.000100, took: 78.375s
[HYBRID] Epoch 3, Batch 500/1563, loss: 3.596, reward: 31.243, critic_reward: 32.195, revenue_rate: 0.7613, distance: 9.1910, memory: -0.0423, power: 0.2783, lr: 0.000100, took: 77.218s
[HYBRID] Epoch 3, Batch 510/1563, loss: 4.996, reward: 30.569, critic_reward: 29.356, revenue_rate: 0.7445, distance: 8.8439, memory: -0.0567, power: 0.2695, lr: 0.000100, took: 76.980s
[HYBRID] Epoch 3, Batch 520/1563, loss: 6.239, reward: 30.782, critic_reward: 31.686, revenue_rate: 0.7517, distance: 8.9113, memory: -0.0561, power: 0.2716, lr: 0.000100, took: 75.328s
[HYBRID] Epoch 3, Batch 530/1563, loss: 2.244, reward: 30.598, critic_reward: 30.714, revenue_rate: 0.7477, distance: 8.8231, memory: -0.0548, power: 0.2689, lr: 0.000100, took: 73.995s
[HYBRID] Epoch 3, Batch 540/1563, loss: 3.135, reward: 33.011, critic_reward: 32.703, revenue_rate: 0.8078, distance: 9.8014, memory: -0.0226, power: 0.2981, lr: 0.000100, took: 83.404s
[HYBRID] Epoch 3, Batch 550/1563, loss: 4.767, reward: 33.530, critic_reward: 34.222, revenue_rate: 0.8224, distance: 10.0451, memory: -0.0257, power: 0.3071, lr: 0.000100, took: 85.550s
[HYBRID] Epoch 3, Batch 560/1563, loss: 5.673, reward: 31.591, critic_reward: 32.159, revenue_rate: 0.7709, distance: 9.2518, memory: -0.0476, power: 0.2811, lr: 0.000100, took: 78.557s
[HYBRID] Epoch 3, Batch 570/1563, loss: 3.420, reward: 30.990, critic_reward: 30.076, revenue_rate: 0.7587, distance: 9.0447, memory: -0.0432, power: 0.2762, lr: 0.000100, took: 77.002s
[HYBRID] Epoch 3, Batch 580/1563, loss: 2.632, reward: 31.139, critic_reward: 31.575, revenue_rate: 0.7610, distance: 9.1118, memory: -0.0431, power: 0.2777, lr: 0.000100, took: 76.589s
[HYBRID] Epoch 3, Batch 590/1563, loss: 2.557, reward: 31.921, critic_reward: 31.962, revenue_rate: 0.7794, distance: 9.4271, memory: -0.0370, power: 0.2847, lr: 0.000100, took: 79.742s
[HYBRID] Epoch 3, Batch 600/1563, loss: 3.134, reward: 32.375, critic_reward: 32.432, revenue_rate: 0.7904, distance: 9.5623, memory: -0.0290, power: 0.2915, lr: 0.000100, took: 81.007s
[HYBRID] Epoch 3, Batch 610/1563, loss: 3.569, reward: 32.851, critic_reward: 33.402, revenue_rate: 0.8059, distance: 9.7618, memory: -0.0238, power: 0.2983, lr: 0.000100, took: 83.574s
[HYBRID] Epoch 3, Batch 620/1563, loss: 3.664, reward: 33.180, critic_reward: 33.389, revenue_rate: 0.8149, distance: 9.9404, memory: -0.0205, power: 0.3011, lr: 0.000100, took: 86.568s
[HYBRID] Epoch 3, Batch 630/1563, loss: 3.145, reward: 33.940, critic_reward: 33.842, revenue_rate: 0.8356, distance: 10.2851, memory: -0.0149, power: 0.3139, lr: 0.000100, took: 87.811s
[HYBRID] Epoch 3, Batch 640/1563, loss: 2.873, reward: 32.626, critic_reward: 33.105, revenue_rate: 0.8008, distance: 9.6162, memory: -0.0294, power: 0.2959, lr: 0.000100, took: 82.328s
[HYBRID] Epoch 3, Batch 650/1563, loss: 2.687, reward: 31.172, critic_reward: 31.037, revenue_rate: 0.7628, distance: 9.0678, memory: -0.0499, power: 0.2778, lr: 0.000100, took: 76.762s
[HYBRID] Epoch 3, Batch 660/1563, loss: 3.078, reward: 30.389, critic_reward: 29.772, revenue_rate: 0.7389, distance: 8.7081, memory: -0.0587, power: 0.2677, lr: 0.000100, took: 73.776s
[HYBRID] Epoch 3, Batch 670/1563, loss: 2.359, reward: 29.874, critic_reward: 30.118, revenue_rate: 0.7287, distance: 8.5607, memory: -0.0695, power: 0.2606, lr: 0.000100, took: 71.583s
[HYBRID] Epoch 3, Batch 680/1563, loss: 3.735, reward: 30.109, critic_reward: 29.867, revenue_rate: 0.7350, distance: 8.6880, memory: -0.0614, power: 0.2658, lr: 0.000100, took: 73.176s
[HYBRID] Epoch 3, Batch 690/1563, loss: 2.336, reward: 30.844, critic_reward: 30.921, revenue_rate: 0.7529, distance: 8.9957, memory: -0.0483, power: 0.2744, lr: 0.000100, took: 76.134s
[HYBRID] Epoch 3, Batch 700/1563, loss: 2.545, reward: 31.256, critic_reward: 31.662, revenue_rate: 0.7640, distance: 9.1374, memory: -0.0508, power: 0.2777, lr: 0.000100, took: 77.013s
[HYBRID] Epoch 3, Batch 710/1563, loss: 5.924, reward: 32.717, critic_reward: 33.540, revenue_rate: 0.8008, distance: 9.7055, memory: -0.0373, power: 0.2960, lr: 0.000100, took: 82.586s
[HYBRID] Epoch 3, Batch 720/1563, loss: 4.434, reward: 32.123, critic_reward: 30.811, revenue_rate: 0.7886, distance: 9.5891, memory: -0.0346, power: 0.2879, lr: 0.000100, took: 80.401s
[HYBRID] Epoch 3, Batch 730/1563, loss: 2.016, reward: 32.356, critic_reward: 32.585, revenue_rate: 0.7962, distance: 9.6649, memory: -0.0293, power: 0.2942, lr: 0.000100, took: 81.572s
[HYBRID] Epoch 3, Batch 740/1563, loss: 4.246, reward: 31.889, critic_reward: 32.193, revenue_rate: 0.7784, distance: 9.4117, memory: -0.0397, power: 0.2876, lr: 0.000100, took: 82.155s
[HYBRID] Epoch 3, Batch 750/1563, loss: 1.984, reward: 31.279, critic_reward: 31.241, revenue_rate: 0.7643, distance: 9.2122, memory: -0.0435, power: 0.2805, lr: 0.000100, took: 77.810s
[HYBRID] Epoch 3, Batch 760/1563, loss: 2.787, reward: 30.671, critic_reward: 30.969, revenue_rate: 0.7512, distance: 8.9894, memory: -0.0533, power: 0.2732, lr: 0.000100, took: 75.907s
[HYBRID] Epoch 3, Batch 770/1563, loss: 2.723, reward: 29.194, critic_reward: 28.884, revenue_rate: 0.7144, distance: 8.5100, memory: -0.0735, power: 0.2591, lr: 0.000100, took: 71.381s
[HYBRID] Epoch 3, Batch 780/1563, loss: 2.999, reward: 28.234, critic_reward: 28.281, revenue_rate: 0.6850, distance: 7.9833, memory: -0.0734, power: 0.2456, lr: 0.000100, took: 67.192s
[HYBRID] Epoch 3, Batch 790/1563, loss: 2.874, reward: 30.487, critic_reward: 30.032, revenue_rate: 0.7460, distance: 8.9827, memory: -0.0555, power: 0.2714, lr: 0.000100, took: 75.201s
[HYBRID] Epoch 3, Batch 800/1563, loss: 2.477, reward: 31.400, critic_reward: 31.673, revenue_rate: 0.7665, distance: 9.2593, memory: -0.0445, power: 0.2815, lr: 0.000100, took: 78.770s
[HYBRID] Epoch 3, Batch 810/1563, loss: 2.564, reward: 32.302, critic_reward: 32.323, revenue_rate: 0.7921, distance: 9.6705, memory: -0.0337, power: 0.2925, lr: 0.000100, took: 81.806s
[HYBRID] Epoch 3, Batch 820/1563, loss: 3.729, reward: 32.873, critic_reward: 32.586, revenue_rate: 0.8072, distance: 9.8131, memory: -0.0310, power: 0.2982, lr: 0.000100, took: 83.562s
[HYBRID] Epoch 3, Batch 830/1563, loss: 4.038, reward: 31.328, critic_reward: 32.373, revenue_rate: 0.7663, distance: 9.1636, memory: -0.0372, power: 0.2807, lr: 0.000100, took: 77.755s
[HYBRID] Epoch 3, Batch 840/1563, loss: 3.077, reward: 30.501, critic_reward: 30.558, revenue_rate: 0.7457, distance: 8.8980, memory: -0.0524, power: 0.2720, lr: 0.000100, took: 75.178s
[HYBRID] Epoch 3, Batch 850/1563, loss: 2.326, reward: 31.013, critic_reward: 30.744, revenue_rate: 0.7582, distance: 9.0617, memory: -0.0531, power: 0.2760, lr: 0.000100, took: 78.757s
[HYBRID] Epoch 3, Batch 860/1563, loss: 3.003, reward: 30.670, critic_reward: 30.549, revenue_rate: 0.7509, distance: 8.8916, memory: -0.0551, power: 0.2726, lr: 0.000100, took: 74.964s
[HYBRID] Epoch 3, Batch 870/1563, loss: 3.124, reward: 30.408, critic_reward: 30.527, revenue_rate: 0.7442, distance: 8.8579, memory: -0.0510, power: 0.2697, lr: 0.000100, took: 74.757s
[HYBRID] Epoch 3, Batch 880/1563, loss: 2.722, reward: 31.163, critic_reward: 30.630, revenue_rate: 0.7636, distance: 9.1129, memory: -0.0433, power: 0.2781, lr: 0.000100, took: 76.824s
[HYBRID] Epoch 3, Batch 890/1563, loss: 4.743, reward: 33.322, critic_reward: 32.661, revenue_rate: 0.8184, distance: 10.0581, memory: -0.0278, power: 0.3054, lr: 0.000100, took: 86.293s
[HYBRID] Epoch 3, Batch 900/1563, loss: 3.138, reward: 34.000, critic_reward: 33.705, revenue_rate: 0.8384, distance: 10.3659, memory: -0.0044, power: 0.3167, lr: 0.000100, took: 89.519s
[HYBRID] Epoch 3, Batch 910/1563, loss: 2.546, reward: 34.325, critic_reward: 34.772, revenue_rate: 0.8458, distance: 10.5976, memory: 0.0034, power: 0.3198, lr: 0.000100, took: 90.563s
[HYBRID] Epoch 3, Batch 920/1563, loss: 2.345, reward: 32.903, critic_reward: 33.012, revenue_rate: 0.8082, distance: 9.7755, memory: -0.0253, power: 0.2992, lr: 0.000100, took: 83.557s
[HYBRID] Epoch 3, Batch 930/1563, loss: 2.480, reward: 32.160, critic_reward: 32.438, revenue_rate: 0.7884, distance: 9.5873, memory: -0.0307, power: 0.2887, lr: 0.000100, took: 80.563s
[HYBRID] Epoch 3, Batch 940/1563, loss: 2.744, reward: 33.284, critic_reward: 33.531, revenue_rate: 0.8204, distance: 10.0185, memory: -0.0259, power: 0.3047, lr: 0.000100, took: 85.988s
[HYBRID] Epoch 3, Batch 950/1563, loss: 4.302, reward: 34.171, critic_reward: 33.172, revenue_rate: 0.8410, distance: 10.4127, memory: -0.0171, power: 0.3155, lr: 0.000100, took: 88.980s
[HYBRID] Epoch 3, Batch 960/1563, loss: 3.046, reward: 34.159, critic_reward: 34.267, revenue_rate: 0.8412, distance: 10.4794, memory: -0.0044, power: 0.3182, lr: 0.000100, took: 91.952s
[HYBRID] Epoch 3, Batch 970/1563, loss: 2.462, reward: 34.056, critic_reward: 34.164, revenue_rate: 0.8367, distance: 10.3635, memory: -0.0118, power: 0.3141, lr: 0.000100, took: 88.826s
[HYBRID] Epoch 3, Batch 980/1563, loss: 3.369, reward: 33.631, critic_reward: 33.878, revenue_rate: 0.8238, distance: 10.1776, memory: -0.0199, power: 0.3066, lr: 0.000100, took: 87.501s
[HYBRID] Epoch 3, Batch 990/1563, loss: 3.700, reward: 31.648, critic_reward: 30.741, revenue_rate: 0.7753, distance: 9.3569, memory: -0.0416, power: 0.2838, lr: 0.000100, took: 79.148s
[HYBRID] Epoch 3, Batch 1000/1563, loss: 2.992, reward: 31.213, critic_reward: 31.523, revenue_rate: 0.7662, distance: 9.2488, memory: -0.0414, power: 0.2799, lr: 0.000100, took: 77.410s
[HYBRID] Epoch 3, Batch 1010/1563, loss: 3.479, reward: 31.968, critic_reward: 31.813, revenue_rate: 0.7811, distance: 9.3528, memory: -0.0422, power: 0.2866, lr: 0.000100, took: 79.409s
[HYBRID] Epoch 3, Batch 1020/1563, loss: 3.955, reward: 32.822, critic_reward: 32.303, revenue_rate: 0.8062, distance: 9.8431, memory: -0.0316, power: 0.2972, lr: 0.000100, took: 83.643s
[HYBRID] Epoch 3, Batch 1030/1563, loss: 5.638, reward: 33.255, critic_reward: 34.207, revenue_rate: 0.8159, distance: 10.0121, memory: -0.0187, power: 0.3032, lr: 0.000100, took: 85.063s
[HYBRID] Epoch 3, Batch 1040/1563, loss: 1.872, reward: 33.289, critic_reward: 33.255, revenue_rate: 0.8156, distance: 9.9485, memory: -0.0207, power: 0.3035, lr: 0.000100, took: 84.602s
[HYBRID] Epoch 3, Batch 1050/1563, loss: 3.124, reward: 34.222, critic_reward: 34.455, revenue_rate: 0.8448, distance: 10.5029, memory: -0.0075, power: 0.3186, lr: 0.000100, took: 89.423s
[HYBRID] Epoch 3, Batch 1060/1563, loss: 4.591, reward: 33.923, critic_reward: 33.093, revenue_rate: 0.8326, distance: 10.2186, memory: -0.0150, power: 0.3126, lr: 0.000100, took: 87.777s
[HYBRID] Epoch 3, Batch 1070/1563, loss: 3.198, reward: 34.690, critic_reward: 34.080, revenue_rate: 0.8557, distance: 10.7363, memory: 0.0033, power: 0.3262, lr: 0.000100, took: 94.930s
[HYBRID] Epoch 3, Batch 1080/1563, loss: 3.759, reward: 35.258, critic_reward: 35.537, revenue_rate: 0.8673, distance: 10.9231, memory: 0.0042, power: 0.3331, lr: 0.000100, took: 94.853s
[HYBRID] Epoch 3, Batch 1090/1563, loss: 6.223, reward: 35.272, critic_reward: 36.272, revenue_rate: 0.8653, distance: 10.7197, memory: -0.0019, power: 0.3273, lr: 0.000100, took: 92.891s
[HYBRID] Epoch 3, Batch 1100/1563, loss: 2.791, reward: 33.435, critic_reward: 32.889, revenue_rate: 0.8177, distance: 9.9025, memory: -0.0231, power: 0.3036, lr: 0.000100, took: 84.582s
[HYBRID] Epoch 3, Batch 1110/1563, loss: 4.616, reward: 32.788, critic_reward: 33.853, revenue_rate: 0.8014, distance: 9.6938, memory: -0.0316, power: 0.2937, lr: 0.000100, took: 82.333s
[HYBRID] Epoch 3, Batch 1120/1563, loss: 3.874, reward: 31.871, critic_reward: 31.664, revenue_rate: 0.7797, distance: 9.4127, memory: -0.0340, power: 0.2851, lr: 0.000100, took: 79.332s
[HYBRID] Epoch 3, Batch 1130/1563, loss: 4.441, reward: 31.659, critic_reward: 31.337, revenue_rate: 0.7736, distance: 9.2339, memory: -0.0489, power: 0.2814, lr: 0.000100, took: 78.345s
[HYBRID] Epoch 3, Batch 1140/1563, loss: 3.066, reward: 30.279, critic_reward: 30.745, revenue_rate: 0.7389, distance: 8.6544, memory: -0.0619, power: 0.2645, lr: 0.000100, took: 72.850s
[HYBRID] Epoch 3, Batch 1150/1563, loss: 3.873, reward: 32.250, critic_reward: 31.765, revenue_rate: 0.7899, distance: 9.4687, memory: -0.0366, power: 0.2893, lr: 0.000100, took: 80.573s
[HYBRID] Epoch 3, Batch 1160/1563, loss: 3.096, reward: 34.327, critic_reward: 34.942, revenue_rate: 0.8437, distance: 10.5404, memory: -0.0026, power: 0.3187, lr: 0.000100, took: 89.622s
[HYBRID] Epoch 3, Batch 1170/1563, loss: 2.691, reward: 35.427, critic_reward: 35.217, revenue_rate: 0.8729, distance: 10.9093, memory: 0.0101, power: 0.3314, lr: 0.000100, took: 97.401s
[HYBRID] Epoch 3, Batch 1180/1563, loss: 10.356, reward: 34.857, critic_reward: 36.416, revenue_rate: 0.8579, distance: 10.6587, memory: -0.0065, power: 0.3242, lr: 0.000100, took: 92.549s
[HYBRID] Epoch 3, Batch 1190/1563, loss: 10.043, reward: 33.311, critic_reward: 33.173, revenue_rate: 0.8172, distance: 10.0401, memory: -0.0191, power: 0.3044, lr: 0.000100, took: 85.733s
[HYBRID] Epoch 3, Batch 1200/1563, loss: 8.614, reward: 33.467, critic_reward: 33.581, revenue_rate: 0.8239, distance: 10.1084, memory: -0.0174, power: 0.3089, lr: 0.000100, took: 86.959s
[HYBRID] Epoch 3, Batch 1210/1563, loss: 10.285, reward: 31.373, critic_reward: 33.664, revenue_rate: 0.7659, distance: 9.2707, memory: -0.0342, power: 0.2817, lr: 0.000100, took: 78.316s
[HYBRID] Epoch 3, Batch 1220/1563, loss: 6.076, reward: 30.083, critic_reward: 28.224, revenue_rate: 0.7362, distance: 8.7464, memory: -0.0589, power: 0.2669, lr: 0.000100, took: 73.586s
[HYBRID] Epoch 3, Batch 1230/1563, loss: 5.662, reward: 30.107, critic_reward: 31.692, revenue_rate: 0.7320, distance: 8.5712, memory: -0.0675, power: 0.2620, lr: 0.000100, took: 72.401s
[HYBRID] Epoch 3, Batch 1240/1563, loss: 6.521, reward: 30.752, critic_reward: 29.147, revenue_rate: 0.7536, distance: 8.9422, memory: -0.0639, power: 0.2731, lr: 0.000100, took: 75.569s
[HYBRID] Epoch 3, Batch 1250/1563, loss: 2.496, reward: 32.437, critic_reward: 32.799, revenue_rate: 0.7934, distance: 9.5799, memory: -0.0373, power: 0.2946, lr: 0.000100, took: 83.635s
[HYBRID] Epoch 3, Batch 1260/1563, loss: 3.225, reward: 32.880, critic_reward: 32.751, revenue_rate: 0.8024, distance: 9.8037, memory: -0.0309, power: 0.2978, lr: 0.000100, took: 90.624s
[HYBRID] Epoch 3, Batch 1270/1563, loss: 3.840, reward: 32.492, critic_reward: 33.080, revenue_rate: 0.7983, distance: 9.6373, memory: -0.0354, power: 0.2933, lr: 0.000100, took: 82.274s
[HYBRID] Epoch 3, Batch 1280/1563, loss: 2.595, reward: 31.056, critic_reward: 30.810, revenue_rate: 0.7563, distance: 8.9435, memory: -0.0505, power: 0.2744, lr: 0.000100, took: 78.167s
[HYBRID] Epoch 3, Batch 1290/1563, loss: 3.894, reward: 30.289, critic_reward: 30.436, revenue_rate: 0.7410, distance: 8.8857, memory: -0.0549, power: 0.2686, lr: 0.000100, took: 74.732s
[HYBRID] Epoch 3, Batch 1300/1563, loss: 3.725, reward: 30.675, critic_reward: 30.926, revenue_rate: 0.7522, distance: 9.0735, memory: -0.0454, power: 0.2761, lr: 0.000100, took: 77.119s
[HYBRID] Epoch 3, Batch 1310/1563, loss: 2.611, reward: 32.501, critic_reward: 32.468, revenue_rate: 0.7967, distance: 9.8763, memory: -0.0280, power: 0.3008, lr: 0.000100, took: 83.320s
[HYBRID] Epoch 3, Batch 1320/1563, loss: 2.021, reward: 33.198, critic_reward: 33.160, revenue_rate: 0.8199, distance: 10.3123, memory: -0.0127, power: 0.3126, lr: 0.000100, took: 88.043s
[HYBRID] Epoch 3, Batch 1330/1563, loss: 3.020, reward: 34.502, critic_reward: 34.628, revenue_rate: 0.8490, distance: 10.6594, memory: -0.0013, power: 0.3259, lr: 0.000100, took: 92.489s
[HYBRID] Epoch 3, Batch 1340/1563, loss: 3.984, reward: 33.961, critic_reward: 33.969, revenue_rate: 0.8376, distance: 10.4647, memory: -0.0026, power: 0.3203, lr: 0.000100, took: 90.221s
[HYBRID] Epoch 3, Batch 1350/1563, loss: 3.188, reward: 34.064, critic_reward: 34.190, revenue_rate: 0.8397, distance: 10.3574, memory: -0.0210, power: 0.3157, lr: 0.000100, took: 89.525s
[HYBRID] Epoch 3, Batch 1360/1563, loss: 2.744, reward: 32.004, critic_reward: 32.102, revenue_rate: 0.7857, distance: 9.6203, memory: -0.0324, power: 0.2895, lr: 0.000100, took: 80.557s
[HYBRID] Epoch 3, Batch 1370/1563, loss: 2.894, reward: 30.786, critic_reward: 30.462, revenue_rate: 0.7506, distance: 8.8663, memory: -0.0492, power: 0.2703, lr: 0.000100, took: 75.341s
[HYBRID] Epoch 3, Batch 1380/1563, loss: 3.529, reward: 31.112, critic_reward: 30.373, revenue_rate: 0.7590, distance: 9.0252, memory: -0.0517, power: 0.2778, lr: 0.000100, took: 75.999s
[HYBRID] Epoch 3, Batch 1390/1563, loss: 3.491, reward: 32.848, critic_reward: 32.664, revenue_rate: 0.8055, distance: 9.7803, memory: -0.0282, power: 0.2979, lr: 0.000100, took: 84.804s
[HYBRID] Epoch 3, Batch 1400/1563, loss: 2.217, reward: 32.348, critic_reward: 32.237, revenue_rate: 0.7891, distance: 9.4198, memory: -0.0324, power: 0.2888, lr: 0.000100, took: 80.581s
[HYBRID] Epoch 3, Batch 1410/1563, loss: 3.431, reward: 32.661, critic_reward: 32.148, revenue_rate: 0.8019, distance: 9.6327, memory: -0.0412, power: 0.2944, lr: 0.000100, took: 82.164s
[HYBRID] Epoch 3, Batch 1420/1563, loss: 2.730, reward: 32.908, critic_reward: 32.481, revenue_rate: 0.8069, distance: 9.8259, memory: -0.0301, power: 0.2964, lr: 0.000100, took: 83.386s
[HYBRID] Epoch 3, Batch 1430/1563, loss: 2.676, reward: 32.339, critic_reward: 32.274, revenue_rate: 0.7916, distance: 9.6105, memory: -0.0351, power: 0.2931, lr: 0.000100, took: 81.564s
[HYBRID] Epoch 3, Batch 1440/1563, loss: 6.320, reward: 32.540, critic_reward: 33.444, revenue_rate: 0.7954, distance: 9.6076, memory: -0.0312, power: 0.2929, lr: 0.000100, took: 81.755s
[HYBRID] Epoch 3, Batch 1450/1563, loss: 5.033, reward: 33.158, critic_reward: 31.719, revenue_rate: 0.8132, distance: 9.7934, memory: -0.0217, power: 0.3007, lr: 0.000100, took: 84.425s
[HYBRID] Epoch 3, Batch 1460/1563, loss: 7.360, reward: 33.317, critic_reward: 35.070, revenue_rate: 0.8212, distance: 10.0887, memory: -0.0213, power: 0.3059, lr: 0.000100, took: 86.330s
[HYBRID] Epoch 3, Batch 1470/1563, loss: 5.208, reward: 32.760, critic_reward: 31.663, revenue_rate: 0.8012, distance: 9.7324, memory: -0.0404, power: 0.2948, lr: 0.000100, took: 82.587s
[HYBRID] Epoch 3, Batch 1480/1563, loss: 5.798, reward: 31.589, critic_reward: 30.611, revenue_rate: 0.7751, distance: 9.2659, memory: -0.0460, power: 0.2843, lr: 0.000100, took: 78.922s
[HYBRID] Epoch 3, Batch 1490/1563, loss: 4.950, reward: 31.384, critic_reward: 32.288, revenue_rate: 0.7692, distance: 9.2219, memory: -0.0534, power: 0.2799, lr: 0.000100, took: 77.825s
[HYBRID] Epoch 3, Batch 1500/1563, loss: 3.257, reward: 32.444, critic_reward: 33.101, revenue_rate: 0.7954, distance: 9.5920, memory: -0.0371, power: 0.2901, lr: 0.000100, took: 83.570s
[HYBRID] Epoch 3, Batch 1510/1563, loss: 2.811, reward: 33.517, critic_reward: 33.123, revenue_rate: 0.8196, distance: 9.9605, memory: -0.0190, power: 0.3035, lr: 0.000100, took: 85.402s
[HYBRID] Epoch 3, Batch 1520/1563, loss: 3.059, reward: 34.194, critic_reward: 34.752, revenue_rate: 0.8440, distance: 10.4990, memory: -0.0073, power: 0.3171, lr: 0.000100, took: 90.195s
[HYBRID] Epoch 3, Batch 1530/1563, loss: 4.492, reward: 34.978, critic_reward: 34.442, revenue_rate: 0.8605, distance: 10.8045, memory: 0.0005, power: 0.3258, lr: 0.000100, took: 93.616s
[HYBRID] Epoch 3, Batch 1540/1563, loss: 7.745, reward: 34.699, critic_reward: 33.547, revenue_rate: 0.8543, distance: 10.6333, memory: -0.0019, power: 0.3239, lr: 0.000100, took: 91.585s
[HYBRID] Epoch 3, Batch 1550/1563, loss: 2.384, reward: 34.054, critic_reward: 34.579, revenue_rate: 0.8387, distance: 10.3919, memory: -0.0072, power: 0.3126, lr: 0.000100, took: 89.132s
[HYBRID] Epoch 3, Batch 1560/1563, loss: 6.644, reward: 34.960, critic_reward: 33.429, revenue_rate: 0.8602, distance: 10.6854, memory: -0.0023, power: 0.3257, lr: 0.000100, took: 92.346s
[HYBRID] 开始验证...
[HYBRID] 验证完成 - Epoch 3, reward: 35.825, revenue_rate: 0.8844, distance: 11.1431, memory: 0.0127, power: 0.3384
[HYBRID] 已保存新模型到 constellation_smp\constellation_smp100\multi_constellation_comparison_2025_08_25_17_02_29\constellation_gpnindrnn_hybrid_2025_08_26_17_20_30 (验证集奖励: 35.8246)
[HYBRID] 训练完成
训练结束时间: 2025-08-27 05:25:02
训练总耗时: 11:58:02.248812
训练过程统计:
  最终训练奖励: 34.7307
  最佳验证奖励: 35.8246
  训练轮数完成: 4689
  奖励提升: 22.4386
  平均每轮提升: 0.0048
生成训练曲线图...
✓ 训练曲线图已保存到: constellation_smp\constellation_smp100\multi_constellation_comparison_2025_08_25_17_02_29\constellation_gpnindrnn_hybrid_2025_08_26_17_20_30\train_loss_reward.png
开始测试 hybrid 模式...
测试配置:
  测试数据大小: 10000
  测试批次数: 157
  可视化样本数: 5
测试开始时间: 2025-08-27 05:25:43
测试结束时间: 2025-08-27 05:52:24
测试耗时: 0:26:41.542493

HYBRID 模式完整结果:
==================================================
训练结果:
  最佳验证奖励: 35.8246
  模型保存路径: constellation_smp\constellation_smp100\multi_constellation_comparison_2025_08_25_17_02_29\constellation_gpnindrnn_hybrid_2025_08_26_17_20_30
测试结果:
  平均收益率: 0.8825
  平均距离: 11.1172
  平均内存使用: 0.0126
  平均功耗: 0.3377
模型信息:
  Actor参数: 2,408,201
  Critic参数: 691,149
  总参数: 3,099,350
综合性能评分: 3.0914
文件输出:
  权重文件: actor.pt, critic.pt
  训练日志: log.txt
  测试结果: test_hybrid/
==================================================

================================================================================
生成对比分析
================================================================================
生成多模式训练曲线对比图...
✓ 多模式训练曲线图已保存

创建对比图表...
对比图表已保存到: constellation_smp\constellation_smp100\multi_constellation_comparison_2025_08_25_17_02_29\comparison_results
对比结果已保存到:
  JSON文件: constellation_smp\constellation_smp100\multi_constellation_comparison_2025_08_25_17_02_29\comparison_results\comparison_results.json
  文本报告: constellation_smp\constellation_smp100\multi_constellation_comparison_2025_08_25_17_02_29\comparison_results\comparison_report.txt
详细训练日志已保存到: constellation_smp\constellation_smp100\multi_constellation_comparison_2025_08_25_17_02_29\detailed_training_summary.txt

================================================================================
多星座模式训练实验总结
================================================================================
实验总耗时: 1 day, 12:50:03.098809
成功训练模式数: 3/3

各模式详细对比:
模式           奖励       收益率      距离       内存       功耗       参数数       
----------------------------------------------------------------------
cooperative  36.1888  0.8908   11.2893  0.0157   0.3415   2,705,622 
competitive  34.1813  0.8380   10.2708  -0.0126  0.3114   2,705,622 
hybrid       35.8246  0.8825   11.1172  0.0126   0.3377   3,099,350 

性能排名:
🏆 最高奖励: COOPERATIVE (36.1888)
💰 最高收益率: COOPERATIVE (0.8908)
🚀 最短距离: COMPETITIVE (10.2708)
⚡ 最低功耗: COMPETITIVE (0.3114)

💡 推荐模式: COOPERATIVE
   理由: 在奖励和收益率两个关键指标上都表现最佳

📁 实验结果文件:
   主目录: constellation_smp\constellation_smp100\multi_constellation_comparison_2025_08_25_17_02_29
   对比分析: constellation_smp\constellation_smp100\multi_constellation_comparison_2025_08_25_17_02_29\comparison_results
   全局日志: constellation_smp\constellation_smp100\multi_constellation_comparison_2025_08_25_17_02_29\multi_mode_training_log.txt
   cooperative 模式: constellation_smp\constellation_smp100\multi_constellation_comparison_2025_08_25_17_02_29\constellation_gpnindrnn_cooperative_2025_08_25_17_02_29
   competitive 模式: constellation_smp\constellation_smp100\multi_constellation_comparison_2025_08_25_17_02_29\constellation_gpnindrnn_competitive_2025_08_26_05_01_59
   hybrid 模式: constellation_smp\constellation_smp100\multi_constellation_comparison_2025_08_25_17_02_29\constellation_gpnindrnn_hybrid_2025_08_26_17_20_30
