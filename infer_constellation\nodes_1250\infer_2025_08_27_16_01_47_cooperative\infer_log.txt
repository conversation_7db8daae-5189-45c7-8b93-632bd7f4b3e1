推理数据数量: 100
每个序列任务数量: 1250
星座卫星数量: 3
星座模式: cooperative
模型: gpn+indrnn
种子: 12348
内存总量: 0.3
电量总量: 5
检查点路径: constellation_smp/constellation_smp100/multi_constellation_comparison_2025_08_25_17_02_29/constellation_gpnindrnn_cooperative_2025_08_25_17_02_29

批次 1:
  奖励值: 139.0754
  收益率: 0.2869
  距离: 36.6061
  内存使用: 0.6814
  能量使用: 1.1617
  推理时间: 3.2171秒

批次 2:
  奖励值: 138.9110
  收益率: 0.2792
  距离: 38.0129
  内存使用: 0.7369
  能量使用: 1.2280
  推理时间: 3.3542秒

批次 3:
  奖励值: 119.5686
  收益率: 0.2423
  距离: 34.0260
  内存使用: 0.6974
  能量使用: 0.9393
  推理时间: 3.0118秒

批次 4:
  奖励值: 124.9038
  收益率: 0.2524
  距离: 33.9983
  内存使用: 0.7414
  能量使用: 1.0527
  推理时间: 2.9929秒

批次 5:
  奖励值: 116.2120
  收益率: 0.2284
  距离: 27.3528
  内存使用: 0.8978
  能量使用: 0.9253
  推理时间: 2.6935秒

批次 6:
  奖励值: 120.3500
  收益率: 0.2385
  距离: 30.4527
  内存使用: 0.6411
  能量使用: 1.0062
  推理时间: 2.8947秒

批次 7:
  奖励值: 125.1434
  收益率: 0.2533
  距离: 33.7797
  内存使用: 0.6960
  能量使用: 1.0375
  推理时间: 2.9017秒

批次 8:
  奖励值: 141.3026
  收益率: 0.2839
  距离: 36.5044
  内存使用: 0.8042
  能量使用: 1.0861
  推理时间: 3.2376秒

批次 9:
  奖励值: 130.1976
  收益率: 0.2607
  距离: 33.2606
  内存使用: 0.6843
  能量使用: 1.0599
  推理时间: 3.0315秒

批次 10:
  奖励值: 125.3803
  收益率: 0.2512
  距离: 31.9360
  内存使用: 0.6394
  能量使用: 1.0640
  推理时间: 2.7474秒

批次 11:
  奖励值: 136.9873
  收益率: 0.2759
  距离: 33.9097
  内存使用: 0.7593
  能量使用: 1.1564
  推理时间: 3.0115秒

批次 12:
  奖励值: 127.2494
  收益率: 0.2493
  距离: 32.3453
  内存使用: 0.7208
  能量使用: 1.0350
  推理时间: 2.9986秒

批次 13:
  奖励值: 134.2039
  收益率: 0.2738
  距离: 35.9818
  内存使用: 0.7187
  能量使用: 1.0916
  推理时间: 2.9029秒

批次 14:
  奖励值: 126.9794
  收益率: 0.2546
  距离: 35.3199
  内存使用: 0.7093
  能量使用: 1.0120
  推理时间: 3.0680秒

批次 15:
  奖励值: 127.4553
  收益率: 0.2573
  距离: 34.1589
  内存使用: 0.7162
  能量使用: 1.0141
  推理时间: 2.9050秒

批次 16:
  奖励值: 124.7033
  收益率: 0.2509
  距离: 30.1152
  内存使用: 0.6298
  能量使用: 1.0484
  推理时间: 2.9140秒

批次 17:
  奖励值: 140.3149
  收益率: 0.2834
  距离: 33.1503
  内存使用: 0.7387
  能量使用: 1.0812
  推理时间: 3.3204秒

批次 18:
  奖励值: 119.8393
  收益率: 0.2391
  距离: 32.1318
  内存使用: 0.7063
  能量使用: 1.0833
  推理时间: 2.9697秒

批次 19:
  奖励值: 109.8749
  收益率: 0.2153
  距离: 30.2843
  内存使用: 0.8990
  能量使用: 0.8669
  推理时间: 2.5619秒

批次 20:
  奖励值: 117.4577
  收益率: 0.2363
  距离: 33.3162
  内存使用: 0.5749
  能量使用: 0.9935
  推理时间: 2.7456秒

批次 21:
  奖励值: 129.0868
  收益率: 0.2586
  距离: 31.4899
  内存使用: 0.6992
  能量使用: 1.0372
  推理时间: 3.1128秒

批次 22:
  奖励值: 130.8446
  收益率: 0.2591
  距离: 35.1720
  内存使用: 0.7011
  能量使用: 1.0377
  推理时间: 3.1832秒

批次 23:
  奖励值: 112.6541
  收益率: 0.2290
  距离: 30.2914
  内存使用: 0.8996
  能量使用: 0.9007
  推理时间: 2.7186秒

批次 24:
  奖励值: 120.4588
  收益率: 0.2436
  距离: 34.4835
  内存使用: 0.6173
  能量使用: 0.9815
  推理时间: 2.7815秒

批次 25:
  奖励值: 118.4434
  收益率: 0.2390
  距离: 30.7397
  内存使用: 0.6877
  能量使用: 0.8859
  推理时间: 2.5991秒

批次 26:
  奖励值: 118.4750
  收益率: 0.2391
  距离: 30.7098
  内存使用: 0.6418
  能量使用: 0.8985
  推理时间: 2.5556秒

批次 27:
  奖励值: 127.9945
  收益率: 0.2579
  距离: 32.8914
  内存使用: 0.6984
  能量使用: 1.0211
  推理时间: 2.8579秒

批次 28:
  奖励值: 120.6793
  收益率: 0.2376
  距离: 32.7779
  内存使用: 0.5956
  能量使用: 0.8679
  推理时间: 2.6987秒

批次 29:
  奖励值: 133.9733
  收益率: 0.2703
  距离: 36.5949
  内存使用: 0.7899
  能量使用: 1.0686
  推理时间: 3.0465秒

批次 30:
  奖励值: 130.1641
  收益率: 0.2628
  距离: 36.5814
  内存使用: 0.7435
  能量使用: 1.1468
  推理时间: 2.9325秒

批次 31:
  奖励值: 130.7625
  收益率: 0.2713
  距离: 37.3791
  内存使用: 0.5932
  能量使用: 1.1028
  推理时间: 3.1131秒

批次 32:
  奖励值: 134.1698
  收益率: 0.2671
  距离: 33.1903
  内存使用: 0.7736
  能量使用: 1.0964
  推理时间: 2.9066秒

批次 33:
  奖励值: 142.0708
  收益率: 0.2867
  距离: 40.4568
  内存使用: 0.7751
  能量使用: 1.2264
  推理时间: 3.1413秒

批次 34:
  奖励值: 135.5349
  收益率: 0.2745
  距离: 34.2960
  内存使用: 0.7465
  能量使用: 1.0877
  推理时间: 2.9659秒

批次 35:
  奖励值: 109.6306
  收益率: 0.2242
  距离: 32.7411
  内存使用: 0.8990
  能量使用: 0.8987
  推理时间: 2.4490秒

批次 36:
  奖励值: 114.0033
  收益率: 0.2347
  距离: 30.7165
  内存使用: 0.8993
  能量使用: 0.9776
  推理时间: 2.5523秒

批次 37:
  奖励值: 129.7387
  收益率: 0.2570
  距离: 29.1589
  内存使用: 0.6835
  能量使用: 1.0801
  推理时间: 3.0213秒

批次 38:
  奖励值: 127.9388
  收益率: 0.2545
  距离: 34.8960
  内存使用: 0.6275
  能量使用: 0.9989
  推理时间: 2.8328秒

批次 39:
  奖励值: 120.7587
  收益率: 0.2441
  距离: 33.0993
  内存使用: 0.6541
  能量使用: 0.9292
  推理时间: 2.6775秒

批次 40:
  奖励值: 123.4619
  收益率: 0.2490
  距离: 31.8140
  内存使用: 0.6944
  能量使用: 0.9677
  推理时间: 2.6771秒

批次 41:
  奖励值: 136.9494
  收益率: 0.2773
  距离: 36.6478
  内存使用: 0.7388
  能量使用: 1.1144
  推理时间: 3.3598秒

批次 42:
  奖励值: 118.9837
  收益率: 0.2361
  距离: 31.2688
  内存使用: 0.6115
  能量使用: 0.9521
  推理时间: 2.7306秒

批次 43:
  奖励值: 107.0958
  收益率: 0.2186
  距离: 29.8645
  内存使用: 0.8998
  能量使用: 0.8441
  推理时间: 2.4349秒

批次 44:
  奖励值: 114.7259
  收益率: 0.2319
  距离: 31.0425
  内存使用: 0.6090
  能量使用: 0.8967
  推理时间: 2.8051秒

批次 45:
  奖励值: 142.9244
  收益率: 0.2919
  距离: 37.6602
  内存使用: 0.8211
  能量使用: 1.2699
  推理时间: 3.1396秒

批次 46:
  奖励值: 111.6588
  收益率: 0.2266
  距离: 29.4025
  内存使用: 0.6194
  能量使用: 0.8604
  推理时间: 2.5983秒

批次 47:
  奖励值: 122.9905
  收益率: 0.2432
  距离: 30.5464
  内存使用: 0.7161
  能量使用: 0.9563
  推理时间: 2.7768秒

批次 48:
  奖励值: 112.8384
  收益率: 0.2297
  距离: 28.8040
  内存使用: 0.6035
  能量使用: 0.9156
  推理时间: 2.7178秒

批次 49:
  奖励值: 129.4433
  收益率: 0.2621
  距离: 34.1547
  内存使用: 0.6893
  能量使用: 1.0695
  推理时间: 3.0074秒

批次 50:
  奖励值: 121.1558
  收益率: 0.2460
  距离: 29.6211
  内存使用: 0.6537
  能量使用: 1.0135
  推理时间: 2.6105秒

批次 51:
  奖励值: 135.1812
  收益率: 0.2740
  距离: 36.5704
  内存使用: 0.7569
  能量使用: 1.1102
  推理时间: 3.2652秒

批次 52:
  奖励值: 126.0900
  收益率: 0.2571
  距离: 34.2032
  内存使用: 0.6348
  能量使用: 0.9821
  推理时间: 2.9614秒

批次 53:
  奖励值: 129.6724
  收益率: 0.2620
  距离: 36.0968
  内存使用: 0.7640
  能量使用: 0.9944
  推理时间: 2.7878秒

批次 54:
  奖励值: 117.2215
  收益率: 0.2466
  距离: 34.3989
  内存使用: 0.6295
  能量使用: 0.9285
  推理时间: 2.6106秒

批次 55:
  奖励值: 124.5089
  收益率: 0.2482
  距离: 30.5076
  内存使用: 0.7346
  能量使用: 0.9399
  推理时间: 2.7158秒

批次 56:
  奖励值: 122.9399
  收益率: 0.2492
  距离: 33.5440
  内存使用: 0.6406
  能量使用: 0.9356
  推理时间: 2.6577秒

批次 57:
  奖励值: 116.2012
  收益率: 0.2290
  距离: 27.5507
  内存使用: 0.6179
  能量使用: 1.0291
  推理时间: 2.5038秒

批次 58:
  奖励值: 138.9968
  收益率: 0.2851
  距离: 36.8754
  内存使用: 0.7244
  能量使用: 1.2064
  推理时间: 3.0603秒

批次 59:
  奖励值: 124.2892
  收益率: 0.2433
  距离: 32.1168
  内存使用: 0.6455
  能量使用: 1.0594
  推理时间: 2.8063秒

批次 60:
  奖励值: 130.4793
  收益率: 0.2726
  距离: 36.7696
  内存使用: 0.7231
  能量使用: 1.1487
  推理时间: 2.9748秒

批次 61:
  奖励值: 116.3568
  收益率: 0.2269
  距离: 29.8337
  内存使用: 0.5510
  能量使用: 0.9018
  推理时间: 2.5394秒

批次 62:
  奖励值: 125.6448
  收益率: 0.2527
  距离: 32.8210
  内存使用: 0.8171
  能量使用: 1.0723
  推理时间: 2.8760秒

批次 63:
  奖励值: 145.5753
  收益率: 0.2942
  距离: 38.4015
  内存使用: 0.8667
  能量使用: 1.1811
  推理时间: 3.1880秒

批次 64:
  奖励值: 123.7690
  收益率: 0.2468
  距离: 34.0077
  内存使用: 0.6501
  能量使用: 1.0040
  推理时间: 2.8066秒

批次 65:
  奖励值: 114.4900
  收益率: 0.2309
  距离: 32.6908
  内存使用: 0.6170
  能量使用: 0.9121
  推理时间: 2.5529秒

批次 66:
  奖励值: 119.3402
  收益率: 0.2458
  距离: 31.4664
  内存使用: 0.6500
  能量使用: 1.0031
  推理时间: 2.5935秒

批次 67:
  奖励值: 112.6262
  收益率: 0.2252
  距离: 29.6593
  内存使用: 0.6171
  能量使用: 0.8712
  推理时间: 2.4883秒

批次 68:
  奖励值: 123.6970
  收益率: 0.2535
  距离: 29.3041
  内存使用: 0.7097
  能量使用: 0.9922
  推理时间: 2.6523秒

批次 69:
  奖励值: 124.6390
  收益率: 0.2508
  距离: 30.5103
  内存使用: 0.6024
  能量使用: 1.0093
  推理时间: 2.7330秒

批次 70:
  奖励值: 122.7095
  收益率: 0.2404
  距离: 32.2193
  内存使用: 0.6070
  能量使用: 0.9545
  推理时间: 2.6478秒

批次 71:
  奖励值: 117.6707
  收益率: 0.2395
  距离: 30.6061
  内存使用: 0.8993
  能量使用: 0.9531
  推理时间: 2.6136秒

批次 72:
  奖励值: 120.0341
  收益率: 0.2413
  距离: 30.9197
  内存使用: 0.5826
  能量使用: 1.0295
  推理时间: 2.6265秒

批次 73:
  奖励值: 111.6999
  收益率: 0.2206
  距离: 27.3488
  内存使用: 0.6188
  能量使用: 0.8327
  推理时间: 2.4066秒

批次 74:
  奖励值: 120.9990
  收益率: 0.2460
  距离: 31.9282
  内存使用: 0.6537
  能量使用: 1.0202
  推理时间: 2.8889秒

批次 75:
  奖励值: 132.3937
  收益率: 0.2603
  距离: 30.6200
  内存使用: 0.7220
  能量使用: 1.0705
  推理时间: 2.9310秒

批次 76:
  奖励值: 104.9610
  收益率: 0.2125
  距离: 27.1311
  内存使用: 0.8995
  能量使用: 0.8473
  推理时间: 2.3581秒

批次 77:
  奖励值: 128.4963
  收益率: 0.2657
  距离: 34.4168
  内存使用: 0.7079
  能量使用: 1.0827
  推理时间: 2.8179秒

批次 78:
  奖励值: 133.1854
  收益率: 0.2674
  距离: 33.0860
  内存使用: 0.6982
  能量使用: 1.0450
  推理时间: 2.7956秒

批次 79:
  奖励值: 112.1977
  收益率: 0.2255
  距离: 31.6326
  内存使用: 0.6238
  能量使用: 0.9483
  推理时间: 2.7473秒

批次 80:
  奖励值: 111.1439
  收益率: 0.2247
  距离: 28.7162
  内存使用: 0.6715
  能量使用: 0.9245
  推理时间: 2.4492秒

批次 81:
  奖励值: 109.1050
  收益率: 0.2231
  距离: 28.3857
  内存使用: 0.8999
  能量使用: 0.9043
  推理时间: 2.4125秒

批次 82:
  奖励值: 127.6072
  收益率: 0.2506
  距离: 32.7659
  内存使用: 0.6680
  能量使用: 0.9517
  推理时间: 2.7063秒

批次 83:
  奖励值: 120.3524
  收益率: 0.2417
  距离: 31.1357
  内存使用: 0.6452
  能量使用: 0.9142
  推理时间: 2.5604秒

批次 84:
  奖励值: 120.7589
  收益率: 0.2488
  距离: 32.8155
  内存使用: 0.6589
  能量使用: 1.0076
  推理时间: 2.8271秒

批次 85:
  奖励值: 134.4944
  收益率: 0.2767
  距离: 37.6111
  内存使用: 0.6807
  能量使用: 1.1194
  推理时间: 3.1564秒

批次 86:
  奖励值: 139.4849
  收益率: 0.2784
  距离: 34.4335
  内存使用: 0.7483
  能量使用: 1.0483
  推理时间: 3.1805秒

批次 87:
  奖励值: 124.6172
  收益率: 0.2540
  距离: 31.9822
  内存使用: 0.6962
  能量使用: 1.0871
  推理时间: 2.9106秒

批次 88:
  奖励值: 126.1819
  收益率: 0.2596
  距离: 30.6719
  内存使用: 0.7184
  能量使用: 1.0460
  推理时间: 2.7452秒

批次 89:
  奖励值: 122.2508
  收益率: 0.2493
  距离: 37.4855
  内存使用: 0.6042
  能量使用: 1.0086
  推理时间: 2.7550秒

批次 90:
  奖励值: 126.2632
  收益率: 0.2538
  距离: 33.1088
  内存使用: 0.6848
  能量使用: 1.0799
  推理时间: 2.9078秒

批次 91:
  奖励值: 128.3389
  收益率: 0.2543
  距离: 34.6252
  内存使用: 0.6922
  能量使用: 1.0049
  推理时间: 2.8453秒

批次 92:
  奖励值: 135.4563
  收益率: 0.2739
  距离: 33.3893
  内存使用: 0.8072
  能量使用: 1.0909
  推理时间: 3.0111秒

批次 93:
  奖励值: 118.0476
  收益率: 0.2364
  距离: 29.7206
  内存使用: 0.6414
  能量使用: 0.9428
  推理时间: 2.5307秒

批次 94:
  奖励值: 115.2461
  收益率: 0.2349
  距离: 32.4850
  内存使用: 0.8996
  能量使用: 0.9231
  推理时间: 2.8200秒

批次 95:
  奖励值: 122.3018
  收益率: 0.2386
  距离: 32.3951
  内存使用: 0.6112
  能量使用: 0.9900
  推理时间: 2.5926秒

批次 96:
  奖励值: 135.9665
  收益率: 0.2691
  距离: 35.4116
  内存使用: 0.7817
  能量使用: 1.0735
  推理时间: 3.1281秒

批次 97:
  奖励值: 137.9301
  收益率: 0.2809
  距离: 35.3927
  内存使用: 0.7299
  能量使用: 1.0172
  推理时间: 2.9174秒

批次 98:
  奖励值: 112.5273
  收益率: 0.2212
  距离: 27.7296
  内存使用: 0.6095
  能量使用: 0.8063
  推理时间: 2.5860秒

批次 99:
  奖励值: 118.5299
  收益率: 0.2390
  距离: 31.9705
  内存使用: 0.6425
  能量使用: 0.9271
  推理时间: 2.6706秒

批次 100:
  奖励值: 141.3504
  收益率: 0.2835
  距离: 36.8443
  内存使用: 0.8382
  能量使用: 1.2167
  推理时间: 2.9901秒


==================== 总结 ====================
平均收益率: 0.2514
平均能量使用: 1.0120
平均推理时间: 2.8279秒
