推理数据数量: 100
每个序列任务数量: 2000
星座卫星数量: 3
星座模式: cooperative
模型: gpn+indrnn
种子: 12348
内存总量: 0.3
电量总量: 5
检查点路径: constellation_smp/constellation_smp100/multi_constellation_comparison_2025_08_25_17_02_29/constellation_gpnindrnn_cooperative_2025_08_25_17_02_29

批次 1:
  奖励值: 138.4371
  收益率: 0.1734
  距离: 31.6027
  内存使用: 0.7409
  能量使用: 0.9820
  推理时间: 2.9688秒

批次 2:
  奖励值: 129.0840
  收益率: 0.1624
  距离: 31.2532
  内存使用: 0.7077
  能量使用: 1.0711
  推理时间: 2.8487秒

批次 3:
  奖励值: 140.3112
  收益率: 0.1769
  距离: 36.8970
  内存使用: 0.8336
  能量使用: 1.0470
  推理时间: 3.0424秒

批次 4:
  奖励值: 142.7277
  收益率: 0.1799
  距离: 42.1692
  内存使用: 0.8208
  能量使用: 1.1545
  推理时间: 3.1678秒

批次 5:
  奖励值: 146.7485
  收益率: 0.1862
  距离: 35.0940
  内存使用: 0.7956
  能量使用: 1.2134
  推理时间: 3.1287秒

批次 6:
  奖励值: 152.8013
  收益率: 0.1910
  距离: 38.9228
  内存使用: 0.8198
  能量使用: 1.2547
  推理时间: 3.2825秒

批次 7:
  奖励值: 120.7293
  收益率: 0.1512
  距离: 31.6539
  内存使用: 0.8999
  能量使用: 0.9875
  推理时间: 2.7108秒

批次 8:
  奖励值: 108.6862
  收益率: 0.1347
  距离: 24.6950
  内存使用: 0.8998
  能量使用: 0.8328
  推理时间: 2.3870秒

批次 9:
  奖励值: 125.1049
  收益率: 0.1539
  距离: 29.5194
  内存使用: 0.6474
  能量使用: 1.0235
  推理时间: 3.0097秒

批次 10:
  奖励值: 116.7412
  收益率: 0.1477
  距离: 30.7503
  内存使用: 0.6053
  能量使用: 0.9620
  推理时间: 2.5996秒

批次 11:
  奖励值: 132.0760
  收益率: 0.1720
  距离: 38.6642
  内存使用: 0.7873
  能量使用: 0.9964
  推理时间: 2.9630秒

批次 12:
  奖励值: 126.4399
  收益率: 0.1590
  距离: 30.8068
  内存使用: 0.6697
  能量使用: 1.0437
  推理时间: 2.7721秒

批次 13:
  奖励值: 132.8766
  收益率: 0.1663
  距离: 33.1649
  内存使用: 0.6092
  能量使用: 1.0726
  推理时间: 2.8513秒

批次 14:
  奖励值: 117.1594
  收益率: 0.1472
  距离: 28.2166
  内存使用: 0.8999
  能量使用: 0.9647
  推理时间: 2.5509秒

批次 15:
  奖励值: 125.5705
  收益率: 0.1576
  距离: 32.3719
  内存使用: 0.6333
  能量使用: 1.0536
  推理时间: 2.7845秒

批次 16:
  奖励值: 125.6011
  收益率: 0.1564
  距离: 31.7803
  内存使用: 0.6497
  能量使用: 0.9677
  推理时间: 2.7672秒

批次 17:
  奖励值: 129.1384
  收益率: 0.1613
  距离: 31.1904
  内存使用: 0.6626
  能量使用: 1.0702
  推理时间: 2.7984秒

批次 18:
  奖励值: 133.1632
  收益率: 0.1655
  距离: 30.0639
  内存使用: 0.7465
  能量使用: 1.0631
  推理时间: 2.8537秒

批次 19:
  奖励值: 139.1794
  收益率: 0.1760
  距离: 35.0860
  内存使用: 0.7113
  能量使用: 1.0439
  推理时间: 3.0394秒

批次 20:
  奖励值: 149.1422
  收益率: 0.1872
  距离: 33.3322
  内存使用: 0.7848
  能量使用: 1.1985
  推理时间: 3.1911秒

批次 21:
  奖励值: 129.1580
  收益率: 0.1641
  距离: 31.1881
  内存使用: 0.6316
  能量使用: 1.0642
  推理时间: 2.7498秒

批次 22:
  奖励值: 134.3468
  收益率: 0.1717
  距离: 33.4280
  内存使用: 0.7503
  能量使用: 1.1281
  推理时间: 2.9270秒

批次 23:
  奖励值: 125.6465
  收益率: 0.1576
  距离: 33.8356
  内存使用: 0.6969
  能量使用: 0.9745
  推理时间: 2.8021秒

批次 24:
  奖励值: 125.4529
  收益率: 0.1546
  距离: 28.2508
  内存使用: 0.6877
  能量使用: 0.9556
  推理时间: 2.6763秒

批次 25:
  奖励值: 123.1783
  收益率: 0.1562
  距离: 33.9905
  内存使用: 0.6281
  能量使用: 1.0248
  推理时间: 2.7846秒

批次 26:
  奖励值: 119.1668
  收益率: 0.1515
  距离: 28.7095
  内存使用: 0.6462
  能量使用: 0.9106
  推理时间: 2.6407秒

批次 27:
  奖励值: 113.0745
  收益率: 0.1425
  距离: 29.8456
  内存使用: 0.8998
  能量使用: 0.9056
  推理时间: 2.5390秒

批次 28:
  奖励值: 118.2228
  收益率: 0.1468
  距离: 30.7318
  内存使用: 0.8999
  能量使用: 0.9227
  推理时间: 2.6229秒

批次 29:
  奖励值: 136.2271
  收益率: 0.1705
  距离: 31.3742
  内存使用: 0.6503
  能量使用: 1.1213
  推理时间: 2.9208秒

批次 30:
  奖励值: 108.5667
  收益率: 0.1367
  距离: 28.7092
  内存使用: 0.8997
  能量使用: 0.8576
  推理时间: 2.4587秒

批次 31:
  奖励值: 129.5423
  收益率: 0.1652
  距离: 35.3425
  内存使用: 0.7322
  能量使用: 1.0406
  推理时间: 3.2264秒

批次 32:
  奖励值: 109.9198
  收益率: 0.1389
  距离: 29.4615
  内存使用: 0.8999
  能量使用: 0.9989
  推理时间: 2.4741秒

批次 33:
  奖励值: 122.2139
  收益率: 0.1551
  距离: 33.2954
  内存使用: 0.6819
  能量使用: 0.9949
  推理时间: 2.7198秒

批次 34:
  奖励值: 149.8139
  收益率: 0.1885
  距离: 40.5628
  内存使用: 0.8260
  能量使用: 1.1833
  推理时间: 3.2608秒

批次 35:
  奖励值: 139.1937
  收益率: 0.1714
  距离: 34.7504
  内存使用: 0.6612
  能量使用: 1.0008
  推理时间: 2.9386秒

批次 36:
  奖励值: 124.3541
  收益率: 0.1537
  距离: 29.6221
  内存使用: 0.6918
  能量使用: 0.9779
  推理时间: 2.6849秒

批次 37:
  奖励值: 124.5102
  收益率: 0.1587
  距离: 31.6707
  内存使用: 0.6620
  能量使用: 1.0468
  推理时间: 2.7360秒

批次 38:
  奖励值: 117.7988
  收益率: 0.1494
  距离: 26.4746
  内存使用: 0.8630
  能量使用: 0.8649
  推理时间: 2.5563秒

批次 39:
  奖励值: 117.6540
  收益率: 0.1483
  距离: 28.5978
  内存使用: 0.6123
  能量使用: 0.8818
  推理时间: 2.5950秒

批次 40:
  奖励值: 123.6953
  收益率: 0.1507
  距离: 28.7409
  内存使用: 0.6772
  能量使用: 0.9912
  推理时间: 2.6707秒

批次 41:
  奖励值: 130.5157
  收益率: 0.1645
  距离: 35.6519
  内存使用: 0.6566
  能量使用: 1.0601
  推理时间: 2.8762秒

批次 42:
  奖励值: 126.9545
  收益率: 0.1583
  距离: 31.4443
  内存使用: 0.6418
  能量使用: 1.0049
  推理时间: 2.7066秒

批次 43:
  奖励值: 136.2347
  收益率: 0.1699
  距离: 35.1847
  内存使用: 0.7503
  能量使用: 1.1956
  推理时间: 3.0173秒

批次 44:
  奖励值: 134.8457
  收益率: 0.1709
  距离: 36.9320
  内存使用: 0.8055
  能量使用: 1.1712
  推理时间: 2.9740秒

批次 45:
  奖励值: 130.1283
  收益率: 0.1630
  距离: 33.2464
  内存使用: 0.6973
  能量使用: 0.9997
  推理时间: 2.8322秒

批次 46:
  奖励值: 142.8402
  收益率: 0.1835
  距离: 34.3464
  内存使用: 0.8132
  能量使用: 1.1601
  推理时间: 3.0880秒

批次 47:
  奖励值: 129.6096
  收益率: 0.1627
  距离: 35.4665
  内存使用: 0.6355
  能量使用: 1.0788
  推理时间: 2.8472秒

批次 48:
  奖励值: 135.9986
  收益率: 0.1693
  距离: 33.0509
  内存使用: 0.7255
  能量使用: 1.0263
  推理时间: 2.9094秒

批次 49:
  奖励值: 133.8246
  收益率: 0.1712
  距离: 36.0197
  内存使用: 0.6594
  能量使用: 1.0715
  推理时间: 2.8909秒

批次 50:
  奖励值: 126.5786
  收益率: 0.1587
  距离: 32.6935
  内存使用: 0.7507
  能量使用: 0.9986
  推理时间: 2.7964秒

批次 51:
  奖励值: 149.1079
  收益率: 0.1915
  距离: 41.0187
  内存使用: 0.7648
  能量使用: 1.2006
  推理时间: 3.2171秒

批次 52:
  奖励值: 111.7024
  收益率: 0.1394
  距离: 29.8416
  内存使用: 0.8991
  能量使用: 0.8546
  推理时间: 2.4531秒

批次 53:
  奖励值: 130.9529
  收益率: 0.1648
  距离: 34.0815
  内存使用: 0.7385
  能量使用: 1.0743
  推理时间: 2.8476秒

批次 54:
  奖励值: 124.0117
  收益率: 0.1572
  距离: 31.4750
  内存使用: 0.6167
  能量使用: 1.0847
  推理时间: 2.7405秒

批次 55:
  奖励值: 113.5150
  收益率: 0.1445
  距离: 30.7809
  内存使用: 0.8968
  能量使用: 0.8922
  推理时间: 2.5330秒

批次 56:
  奖励值: 145.8289
  收益率: 0.1830
  距离: 35.5330
  内存使用: 0.8320
  能量使用: 1.1772
  推理时间: 3.1825秒

批次 57:
  奖励值: 132.1091
  收益率: 0.1661
  距离: 34.2376
  内存使用: 0.7172
  能量使用: 1.0268
  推理时间: 2.8454秒

批次 58:
  奖励值: 123.5572
  收益率: 0.1537
  距离: 28.6262
  内存使用: 0.8995
  能量使用: 0.9597
  推理时间: 2.7378秒

批次 59:
  奖励值: 142.5641
  收益率: 0.1791
  距离: 34.4075
  内存使用: 0.7582
  能量使用: 1.1059
  推理时间: 3.1336秒

批次 60:
  奖励值: 115.7751
  收益率: 0.1479
  距离: 28.2164
  内存使用: 0.8999
  能量使用: 0.9335
  推理时间: 2.5707秒

批次 61:
  奖励值: 116.6221
  收益率: 0.1473
  距离: 32.3503
  内存使用: 0.8991
  能量使用: 0.9063
  推理时间: 2.7119秒

批次 62:
  奖励值: 130.8090
  收益率: 0.1622
  距离: 32.4832
  内存使用: 0.6694
  能量使用: 1.0294
  推理时间: 2.8724秒

批次 63:
  奖励值: 144.1884
  收益率: 0.1815
  距离: 37.8761
  内存使用: 0.6983
  能量使用: 1.0489
  推理时间: 3.1301秒

批次 64:
  奖励值: 136.8809
  收益率: 0.1732
  距离: 34.7409
  内存使用: 0.6738
  能量使用: 1.0234
  推理时间: 2.9901秒

批次 65:
  奖励值: 150.3482
  收益率: 0.1830
  距离: 37.3096
  内存使用: 0.8186
  能量使用: 1.1755
  推理时间: 3.4226秒

批次 66:
  奖励值: 122.3427
  收益率: 0.1538
  距离: 28.3057
  内存使用: 0.6639
  能量使用: 0.9449
  推理时间: 2.5610秒

批次 67:
  奖励值: 128.0975
  收益率: 0.1589
  距离: 28.6526
  内存使用: 0.6803
  能量使用: 1.0720
  推理时间: 2.7568秒

批次 68:
  奖励值: 140.7146
  收益率: 0.1777
  距离: 38.2487
  内存使用: 0.7753
  能量使用: 1.1469
  推理时间: 3.0609秒

批次 69:
  奖励值: 143.1089
  收益率: 0.1821
  距离: 37.7524
  内存使用: 0.8069
  能量使用: 1.0229
  推理时间: 3.1243秒

批次 70:
  奖励值: 126.0336
  收益率: 0.1562
  距离: 31.8289
  内存使用: 0.7177
  能量使用: 0.9861
  推理时间: 2.7220秒

批次 71:
  奖励值: 117.7332
  收益率: 0.1476
  距离: 30.6924
  内存使用: 0.8720
  能量使用: 0.9692
  推理时间: 2.5978秒

批次 72:
  奖励值: 127.6130
  收益率: 0.1586
  距离: 32.7060
  内存使用: 0.6703
  能量使用: 0.9976
  推理时间: 2.7089秒

批次 73:
  奖励值: 149.2689
  收益率: 0.1862
  距离: 36.7584
  内存使用: 0.8780
  能量使用: 1.2258
  推理时间: 3.2085秒

批次 74:
  奖励值: 133.1853
  收益率: 0.1647
  距离: 31.6458
  内存使用: 0.7114
  能量使用: 1.0721
  推理时间: 2.8568秒

批次 75:
  奖励值: 123.9797
  收益率: 0.1562
  距离: 33.2228
  内存使用: 0.6818
  能量使用: 0.9361
  推理时间: 2.7117秒

批次 76:
  奖励值: 126.3089
  收益率: 0.1588
  距离: 31.8794
  内存使用: 0.6893
  能量使用: 1.0136
  推理时间: 2.7499秒

批次 77:
  奖励值: 142.0057
  收益率: 0.1775
  距离: 33.6140
  内存使用: 0.8431
  能量使用: 1.1316
  推理时间: 3.0712秒

批次 78:
  奖励值: 124.3569
  收益率: 0.1571
  距离: 28.5754
  内存使用: 0.6046
  能量使用: 0.9063
  推理时间: 2.7956秒

批次 79:
  奖励值: 140.1878
  收益率: 0.1778
  距离: 31.9948
  内存使用: 0.7366
  能量使用: 1.0931
  推理时间: 3.3569秒

批次 80:
  奖励值: 129.0078
  收益率: 0.1611
  距离: 30.9093
  内存使用: 0.6867
  能量使用: 0.9884
  推理时间: 2.8187秒

批次 81:
  奖励值: 116.2597
  收益率: 0.1472
  距离: 30.3254
  内存使用: 0.8999
  能量使用: 0.8554
  推理时间: 2.6114秒

批次 82:
  奖励值: 127.1306
  收益率: 0.1602
  距离: 32.4365
  内存使用: 0.6496
  能量使用: 1.0526
  推理时间: 2.8452秒

批次 83:
  奖励值: 125.0264
  收益率: 0.1554
  距离: 29.4090
  内存使用: 0.6944
  能量使用: 1.0438
  推理时间: 3.0214秒

批次 84:
  奖励值: 149.0894
  收益率: 0.1857
  距离: 34.0714
  内存使用: 0.8074
  能量使用: 1.1423
  推理时间: 3.2468秒

批次 85:
  奖励值: 121.8339
  收益率: 0.1556
  距离: 33.8760
  内存使用: 0.8932
  能量使用: 0.9628
  推理时间: 2.6301秒

批次 86:
  奖励值: 130.4513
  收益率: 0.1604
  距离: 30.7118
  内存使用: 0.6288
  能量使用: 0.9568
  推理时间: 2.6747秒

批次 87:
  奖励值: 114.5220
  收益率: 0.1446
  距离: 28.1489
  内存使用: 0.8999
  能量使用: 0.9071
  推理时间: 2.4908秒

批次 88:
  奖励值: 135.3368
  收益率: 0.1669
  距离: 34.4882
  内存使用: 0.7755
  能量使用: 1.0626
  推理时间: 2.7297秒

批次 89:
  奖励值: 156.8809
  收益率: 0.1921
  距离: 38.2817
  内存使用: 0.8729
  能量使用: 1.2834
  推理时间: 3.0814秒

批次 90:
  奖励值: 124.9563
  收益率: 0.1568
  距离: 32.1593
  内存使用: 0.6238
  能量使用: 0.9042
  推理时间: 2.6288秒

批次 91:
  奖励值: 115.8537
  收益率: 0.1473
  距离: 33.7025
  内存使用: 0.8998
  能量使用: 1.0903
  推理时间: 2.4649秒

批次 92:
  奖励值: 111.2305
  收益率: 0.1381
  距离: 29.7631
  内存使用: 0.8999
  能量使用: 0.8760
  推理时间: 2.2984秒

批次 93:
  奖励值: 135.4493
  收益率: 0.1669
  距离: 32.3005
  内存使用: 0.7290
  能量使用: 1.0719
  推理时间: 3.4203秒

批次 94:
  奖励值: 125.2114
  收益率: 0.1600
  距离: 33.0942
  内存使用: 0.6391
  能量使用: 1.0440
  推理时间: 2.5755秒

批次 95:
  奖励值: 126.3599
  收益率: 0.1604
  距离: 32.6515
  内存使用: 0.6465
  能量使用: 0.9821
  推理时间: 2.6263秒

批次 96:
  奖励值: 117.0623
  收益率: 0.1495
  距离: 32.1855
  内存使用: 0.8998
  能量使用: 0.9901
  推理时间: 2.7926秒

批次 97:
  奖励值: 137.5526
  收益率: 0.1729
  距离: 33.8194
  内存使用: 0.7932
  能量使用: 1.0822
  推理时间: 3.2955秒

批次 98:
  奖励值: 118.0210
  收益率: 0.1483
  距离: 31.1344
  内存使用: 0.8999
  能量使用: 0.9236
  推理时间: 3.0032秒

批次 99:
  奖励值: 145.0723
  收益率: 0.1827
  距离: 38.2793
  内存使用: 0.7383
  能量使用: 1.2378
  推理时间: 3.3760秒

批次 100:
  奖励值: 122.5394
  收益率: 0.1547
  距离: 30.8708
  内存使用: 0.8996
  能量使用: 0.9508
  推理时间: 2.8569秒


==================== 总结 ====================
平均收益率: 0.1626
平均能量使用: 1.0304
平均推理时间: 2.8480秒
