推理数据数量: 100
每个序列任务数量: 2000
星座卫星数量: 3
星座模式: competitive
模型: gpn+indrnn
种子: 12348
内存总量: 0.3
电量总量: 5
检查点路径: constellation_smp/constellation_smp100/multi_constellation_comparison_2025_08_25_17_02_29/constellation_gpnindrnn_competitive_2025_08_26_05_01_59

批次 1:
  奖励值: 151.7120
  收益率: 0.1895
  距离: 33.7172
  内存使用: 0.7666
  能量使用: 1.0921
  推理时间: 2.8952秒

批次 2:
  奖励值: 135.7008
  收益率: 0.1708
  距离: 33.2239
  内存使用: 0.7717
  能量使用: 1.0028
  推理时间: 2.8235秒

批次 3:
  奖励值: 136.2795
  收益率: 0.1702
  距离: 32.8927
  内存使用: 0.7208
  能量使用: 1.0275
  推理时间: 2.7302秒

批次 4:
  奖励值: 145.0947
  收益率: 0.1795
  距离: 36.6700
  内存使用: 0.8129
  能量使用: 1.1277
  推理时间: 3.0590秒

批次 5:
  奖励值: 134.6343
  收益率: 0.1719
  距离: 34.1600
  内存使用: 0.7072
  能量使用: 1.0642
  推理时间: 2.8938秒

批次 6:
  奖励值: 154.2772
  收益率: 0.1928
  距离: 39.2028
  内存使用: 0.8368
  能量使用: 1.2061
  推理时间: 3.2224秒

批次 7:
  奖励值: 136.5727
  收益率: 0.1699
  距离: 33.9541
  内存使用: 0.7888
  能量使用: 1.1117
  推理时间: 2.8622秒

批次 8:
  奖励值: 125.0703
  收益率: 0.1570
  距离: 32.5049
  内存使用: 0.6041
  能量使用: 0.9504
  推理时间: 2.4476秒

批次 9:
  奖励值: 143.6474
  收益率: 0.1770
  距离: 34.4704
  内存使用: 0.7146
  能量使用: 1.1200
  推理时间: 2.9786秒

批次 10:
  奖励值: 126.2501
  收益率: 0.1573
  距离: 28.8553
  内存使用: 0.6089
  能量使用: 1.0356
  推理时间: 2.7156秒

批次 11:
  奖励值: 142.0941
  收益率: 0.1804
  距离: 33.5922
  内存使用: 0.7422
  能量使用: 1.0773
  推理时间: 2.9474秒

批次 12:
  奖励值: 112.5731
  收益率: 0.1412
  距离: 26.9169
  内存使用: 0.6591
  能量使用: 0.8777
  推理时间: 2.2655秒

批次 13:
  奖励值: 162.1699
  收益率: 0.2014
  距离: 37.5985
  内存使用: 0.8438
  能量使用: 1.2178
  推理时间: 3.2559秒

批次 14:
  奖励值: 144.8492
  收益率: 0.1832
  距离: 37.2896
  内存使用: 0.8121
  能量使用: 1.1997
  推理时间: 3.0129秒

批次 15:
  奖励值: 131.0937
  收益率: 0.1648
  距离: 34.2733
  内存使用: 0.6638
  能量使用: 1.0754
  推理时间: 2.5789秒

批次 16:
  奖励值: 134.3121
  收益率: 0.1674
  距离: 34.2614
  内存使用: 0.6876
  能量使用: 1.0269
  推理时间: 2.6874秒

批次 17:
  奖励值: 108.1614
  收益率: 0.1356
  距离: 27.0493
  内存使用: 0.5386
  能量使用: 0.8893
  推理时间: 2.3444秒

批次 18:
  奖励值: 139.0689
  收益率: 0.1734
  距离: 32.3923
  内存使用: 0.7557
  能量使用: 1.1142
  推理时间: 2.7884秒

批次 19:
  奖励值: 136.3297
  收益率: 0.1712
  距离: 32.2346
  内存使用: 0.6946
  能量使用: 1.0113
  推理时间: 2.8399秒

批次 20:
  奖励值: 146.3047
  收益率: 0.1852
  距离: 35.4491
  内存使用: 0.7794
  能量使用: 1.1619
  推理时间: 3.1041秒

批次 21:
  奖励值: 122.9333
  收益率: 0.1548
  距离: 27.3449
  内存使用: 0.6405
  能量使用: 0.9481
  推理时间: 2.6207秒

批次 22:
  奖励值: 137.5786
  收益率: 0.1776
  距离: 37.3897
  内存使用: 0.7955
  能量使用: 1.0576
  推理时间: 2.9092秒

批次 23:
  奖励值: 136.6447
  收益率: 0.1705
  距离: 35.3731
  内存使用: 0.6560
  能量使用: 1.0815
  推理时间: 3.1657秒

批次 24:
  奖励值: 134.5548
  收益率: 0.1670
  距离: 32.6929
  内存使用: 0.6500
  能量使用: 0.9475
  推理时间: 2.7145秒

批次 25:
  奖励值: 120.6746
  收益率: 0.1514
  距离: 30.2745
  内存使用: 0.6155
  能量使用: 0.9608
  推理时间: 2.6005秒

批次 26:
  奖励值: 131.9181
  收益率: 0.1676
  距离: 31.7101
  内存使用: 0.6944
  能量使用: 0.9592
  推理时间: 2.7654秒

批次 27:
  奖励值: 116.6378
  收益率: 0.1468
  距离: 30.4559
  内存使用: 0.8998
  能量使用: 0.9686
  推理时间: 2.8013秒

批次 28:
  奖励值: 128.6794
  收益率: 0.1575
  距离: 29.3489
  内存使用: 0.8998
  能量使用: 0.9925
  推理时间: 2.7022秒

批次 29:
  奖励值: 150.9718
  收益率: 0.1898
  距离: 36.2905
  内存使用: 0.8059
  能量使用: 1.1600
  推理时间: 3.0108秒

批次 30:
  奖励值: 139.7799
  收益率: 0.1744
  距离: 34.5667
  内存使用: 0.8474
  能量使用: 1.0260
  推理时间: 2.9130秒

批次 31:
  奖励值: 131.9274
  收益率: 0.1664
  距离: 32.7577
  内存使用: 0.6943
  能量使用: 1.0931
  推理时间: 2.7328秒

批次 32:
  奖励值: 133.1156
  收益率: 0.1674
  距离: 34.7849
  内存使用: 0.6646
  能量使用: 1.1192
  推理时间: 2.8395秒

批次 33:
  奖励值: 139.2906
  收益率: 0.1753
  距离: 35.3916
  内存使用: 0.7296
  能量使用: 1.0975
  推理时间: 2.9560秒

批次 34:
  奖励值: 148.5031
  收益率: 0.1831
  距离: 33.5570
  内存使用: 0.7335
  能量使用: 1.1032
  推理时间: 2.9103秒

批次 35:
  奖励值: 133.7779
  收益率: 0.1656
  距离: 34.9397
  内存使用: 0.6714
  能量使用: 1.0330
  推理时间: 2.8036秒

批次 36:
  奖励值: 130.6081
  收益率: 0.1620
  距离: 32.1038
  内存使用: 0.6792
  能量使用: 1.0008
  推理时间: 2.6611秒

批次 37:
  奖励值: 131.1855
  收益率: 0.1664
  距离: 32.0383
  内存使用: 0.6820
  能量使用: 1.1086
  推理时间: 2.6491秒

批次 38:
  奖励值: 135.0663
  收益率: 0.1727
  距离: 32.9816
  内存使用: 0.7570
  能量使用: 1.1288
  推理时间: 2.6607秒

批次 39:
  奖励值: 119.6209
  收益率: 0.1511
  距离: 29.5800
  内存使用: 0.6147
  能量使用: 0.9634
  推理时间: 2.5205秒

批次 40:
  奖励值: 131.6513
  收益率: 0.1633
  距离: 36.0207
  内存使用: 0.6664
  能量使用: 1.0102
  推理时间: 2.8127秒

批次 41:
  奖励值: 150.3742
  收益率: 0.1875
  距离: 37.2545
  内存使用: 0.7940
  能量使用: 1.2029
  推理时间: 2.9674秒

批次 42:
  奖励值: 130.0287
  收益率: 0.1636
  距离: 34.9304
  内存使用: 0.6773
  能量使用: 0.9348
  推理时间: 2.7812秒

批次 43:
  奖励值: 130.6575
  收益率: 0.1610
  距离: 30.3748
  内存使用: 0.6530
  能量使用: 1.0570
  推理时间: 2.6258秒

批次 44:
  奖励值: 147.9374
  收益率: 0.1846
  距离: 35.5644
  内存使用: 0.7782
  能量使用: 1.1825
  推理时间: 2.9886秒

批次 45:
  奖励值: 127.9885
  收益率: 0.1592
  距离: 30.7521
  内存使用: 0.6933
  能量使用: 0.9775
  推理时间: 2.4952秒

批次 46:
  奖励值: 123.6386
  收益率: 0.1600
  距离: 31.9660
  内存使用: 0.6783
  能量使用: 0.8784
  推理时间: 2.4873秒

批次 47:
  奖励值: 130.5749
  收益率: 0.1599
  距离: 28.3724
  内存使用: 0.6447
  能量使用: 1.0331
  推理时间: 2.7224秒

批次 48:
  奖励值: 157.9750
  收益率: 0.1945
  距离: 34.5824
  内存使用: 0.8582
  能量使用: 1.2093
  推理时间: 3.0474秒

批次 49:
  奖励值: 122.8921
  收益率: 0.1576
  距离: 33.5440
  内存使用: 0.9000
  能量使用: 0.9718
  推理时间: 2.5309秒

批次 50:
  奖励值: 123.5328
  收益率: 0.1543
  距离: 30.6698
  内存使用: 0.8998
  能量使用: 0.9648
  推理时间: 2.9427秒

批次 51:
  奖励值: 130.9565
  收益率: 0.1678
  距离: 35.1452
  内存使用: 0.8999
  能量使用: 1.0419
  推理时间: 2.6644秒

批次 52:
  奖励值: 142.2020
  收益率: 0.1768
  距离: 37.1324
  内存使用: 0.7814
  能量使用: 1.0822
  推理时间: 2.9868秒

批次 53:
  奖励值: 148.7066
  收益率: 0.1865
  距离: 37.6449
  内存使用: 0.7738
  能量使用: 1.1680
  推理时间: 2.9941秒

批次 54:
  奖励值: 133.7235
  收益率: 0.1685
  距离: 32.1521
  内存使用: 0.6949
  能量使用: 1.1060
  推理时间: 2.6390秒

批次 55:
  奖励值: 138.0826
  收益率: 0.1753
  距离: 37.0461
  内存使用: 0.7228
  能量使用: 1.0659
  推理时间: 2.8065秒

批次 56:
  奖励值: 148.3850
  收益率: 0.1855
  距离: 34.8258
  内存使用: 0.8311
  能量使用: 1.1732
  推理时间: 2.9944秒

批次 57:
  奖励值: 136.5721
  收益率: 0.1707
  距离: 33.6749
  内存使用: 0.7063
  能量使用: 1.0745
  推理时间: 2.8640秒

批次 58:
  奖励值: 164.5969
  收益率: 0.2060
  距离: 40.5614
  内存使用: 0.8990
  能量使用: 1.3457
  推理时间: 3.3999秒

批次 59:
  奖励值: 137.3349
  收益率: 0.1736
  距离: 35.1531
  内存使用: 0.7506
  能量使用: 1.0264
  推理时间: 2.9932秒

批次 60:
  奖励值: 123.0796
  收益率: 0.1601
  距离: 35.1554
  内存使用: 0.8999
  能量使用: 0.9854
  推理时间: 2.5415秒

批次 61:
  奖励值: 130.2796
  收益率: 0.1624
  距离: 32.5734
  内存使用: 0.6489
  能量使用: 1.0185
  推理时间: 2.7493秒

批次 62:
  奖励值: 137.4756
  收益率: 0.1689
  距离: 31.3227
  内存使用: 0.6490
  能量使用: 1.0771
  推理时间: 2.8924秒

批次 63:
  奖励值: 138.0792
  收益率: 0.1736
  距离: 35.9199
  内存使用: 0.6855
  能量使用: 1.0362
  推理时间: 2.8571秒

批次 64:
  奖励值: 133.1259
  收益率: 0.1666
  距离: 30.4990
  内存使用: 0.7175
  能量使用: 0.9318
  推理时间: 2.7649秒

批次 65:
  奖励值: 140.5549
  收益率: 0.1725
  距离: 37.3959
  内存使用: 0.7579
  能量使用: 1.1363
  推理时间: 2.9761秒

批次 66:
  奖励值: 132.9628
  收益率: 0.1664
  距离: 29.4297
  内存使用: 0.6836
  能量使用: 0.9610
  推理时间: 2.7535秒

批次 67:
  奖励值: 130.7697
  收益率: 0.1636
  距离: 31.9070
  内存使用: 0.7175
  能量使用: 1.0360
  推理时间: 2.7826秒

批次 68:
  奖励值: 137.7686
  收益率: 0.1703
  距离: 30.8690
  内存使用: 0.7201
  能量使用: 1.0737
  推理时间: 2.9157秒

批次 69:
  奖励值: 153.4113
  收益率: 0.1935
  距离: 37.4133
  内存使用: 0.8122
  能量使用: 1.1519
  推理时间: 3.2150秒

批次 70:
  奖励值: 143.8445
  收益率: 0.1783
  距离: 36.5200
  内存使用: 0.7112
  能量使用: 1.0796
  推理时间: 3.0000秒

批次 71:
  奖励值: 140.6760
  收益率: 0.1794
  距离: 42.3585
  内存使用: 0.7793
  能量使用: 1.1100
  推理时间: 2.9675秒

批次 72:
  奖励值: 143.8107
  收益率: 0.1761
  距离: 32.1671
  内存使用: 0.7204
  能量使用: 1.0911
  推理时间: 2.9675秒

批次 73:
  奖励值: 158.8962
  收益率: 0.1987
  距离: 40.1829
  内存使用: 0.8154
  能量使用: 1.2091
  推理时间: 3.3166秒

批次 74:
  奖励值: 133.3497
  收益率: 0.1663
  距离: 34.1472
  内存使用: 0.7359
  能量使用: 1.0175
  推理时间: 2.8229秒

批次 75:
  奖励值: 130.6125
  收益率: 0.1618
  距离: 30.1156
  内存使用: 0.6994
  能量使用: 0.9599
  推理时间: 2.8018秒

批次 76:
  奖励值: 132.2238
  收益率: 0.1653
  距离: 31.7427
  内存使用: 0.7064
  能量使用: 1.1032
  推理时间: 2.8369秒

批次 77:
  奖励值: 132.0499
  收益率: 0.1649
  距离: 30.9205
  内存使用: 0.7963
  能量使用: 0.9833
  推理时间: 2.6886秒

批次 78:
  奖励值: 134.1294
  收益率: 0.1709
  距离: 33.4652
  内存使用: 0.6429
  能量使用: 1.0331
  推理时间: 2.8776秒

批次 79:
  奖励值: 142.1034
  收益率: 0.1798
  距离: 31.5378
  内存使用: 0.7621
  能量使用: 1.0933
  推理时间: 2.9593秒

批次 80:
  奖励值: 130.5361
  收益率: 0.1663
  距离: 37.3023
  内存使用: 0.6368
  能量使用: 0.9887
  推理时间: 2.7840秒

批次 81:
  奖励值: 153.2000
  收益率: 0.1935
  距离: 39.4305
  内存使用: 0.8382
  能量使用: 1.1313
  推理时间: 3.2241秒

批次 82:
  奖励值: 141.9144
  收益率: 0.1783
  距离: 35.2550
  内存使用: 0.7688
  能量使用: 1.1474
  推理时间: 3.3988秒

批次 83:
  奖励值: 126.9293
  收益率: 0.1602
  距离: 34.4106
  内存使用: 0.6900
  能量使用: 1.0409
  推理时间: 2.7154秒

批次 84:
  奖励值: 159.5114
  收益率: 0.2000
  距离: 38.9187
  内存使用: 0.8592
  能量使用: 1.1993
  推理时间: 3.3087秒

批次 85:
  奖励值: 140.0882
  收益率: 0.1759
  距离: 33.7457
  内存使用: 0.7724
  能量使用: 1.1055
  推理时间: 2.6410秒

批次 86:
  奖励值: 130.9643
  收益率: 0.1620
  距离: 32.6334
  内存使用: 0.6239
  能量使用: 1.0140
  推理时间: 2.7486秒

批次 87:
  奖励值: 157.3207
  收益率: 0.1978
  距离: 37.7835
  内存使用: 0.7897
  能量使用: 1.1854
  推理时间: 3.3317秒

批次 88:
  奖励值: 144.6127
  收益率: 0.1794
  距离: 38.9106
  内存使用: 0.7642
  能量使用: 1.0996
  推理时间: 3.1087秒

批次 89:
  奖励值: 149.9324
  收益率: 0.1846
  距离: 38.5168
  内存使用: 0.8056
  能量使用: 1.2129
  推理时间: 3.0881秒

批次 90:
  奖励值: 132.8716
  收益率: 0.1678
  距离: 36.1740
  内存使用: 0.6332
  能量使用: 1.0495
  推理时间: 2.7536秒

批次 91:
  奖励值: 125.7569
  收益率: 0.1570
  距离: 31.5395
  内存使用: 0.8999
  能量使用: 1.0467
  推理时间: 2.6865秒

批次 92:
  奖励值: 138.6160
  收益率: 0.1707
  距离: 35.0520
  内存使用: 0.7008
  能量使用: 1.0153
  推理时间: 2.7908秒

批次 93:
  奖励值: 144.9741
  收益率: 0.1774
  距离: 32.3300
  内存使用: 0.7889
  能量使用: 1.1329
  推理时间: 3.0717秒

批次 94:
  奖励值: 140.6483
  收益率: 0.1786
  距离: 35.3392
  内存使用: 0.7271
  能量使用: 1.0370
  推理时间: 2.9245秒

批次 95:
  奖励值: 133.0912
  收益率: 0.1689
  距离: 34.3194
  内存使用: 0.6544
  能量使用: 1.0439
  推理时间: 2.8913秒

批次 96:
  奖励值: 128.9280
  收益率: 0.1621
  距离: 30.9961
  内存使用: 0.9000
  能量使用: 1.0479
  推理时间: 2.8354秒

批次 97:
  奖励值: 146.2141
  收益率: 0.1862
  距离: 40.3185
  内存使用: 0.8699
  能量使用: 1.1834
  推理时间: 2.9905秒

批次 98:
  奖励值: 154.3865
  收益率: 0.1924
  距离: 38.0890
  内存使用: 0.8622
  能量使用: 1.2758
  推理时间: 3.0181秒

批次 99:
  奖励值: 152.9798
  收益率: 0.1898
  距离: 35.3168
  内存使用: 0.7299
  能量使用: 1.1936
  推理时间: 3.2161秒

批次 100:
  奖励值: 155.6360
  收益率: 0.1960
  距离: 38.5209
  内存使用: 0.8080
  能量使用: 1.1991
  推理时间: 3.1756秒


==================== 总结 ====================
平均收益率: 0.1725
平均能量使用: 1.0708
平均推理时间: 2.8652秒
