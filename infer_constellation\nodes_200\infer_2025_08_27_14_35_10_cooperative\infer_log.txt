推理数据数量: 100
每个序列任务数量: 200
星座卫星数量: 3
星座模式: cooperative
模型: gpn+indrnn
种子: 12348
内存总量: 0.3
电量总量: 5
检查点路径: constellation_smp/constellation_smp100/multi_constellation_comparison_2025_08_25_17_02_29/constellation_gpnindrnn_cooperative_2025_08_25_17_02_29

批次 1:
  奖励值: 51.6653
  收益率: 0.6779
  距离: 17.2023
  内存使用: 0.2085
  能量使用: 0.4586
  推理时间: 1.3350秒

批次 2:
  奖励值: 53.7950
  收益率: 0.6731
  距离: 16.5849
  内存使用: 0.1792
  能量使用: 0.4459
  推理时间: 1.3642秒

批次 3:
  奖励值: 51.9106
  收益率: 0.6802
  距离: 16.3787
  内存使用: 0.1693
  能量使用: 0.4307
  推理时间: 1.2947秒

批次 4:
  奖励值: 56.3799
  收益率: 0.6818
  距离: 15.5400
  内存使用: 0.2189
  能量使用: 0.4812
  推理时间: 1.4031秒

批次 5:
  奖励值: 51.8530
  收益率: 0.6741
  距离: 15.7373
  内存使用: 0.5143
  能量使用: 0.4423
  推理时间: 1.3325秒

批次 6:
  奖励值: 62.8999
  收益率: 0.7451
  距离: 17.2502
  内存使用: 0.2338
  能量使用: 0.5163
  推理时间: 1.4552秒

批次 7:
  奖励值: 59.6935
  收益率: 0.6886
  距离: 16.2036
  内存使用: 0.1408
  能量使用: 0.4687
  推理时间: 1.3169秒

批次 8:
  奖励值: 57.5564
  收益率: 0.7097
  距离: 17.1628
  内存使用: 0.2284
  能量使用: 0.4960
  推理时间: 1.4680秒

批次 9:
  奖励值: 53.4976
  收益率: 0.6547
  距离: 12.9895
  内存使用: 0.1456
  能量使用: 0.4698
  推理时间: 1.2755秒

批次 10:
  奖励值: 59.5822
  收益率: 0.7031
  距离: 16.5209
  内存使用: 0.2671
  能量使用: 0.4476
  推理时间: 1.3947秒

批次 11:
  奖励值: 51.1461
  收益率: 0.6327
  距离: 13.9849
  内存使用: 0.1138
  能量使用: 0.4092
  推理时间: 1.2504秒

批次 12:
  奖励值: 56.2253
  收益率: 0.7057
  距离: 15.8102
  内存使用: 0.2355
  能量使用: 0.5038
  推理时间: 1.3593秒

批次 13:
  奖励值: 58.2252
  收益率: 0.7382
  距离: 17.6354
  内存使用: 0.2121
  能量使用: 0.5020
  推理时间: 1.5294秒

批次 14:
  奖励值: 64.4340
  收益率: 0.7446
  距离: 15.6453
  内存使用: 0.2068
  能量使用: 0.4800
  推理时间: 1.4574秒

批次 15:
  奖励值: 56.5155
  收益率: 0.7183
  距离: 18.8581
  内存使用: 0.2287
  能量使用: 0.4822
  推理时间: 1.4318秒

批次 16:
  奖励值: 56.1745
  收益率: 0.6855
  距离: 15.6019
  内存使用: 0.1752
  能量使用: 0.4738
  推理时间: 1.3257秒

批次 17:
  奖励值: 60.9406
  收益率: 0.7242
  距离: 16.5871
  内存使用: 0.2250
  能量使用: 0.5068
  推理时间: 1.5952秒

批次 18:
  奖励值: 57.4163
  收益率: 0.6837
  距离: 15.8015
  内存使用: 0.2405
  能量使用: 0.5255
  推理时间: 1.4581秒

批次 19:
  奖励值: 57.4713
  收益率: 0.7135
  距离: 13.3714
  内存使用: 0.1638
  能量使用: 0.5222
  推理时间: 1.4197秒

批次 20:
  奖励值: 54.9322
  收益率: 0.6839
  距离: 16.6572
  内存使用: 0.1576
  能量使用: 0.5209
  推理时间: 1.3011秒

批次 21:
  奖励值: 62.2378
  收益率: 0.7397
  距离: 17.9539
  内存使用: 0.2585
  能量使用: 0.5119
  推理时间: 1.4340秒

批次 22:
  奖励值: 53.6059
  收益率: 0.6695
  距离: 16.2210
  内存使用: 0.1813
  能量使用: 0.3860
  推理时间: 1.2996秒

批次 23:
  奖励值: 58.6340
  收益率: 0.7234
  距离: 16.3606
  内存使用: 0.2503
  能量使用: 0.5210
  推理时间: 1.5052秒

批次 24:
  奖励值: 54.1540
  收益率: 0.6723
  距离: 17.4176
  内存使用: 0.2053
  能量使用: 0.4691
  推理时间: 1.4227秒

批次 25:
  奖励值: 54.9011
  收益率: 0.7067
  距离: 17.6600
  内存使用: 0.2267
  能量使用: 0.5111
  推理时间: 1.2911秒

批次 26:
  奖励值: 57.3400
  收益率: 0.7178
  距离: 18.4228
  内存使用: 0.1875
  能量使用: 0.5098
  推理时间: 1.3781秒

批次 27:
  奖励值: 55.8920
  收益率: 0.6986
  距离: 15.8694
  内存使用: 0.2317
  能量使用: 0.5149
  推理时间: 1.3713秒

批次 28:
  奖励值: 52.7206
  收益率: 0.6813
  距离: 15.2422
  内存使用: 0.1777
  能量使用: 0.5390
  推理时间: 1.2964秒

批次 29:
  奖励值: 56.0839
  收益率: 0.6578
  距离: 16.3963
  内存使用: 0.1714
  能量使用: 0.4644
  推理时间: 1.2784秒

批次 30:
  奖励值: 56.2055
  收益率: 0.7033
  距离: 13.5227
  内存使用: 0.2070
  能量使用: 0.4593
  推理时间: 1.3605秒

批次 31:
  奖励值: 53.2545
  收益率: 0.6784
  距离: 16.0478
  内存使用: 0.1490
  能量使用: 0.4942
  推理时间: 1.3932秒

批次 32:
  奖励值: 54.4633
  收益率: 0.7121
  距离: 18.9770
  内存使用: 0.1956
  能量使用: 0.5008
  推理时间: 1.4811秒

批次 33:
  奖励值: 55.2897
  收益率: 0.6864
  距离: 13.9892
  内存使用: 0.1499
  能量使用: 0.4816
  推理时间: 1.4323秒

批次 34:
  奖励值: 59.2245
  收益率: 0.7451
  距离: 17.0465
  内存使用: 0.2510
  能量使用: 0.4536
  推理时间: 1.3295秒

批次 35:
  奖励值: 52.5206
  收益率: 0.6597
  距离: 14.1396
  内存使用: 0.1281
  能量使用: 0.4912
  推理时间: 1.2862秒

批次 36:
  奖励值: 55.5392
  收益率: 0.7217
  距离: 15.8012
  内存使用: 0.1929
  能量使用: 0.5276
  推理时间: 1.4785秒

批次 37:
  奖励值: 55.9027
  收益率: 0.7074
  距离: 16.1508
  内存使用: 0.1257
  能量使用: 0.5181
  推理时间: 1.4653秒

批次 38:
  奖励值: 55.7227
  收益率: 0.6951
  距离: 15.0828
  内存使用: 0.1797
  能量使用: 0.4921
  推理时间: 1.1465秒

批次 39:
  奖励值: 52.6494
  收益率: 0.6788
  距离: 17.0295
  内存使用: 0.1840
  能量使用: 0.4878
  推理时间: 1.3227秒

批次 40:
  奖励值: 53.4757
  收益率: 0.6916
  距离: 16.1735
  内存使用: 0.1730
  能量使用: 0.5021
  推理时间: 1.3340秒

批次 41:
  奖励值: 53.2703
  收益率: 0.6901
  距离: 14.5025
  内存使用: 0.0913
  能量使用: 0.4655
  推理时间: 1.3082秒

批次 42:
  奖励值: 59.7488
  收益率: 0.7341
  距离: 15.5513
  内存使用: 0.2517
  能量使用: 0.4994
  推理时间: 1.4175秒

批次 43:
  奖励值: 57.5201
  收益率: 0.7107
  距离: 15.4912
  内存使用: 0.1868
  能量使用: 0.4817
  推理时间: 1.3361秒

批次 44:
  奖励值: 54.8428
  收益率: 0.7025
  距离: 17.6886
  内存使用: 0.1842
  能量使用: 0.4417
  推理时间: 1.3851秒

批次 45:
  奖励值: 57.3110
  收益率: 0.7185
  距离: 15.8638
  内存使用: 0.2280
  能量使用: 0.4861
  推理时间: 1.3772秒

批次 46:
  奖励值: 60.0203
  收益率: 0.7348
  距离: 16.6672
  内存使用: 0.1765
  能量使用: 0.5156
  推理时间: 1.4438秒

批次 47:
  奖励值: 51.2776
  收益率: 0.6704
  距离: 17.6204
  内存使用: 0.1557
  能量使用: 0.4687
  推理时间: 1.3258秒

批次 48:
  奖励值: 52.1008
  收益率: 0.6900
  距离: 17.5467
  内存使用: 0.1463
  能量使用: 0.5054
  推理时间: 1.3327秒

批次 49:
  奖励值: 53.2540
  收益率: 0.6893
  距离: 16.5980
  内存使用: 0.1744
  能量使用: 0.5197
  推理时间: 1.3591秒

批次 50:
  奖励值: 52.8146
  收益率: 0.6689
  距离: 16.0975
  内存使用: 0.1948
  能量使用: 0.4785
  推理时间: 1.2807秒

批次 51:
  奖励值: 55.1256
  收益率: 0.6624
  距离: 12.2968
  内存使用: 0.1654
  能量使用: 0.4867
  推理时间: 1.3282秒

批次 52:
  奖励值: 54.7666
  收益率: 0.6766
  距离: 16.9704
  内存使用: 0.2172
  能量使用: 0.4793
  推理时间: 1.3125秒

批次 53:
  奖励值: 65.5451
  收益率: 0.7274
  距离: 14.2784
  内存使用: 0.2589
  能量使用: 0.5725
  推理时间: 1.4670秒

批次 54:
  奖励值: 53.9856
  收益率: 0.6817
  距离: 15.4904
  内存使用: 0.2101
  能量使用: 0.5284
  推理时间: 1.3722秒

批次 55:
  奖励值: 54.8911
  收益率: 0.7106
  距离: 16.7518
  内存使用: 0.2021
  能量使用: 0.4638
  推理时间: 1.4005秒

批次 56:
  奖励值: 62.0031
  收益率: 0.7371
  距离: 18.2098
  内存使用: 0.2536
  能量使用: 0.5303
  推理时间: 1.4898秒

批次 57:
  奖励值: 54.5886
  收益率: 0.6878
  距离: 15.7572
  内存使用: 0.1713
  能量使用: 0.4944
  推理时间: 1.3512秒

批次 58:
  奖励值: 52.0201
  收益率: 0.6558
  距离: 16.5160
  内存使用: 0.1498
  能量使用: 0.4939
  推理时间: 1.2808秒

批次 59:
  奖励值: 56.4409
  收益率: 0.6971
  距离: 13.1044
  内存使用: 0.1401
  能量使用: 0.4736
  推理时间: 1.3255秒

批次 60:
  奖励值: 57.3584
  收益率: 0.7163
  距离: 16.4555
  内存使用: 0.1680
  能量使用: 0.4597
  推理时间: 1.3395秒

批次 61:
  奖励值: 57.0725
  收益率: 0.6972
  距离: 14.5558
  内存使用: 0.2024
  能量使用: 0.4968
  推理时间: 1.4127秒

批次 62:
  奖励值: 54.2847
  收益率: 0.6724
  距离: 15.1620
  内存使用: 0.1870
  能量使用: 0.4816
  推理时间: 1.3181秒

批次 63:
  奖励值: 50.6276
  收益率: 0.6770
  距离: 15.4766
  内存使用: 0.1777
  能量使用: 0.4904
  推理时间: 1.2893秒

批次 64:
  奖励值: 53.7082
  收益率: 0.6783
  距离: 16.3371
  内存使用: 0.5339
  能量使用: 0.5126
  推理时间: 1.3874秒

批次 65:
  奖励值: 56.6061
  收益率: 0.6908
  距离: 17.9541
  内存使用: 0.1923
  能量使用: 0.4414
  推理时间: 1.3904秒

批次 66:
  奖励值: 55.2763
  收益率: 0.6861
  距离: 15.9868
  内存使用: 0.2036
  能量使用: 0.4570
  推理时间: 1.3333秒

批次 67:
  奖励值: 56.1140
  收益率: 0.7148
  距离: 17.9087
  内存使用: 0.1789
  能量使用: 0.4844
  推理时间: 1.4319秒

批次 68:
  奖励值: 51.2720
  收益率: 0.6520
  距离: 15.6118
  内存使用: 0.1550
  能量使用: 0.4893
  推理时间: 1.2501秒

批次 69:
  奖励值: 58.2204
  收益率: 0.7202
  距离: 15.6125
  内存使用: 0.1883
  能量使用: 0.5584
  推理时间: 1.4318秒

批次 70:
  奖励值: 55.2773
  收益率: 0.7039
  距离: 18.5533
  内存使用: 0.1980
  能量使用: 0.4942
  推理时间: 1.4574秒

批次 71:
  奖励值: 55.0840
  收益率: 0.6870
  距离: 15.1937
  内存使用: 0.1522
  能量使用: 0.4862
  推理时间: 1.3094秒

批次 72:
  奖励值: 51.7883
  收益率: 0.6902
  距离: 16.9342
  内存使用: 0.1969
  能量使用: 0.4924
  推理时间: 1.4241秒

批次 73:
  奖励值: 57.0695
  收益率: 0.7185
  距离: 17.3377
  内存使用: 0.1817
  能量使用: 0.5415
  推理时间: 1.4436秒

批次 74:
  奖励值: 56.0503
  收益率: 0.6983
  距离: 15.4140
  内存使用: 0.1671
  能量使用: 0.5183
  推理时间: 1.3694秒

批次 75:
  奖励值: 53.6805
  收益率: 0.6524
  距离: 13.9419
  内存使用: 0.2111
  能量使用: 0.4860
  推理时间: 1.3880秒

批次 76:
  奖励值: 52.5146
  收益率: 0.6539
  距离: 14.2416
  内存使用: 0.1113
  能量使用: 0.4179
  推理时间: 1.2473秒

批次 77:
  奖励值: 56.9791
  收益率: 0.7189
  距离: 15.7000
  内存使用: 0.2067
  能量使用: 0.5352
  推理时间: 1.4726秒

批次 78:
  奖励值: 52.9733
  收益率: 0.6794
  距离: 14.6033
  内存使用: 0.1544
  能量使用: 0.4958
  推理时间: 1.3097秒

批次 79:
  奖励值: 50.8418
  收益率: 0.6410
  距离: 12.6941
  内存使用: 0.1639
  能量使用: 0.4583
  推理时间: 1.2298秒

批次 80:
  奖励值: 57.1332
  收益率: 0.6743
  距离: 15.0601
  内存使用: 0.1840
  能量使用: 0.4895
  推理时间: 1.3691秒

批次 81:
  奖励值: 52.1032
  收益率: 0.6800
  距离: 14.2867
  内存使用: 0.4744
  能量使用: 0.4325
  推理时间: 1.3308秒

批次 82:
  奖励值: 56.7049
  收益率: 0.7238
  距离: 21.0540
  内存使用: 0.2345
  能量使用: 0.5017
  推理时间: 1.4322秒

批次 83:
  奖励值: 60.7331
  收益率: 0.7309
  距离: 15.1788
  内存使用: 0.2160
  能量使用: 0.5391
  推理时间: 1.4289秒

批次 84:
  奖励值: 54.8939
  收益率: 0.7033
  距离: 16.1941
  内存使用: 0.1378
  能量使用: 0.4803
  推理时间: 1.3119秒

批次 85:
  奖励值: 55.9990
  收益率: 0.7067
  距离: 13.1252
  内存使用: 0.1629
  能量使用: 0.5138
  推理时间: 1.3506秒

批次 86:
  奖励值: 45.2557
  收益率: 0.6298
  距离: 14.6866
  内存使用: 0.1790
  能量使用: 0.4229
  推理时间: 1.2307秒

批次 87:
  奖励值: 57.4998
  收益率: 0.7037
  距离: 15.9375
  内存使用: 0.1863
  能量使用: 0.4767
  推理时间: 1.3880秒

批次 88:
  奖励值: 55.7928
  收益率: 0.6884
  距离: 16.3154
  内存使用: 0.2060
  能量使用: 0.4930
  推理时间: 1.3573秒

批次 89:
  奖励值: 56.7752
  收益率: 0.7120
  距离: 16.6080
  内存使用: 0.2288
  能量使用: 0.6128
  推理时间: 1.4206秒

批次 90:
  奖励值: 58.2463
  收益率: 0.7104
  距离: 18.1890
  内存使用: 0.1870
  能量使用: 0.5315
  推理时间: 1.4293秒

批次 91:
  奖励值: 53.0095
  收益率: 0.6670
  距离: 12.8187
  内存使用: 0.1179
  能量使用: 0.4444
  推理时间: 1.2799秒

批次 92:
  奖励值: 53.8380
  收益率: 0.6795
  距离: 15.5458
  内存使用: 0.1692
  能量使用: 0.4090
  推理时间: 1.2695秒

批次 93:
  奖励值: 53.2282
  收益率: 0.6808
  距离: 15.2418
  内存使用: 0.2032
  能量使用: 0.4986
  推理时间: 1.3662秒

批次 94:
  奖励值: 57.7782
  收益率: 0.7351
  距离: 19.1153
  内存使用: 0.2391
  能量使用: 0.4893
  推理时间: 1.3774秒

批次 95:
  奖励值: 60.3982
  收益率: 0.7362
  距离: 17.4530
  内存使用: 0.2109
  能量使用: 0.5398
  推理时间: 1.4880秒

批次 96:
  奖励值: 59.8202
  收益率: 0.7329
  距离: 14.5549
  内存使用: 0.2425
  能量使用: 0.5623
  推理时间: 1.4565秒

批次 97:
  奖励值: 53.4967
  收益率: 0.6461
  距离: 15.7361
  内存使用: 0.1886
  能量使用: 0.4818
  推理时间: 1.3257秒

批次 98:
  奖励值: 52.6610
  收益率: 0.6685
  距离: 13.6280
  内存使用: 0.0806
  能量使用: 0.3696
  推理时间: 1.3338秒

批次 99:
  奖励值: 57.6090
  收益率: 0.6918
  距离: 17.9367
  内存使用: 0.1821
  能量使用: 0.4632
  推理时间: 1.3782秒

批次 100:
  奖励值: 51.0438
  收益率: 0.6765
  距离: 16.8985
  内存使用: 0.1413
  能量使用: 0.4726
  推理时间: 1.3400秒


==================== 总结 ====================
平均收益率: 0.6944
平均能量使用: 0.4883
平均推理时间: 1.3687秒
