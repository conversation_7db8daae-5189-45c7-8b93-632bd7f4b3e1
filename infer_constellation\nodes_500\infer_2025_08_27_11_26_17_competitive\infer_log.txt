推理数据数量: 100
每个序列任务数量: 500
星座卫星数量: 3
星座模式: competitive
模型: gpn+indrnn
种子: 12348
内存总量: 0.3
电量总量: 5
检查点路径: constellation_smp/constellation_smp100/multi_constellation_comparison_2025_08_25_17_02_29/constellation_gpnindrnn_competitive_2025_08_26_05_01_59

批次 1:
  奖励值: 103.7773
  收益率: 0.4989
  距离: 24.2113
  内存使用: 0.4863
  能量使用: 0.7477
  推理时间: 2.3004秒

批次 2:
  奖励值: 99.1505
  收益率: 0.4838
  距离: 24.4562
  内存使用: 0.5214
  能量使用: 0.7215
  推理时间: 2.0835秒

批次 3:
  奖励值: 105.6098
  收益率: 0.5232
  距离: 26.0648
  内存使用: 0.5182
  能量使用: 0.7974
  推理时间: 2.8074秒

批次 4:
  奖励值: 96.4780
  收益率: 0.4866
  距离: 27.7657
  内存使用: 0.4989
  能量使用: 0.7794
  推理时间: 2.1029秒

批次 5:
  奖励值: 88.4225
  收益率: 0.4517
  距离: 24.1115
  内存使用: 0.4729
  能量使用: 0.7705
  推理时间: 1.9600秒

批次 6:
  奖励值: 88.0252
  收益率: 0.4515
  距离: 23.0900
  内存使用: 0.3818
  能量使用: 0.6778
  推理时间: 1.9074秒

批次 7:
  奖励值: 97.5349
  收益率: 0.4924
  距离: 24.7784
  内存使用: 0.5433
  能量使用: 0.8085
  推理时间: 2.1110秒

批次 8:
  奖励值: 99.9179
  收益率: 0.5108
  距离: 25.2700
  内存使用: 0.5415
  能量使用: 0.7528
  推理时间: 2.5106秒

批次 9:
  奖励值: 102.8580
  收益率: 0.5340
  距离: 25.1713
  内存使用: 0.6096
  能量使用: 0.9032
  推理时间: 2.2887秒

批次 10:
  奖励值: 105.9347
  收益率: 0.5195
  距离: 26.5353
  内存使用: 0.5628
  能量使用: 0.8326
  推理时间: 2.4065秒

批次 11:
  奖励值: 102.4618
  收益率: 0.5011
  距离: 27.1294
  内存使用: 0.5377
  能量使用: 0.8093
  推理时间: 2.5113秒

批次 12:
  奖励值: 100.3232
  收益率: 0.5048
  距离: 28.1734
  内存使用: 0.5833
  能量使用: 0.7969
  推理时间: 2.5590秒

批次 13:
  奖励值: 89.0470
  收益率: 0.4619
  距离: 23.2118
  内存使用: 0.3709
  能量使用: 0.7234
  推理时间: 2.2449秒

批次 14:
  奖励值: 100.6747
  收益率: 0.5037
  距离: 27.8870
  内存使用: 0.5705
  能量使用: 0.7931
  推理时间: 2.5458秒

批次 15:
  奖励值: 103.6884
  收益率: 0.5129
  距离: 25.3404
  内存使用: 0.4546
  能量使用: 0.8280
  推理时间: 2.2726秒

批次 16:
  奖励值: 98.5340
  收益率: 0.5000
  距离: 26.0083
  内存使用: 0.5026
  能量使用: 0.8210
  推理时间: 2.4195秒

批次 17:
  奖励值: 104.7870
  收益率: 0.5197
  距离: 26.6917
  内存使用: 0.5295
  能量使用: 0.8047
  推理时间: 2.5913秒

批次 18:
  奖励值: 87.9278
  收益率: 0.4553
  距离: 21.5957
  内存使用: 0.4371
  能量使用: 0.7115
  推理时间: 2.3785秒

批次 19:
  奖励值: 99.5100
  收益率: 0.4925
  距离: 28.7976
  内存使用: 0.5974
  能量使用: 0.7849
  推理时间: 2.3077秒

批次 20:
  奖励值: 101.3569
  收益率: 0.5106
  距离: 26.3249
  内存使用: 0.5634
  能量使用: 0.7979
  推理时间: 2.5728秒

批次 21:
  奖励值: 98.6491
  收益率: 0.4967
  距离: 26.3130
  内存使用: 0.5231
  能量使用: 0.8657
  推理时间: 2.5092秒

批次 22:
  奖励值: 92.2455
  收益率: 0.4816
  距离: 26.4487
  内存使用: 0.8530
  能量使用: 0.8072
  推理时间: 2.4976秒

批次 23:
  奖励值: 98.2405
  收益率: 0.4969
  距离: 30.1547
  内存使用: 0.5322
  能量使用: 0.8321
  推理时间: 2.5755秒

批次 24:
  奖励值: 88.3622
  收益率: 0.4619
  距离: 23.4316
  内存使用: 0.4134
  能量使用: 0.8307
  推理时间: 2.1098秒

批次 25:
  奖励值: 97.6086
  收益率: 0.4900
  距离: 28.6407
  内存使用: 0.5517
  能量使用: 0.7903
  推理时间: 2.1722秒

批次 26:
  奖励值: 97.3658
  收益率: 0.4880
  距离: 26.7403
  内存使用: 0.5801
  能量使用: 0.7481
  推理时间: 2.4119秒

批次 27:
  奖励值: 99.6922
  收益率: 0.4976
  距离: 24.3989
  内存使用: 0.5556
  能量使用: 0.8611
  推理时间: 2.3216秒

批次 28:
  奖励值: 100.6550
  收益率: 0.5033
  距离: 25.9916
  内存使用: 0.4799
  能量使用: 0.8184
  推理时间: 2.3113秒

批次 29:
  奖励值: 98.3613
  收益率: 0.4900
  距离: 23.4305
  内存使用: 0.4432
  能量使用: 0.8037
  推理时间: 2.4391秒

批次 30:
  奖励值: 100.1895
  收益率: 0.5115
  距离: 29.2550
  内存使用: 0.4992
  能量使用: 0.8667
  推理时间: 2.5827秒

批次 31:
  奖励值: 102.7899
  收益率: 0.5161
  距离: 27.4155
  内存使用: 0.5441
  能量使用: 0.8607
  推理时间: 2.7515秒

批次 32:
  奖励值: 101.2620
  收益率: 0.5031
  距离: 24.3255
  内存使用: 0.5494
  能量使用: 0.8011
  推理时间: 2.6558秒

批次 33:
  奖励值: 95.2943
  收益率: 0.4761
  距离: 25.5898
  内存使用: 0.4306
  能量使用: 0.7910
  推理时间: 2.2355秒

批次 34:
  奖励值: 95.9686
  收益率: 0.4896
  距离: 27.1720
  内存使用: 0.5115
  能量使用: 0.7794
  推理时间: 2.2404秒

批次 35:
  奖励值: 96.3717
  收益率: 0.5038
  距离: 28.5554
  内存使用: 0.4840
  能量使用: 0.8171
  推理时间: 2.4806秒

批次 36:
  奖励值: 101.2555
  收益率: 0.5061
  距离: 28.7009
  内存使用: 0.5127
  能量使用: 0.8754
  推理时间: 2.5570秒

批次 37:
  奖励值: 101.7050
  收益率: 0.5108
  距离: 26.2012
  内存使用: 0.5647
  能量使用: 0.8242
  推理时间: 2.6247秒

批次 38:
  奖励值: 94.9211
  收益率: 0.4809
  距离: 28.0076
  内存使用: 0.5393
  能量使用: 0.8110
  推理时间: 2.3098秒

批次 39:
  奖励值: 106.6892
  收益率: 0.5278
  距离: 28.1958
  内存使用: 0.5718
  能量使用: 0.8540
  推理时间: 2.5163秒

批次 40:
  奖励值: 94.1970
  收益率: 0.4977
  距离: 24.8981
  内存使用: 0.5130
  能量使用: 0.7949
  推理时间: 2.3095秒

批次 41:
  奖励值: 101.8681
  收益率: 0.5188
  距离: 29.7094
  内存使用: 0.5625
  能量使用: 0.7919
  推理时间: 2.6142秒

批次 42:
  奖励值: 100.4779
  收益率: 0.4911
  距离: 22.7794
  内存使用: 0.4448
  能量使用: 0.8201
  推理时间: 2.3562秒

批次 43:
  奖励值: 114.6034
  收益率: 0.5594
  距离: 27.2645
  内存使用: 0.5806
  能量使用: 0.9481
  推理时间: 2.8036秒

批次 44:
  奖励值: 102.3043
  收益率: 0.5189
  距离: 27.6827
  内存使用: 0.5357
  能量使用: 0.7731
  推理时间: 2.4269秒

批次 45:
  奖励值: 108.9597
  收益率: 0.5233
  距离: 29.5791
  内存使用: 0.6375
  能量使用: 0.8640
  推理时间: 2.4975秒

批次 46:
  奖励值: 99.9683
  收益率: 0.5007
  距离: 26.7695
  内存使用: 0.4531
  能量使用: 0.8409
  推理时间: 2.5070秒

批次 47:
  奖励值: 99.0771
  收益率: 0.4993
  距离: 22.5914
  内存使用: 0.4702
  能量使用: 0.7842
  推理时间: 2.2860秒

批次 48:
  奖励值: 96.0234
  收益率: 0.4874
  距离: 27.4289
  内存使用: 0.5044
  能量使用: 0.8199
  推理时间: 2.2926秒

批次 49:
  奖励值: 102.0395
  收益率: 0.5063
  距离: 27.9713
  内存使用: 0.5661
  能量使用: 0.7835
  推理时间: 2.3904秒

批次 50:
  奖励值: 101.2980
  收益率: 0.4968
  距离: 26.4191
  内存使用: 0.5404
  能量使用: 0.8444
  推理时间: 2.4027秒

批次 51:
  奖励值: 96.8189
  收益率: 0.4944
  距离: 24.7293
  内存使用: 0.4861
  能量使用: 0.7469
  推理时间: 2.2673秒

批次 52:
  奖励值: 104.1021
  收益率: 0.5301
  距离: 27.8488
  内存使用: 0.5817
  能量使用: 0.9653
  推理时间: 2.5726秒

批次 53:
  奖励值: 95.9177
  收益率: 0.4978
  距离: 30.5815
  内存使用: 0.5195
  能量使用: 0.8635
  推理时间: 2.4643秒

批次 54:
  奖励值: 104.4306
  收益率: 0.5095
  距离: 25.1879
  内存使用: 0.5341
  能量使用: 0.8169
  推理时间: 2.5539秒

批次 55:
  奖励值: 97.6394
  收益率: 0.4896
  距离: 24.9267
  内存使用: 0.4415
  能量使用: 0.7529
  推理时间: 2.2901秒

批次 56:
  奖励值: 97.1687
  收益率: 0.4945
  距离: 29.5847
  内存使用: 0.5018
  能量使用: 0.7762
  推理时间: 2.1967秒

批次 57:
  奖励值: 95.7749
  收益率: 0.4859
  距离: 27.3362
  内存使用: 0.5349
  能量使用: 0.7791
  推理时间: 2.0741秒

批次 58:
  奖励值: 98.7685
  收益率: 0.4848
  距离: 24.1611
  内存使用: 0.5345
  能量使用: 0.7674
  推理时间: 2.1122秒

批次 59:
  奖励值: 93.1269
  收益率: 0.4749
  距离: 25.9484
  内存使用: 0.8056
  能量使用: 0.7922
  推理时间: 2.0176秒

批次 60:
  奖励值: 102.9009
  收益率: 0.5041
  距离: 25.4482
  内存使用: 0.4922
  能量使用: 0.8577
  推理时间: 2.2014秒

批次 61:
  奖励值: 98.7275
  收益率: 0.4958
  距离: 26.6129
  内存使用: 0.5194
  能量使用: 0.8127
  推理时间: 2.1267秒

批次 62:
  奖励值: 100.3427
  收益率: 0.5211
  距离: 28.1387
  内存使用: 0.5148
  能量使用: 0.8611
  推理时间: 2.2387秒

批次 63:
  奖励值: 104.8671
  收益率: 0.5083
  距离: 28.8076
  内存使用: 0.6047
  能量使用: 0.8535
  推理时间: 2.2992秒

批次 64:
  奖励值: 99.2772
  收益率: 0.4952
  距离: 25.2405
  内存使用: 0.4396
  能量使用: 0.7654
  推理时间: 2.1754秒

批次 65:
  奖励值: 98.3666
  收益率: 0.4928
  距离: 26.5445
  内存使用: 0.5494
  能量使用: 0.7731
  推理时间: 2.1424秒

批次 66:
  奖励值: 100.2016
  收益率: 0.5064
  距离: 27.3472
  内存使用: 0.5499
  能量使用: 0.8997
  推理时间: 2.2645秒

批次 67:
  奖励值: 110.8413
  收益率: 0.5493
  距离: 29.5150
  内存使用: 0.6127
  能量使用: 0.9896
  推理时间: 2.4771秒

批次 68:
  奖励值: 100.7425
  收益率: 0.5024
  距离: 28.1592
  内存使用: 0.5504
  能量使用: 0.8054
  推理时间: 2.2203秒

批次 69:
  奖励值: 98.0035
  收益率: 0.5004
  距离: 25.8568
  内存使用: 0.4980
  能量使用: 0.8382
  推理时间: 2.2138秒

批次 70:
  奖励值: 108.2978
  收益率: 0.5303
  距离: 25.3808
  内存使用: 0.5670
  能量使用: 0.8565
  推理时间: 2.3227秒

批次 71:
  奖励值: 93.9684
  收益率: 0.4636
  距离: 23.6200
  内存使用: 0.4998
  能量使用: 0.7433
  推理时间: 2.0740秒

批次 72:
  奖励值: 92.5719
  收益率: 0.4981
  距离: 24.2332
  内存使用: 0.4680
  能量使用: 0.7550
  推理时间: 2.1058秒

批次 73:
  奖励值: 96.8068
  收益率: 0.4898
  距离: 26.6205
  内存使用: 0.5664
  能量使用: 0.7995
  推理时间: 2.2019秒

批次 74:
  奖励值: 88.4230
  收益率: 0.4405
  距离: 24.7114
  内存使用: 0.7669
  能量使用: 0.6757
  推理时间: 2.0113秒

批次 75:
  奖励值: 109.4263
  收益率: 0.5430
  距离: 28.6469
  内存使用: 0.6123
  能量使用: 0.8881
  推理时间: 2.4086秒

批次 76:
  奖励值: 98.2812
  收益率: 0.4960
  距离: 26.2143
  内存使用: 0.5548
  能量使用: 0.8685
  推理时间: 2.2118秒

批次 77:
  奖励值: 92.4602
  收益率: 0.4692
  距离: 25.8595
  内存使用: 0.4540
  能量使用: 0.7662
  推理时间: 2.0713秒

批次 78:
  奖励值: 105.3416
  收益率: 0.5124
  距离: 30.8216
  内存使用: 0.5638
  能量使用: 0.9314
  推理时间: 2.4113秒

批次 79:
  奖励值: 96.5722
  收益率: 0.5029
  距离: 28.7067
  内存使用: 0.5105
  能量使用: 0.8384
  推理时间: 2.2031秒

批次 80:
  奖励值: 92.7073
  收益率: 0.4884
  距离: 23.7316
  内存使用: 0.4789
  能量使用: 0.7750
  推理时间: 2.0374秒

批次 81:
  奖励值: 103.2142
  收益率: 0.5165
  距离: 30.1809
  内存使用: 0.5410
  能量使用: 0.8496
  推理时间: 2.2855秒

批次 82:
  奖励值: 95.8844
  收益率: 0.4808
  距离: 26.9743
  内存使用: 0.4979
  能量使用: 0.7590
  推理时间: 2.1526秒

批次 83:
  奖励值: 97.9957
  收益率: 0.4824
  距离: 24.5623
  内存使用: 0.4729
  能量使用: 0.7736
  推理时间: 2.0931秒

批次 84:
  奖励值: 94.9130
  收益率: 0.4758
  距离: 25.1492
  内存使用: 0.4365
  能量使用: 0.7882
  推理时间: 2.1100秒

批次 85:
  奖励值: 94.5793
  收益率: 0.4718
  距离: 23.4329
  内存使用: 0.4737
  能量使用: 0.7349
  推理时间: 2.1460秒

批次 86:
  奖励值: 98.6338
  收益率: 0.4926
  距离: 27.6490
  内存使用: 0.4503
  能量使用: 0.8332
  推理时间: 2.3070秒

批次 87:
  奖励值: 90.5152
  收益率: 0.4676
  距离: 22.9089
  内存使用: 0.5298
  能量使用: 0.7308
  推理时间: 2.0092秒

批次 88:
  奖励值: 88.8351
  收益率: 0.4655
  距离: 24.7640
  内存使用: 0.4322
  能量使用: 0.7335
  推理时间: 1.9746秒

批次 89:
  奖励值: 101.9603
  收益率: 0.5220
  距离: 27.3306
  内存使用: 0.5148
  能量使用: 0.8340
  推理时间: 2.2518秒

批次 90:
  奖励值: 94.5914
  收益率: 0.4770
  距离: 22.8003
  内存使用: 0.4434
  能量使用: 0.7736
  推理时间: 2.1918秒

批次 91:
  奖励值: 94.0136
  收益率: 0.4739
  距离: 26.4806
  内存使用: 0.4883
  能量使用: 0.8040
  推理时间: 1.9718秒

批次 92:
  奖励值: 92.7461
  收益率: 0.4865
  距离: 26.0625
  内存使用: 0.5087
  能量使用: 0.7366
  推理时间: 2.0117秒

批次 93:
  奖励值: 102.5977
  收益率: 0.5176
  距离: 25.6544
  内存使用: 0.4707
  能量使用: 0.8795
  推理时间: 2.2166秒

批次 94:
  奖励值: 95.8616
  收益率: 0.5091
  距离: 25.5951
  内存使用: 0.4773
  能量使用: 0.8044
  推理时间: 2.1427秒

批次 95:
  奖励值: 100.5331
  收益率: 0.5094
  距离: 30.2193
  内存使用: 0.4859
  能量使用: 0.8285
  推理时间: 2.1233秒

批次 96:
  奖励值: 95.2657
  收益率: 0.4939
  距离: 26.2046
  内存使用: 0.4443
  能量使用: 0.8197
  推理时间: 2.0709秒

批次 97:
  奖励值: 100.6166
  收益率: 0.5033
  距离: 28.9748
  内存使用: 0.5975
  能量使用: 0.7944
  推理时间: 2.1092秒

批次 98:
  奖励值: 92.0742
  收益率: 0.4895
  距离: 25.3941
  内存使用: 0.4973
  能量使用: 0.7603
  推理时间: 2.0464秒

批次 99:
  奖励值: 106.5097
  收益率: 0.5223
  距离: 24.5903
  内存使用: 0.5204
  能量使用: 0.8698
  推理时间: 2.2279秒

批次 100:
  奖励值: 98.6504
  收益率: 0.4929
  距离: 29.3663
  内存使用: 0.5131
  能量使用: 0.8199
  推理时间: 2.1506秒


==================== 总结 ====================
平均收益率: 0.4977
平均能量使用: 0.8091
平均推理时间: 2.2963秒
