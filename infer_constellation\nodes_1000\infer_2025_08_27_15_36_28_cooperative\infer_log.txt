推理数据数量: 100
每个序列任务数量: 1000
星座卫星数量: 3
星座模式: cooperative
模型: gpn+indrnn
种子: 12348
内存总量: 0.3
电量总量: 5
检查点路径: constellation_smp/constellation_smp100/multi_constellation_comparison_2025_08_25_17_02_29/constellation_gpnindrnn_cooperative_2025_08_25_17_02_29

批次 1:
  奖励值: 125.4265
  收益率: 0.3259
  距离: 33.3582
  内存使用: 0.6831
  能量使用: 1.1057
  推理时间: 2.7445秒

批次 2:
  奖励值: 117.2414
  收益率: 0.2981
  距离: 34.9115
  内存使用: 0.6557
  能量使用: 0.9499
  推理时间: 2.6127秒

批次 3:
  奖励值: 111.7742
  收益率: 0.2791
  距离: 30.4436
  内存使用: 0.8637
  能量使用: 0.9443
  推理时间: 2.4525秒

批次 4:
  奖励值: 139.7581
  收益率: 0.3482
  距离: 36.7978
  内存使用: 0.8070
  能量使用: 1.0790
  推理时间: 3.0289秒

批次 5:
  奖励值: 109.2914
  收益率: 0.2786
  距离: 30.4249
  内存使用: 0.8969
  能量使用: 0.8954
  推理时间: 2.4487秒

批次 6:
  奖励值: 132.2846
  收益率: 0.3360
  距离: 35.8159
  内存使用: 0.7425
  能量使用: 1.0725
  推理时间: 2.8660秒

批次 7:
  奖励值: 109.0057
  收益率: 0.2781
  距离: 27.2554
  内存使用: 0.6273
  能量使用: 0.9059
  推理时间: 2.4271秒

批次 8:
  奖励值: 124.3828
  收益率: 0.3179
  距离: 34.6966
  内存使用: 0.7264
  能量使用: 1.0113
  推理时间: 2.8132秒

批次 9:
  奖励值: 121.4128
  收益率: 0.3065
  距离: 31.7712
  内存使用: 0.6586
  能量使用: 0.9446
  推理时间: 2.6789秒

批次 10:
  奖励值: 117.0456
  收益率: 0.2911
  距离: 30.6108
  内存使用: 0.5770
  能量使用: 0.9208
  推理时间: 2.5531秒

批次 11:
  奖励值: 125.8343
  收益率: 0.3122
  距离: 35.6979
  内存使用: 0.6390
  能量使用: 0.9943
  推理时间: 2.7690秒

批次 12:
  奖励值: 120.7545
  收益率: 0.3096
  距离: 32.1839
  内存使用: 0.8997
  能量使用: 1.0075
  推理时间: 2.6485秒

批次 13:
  奖励值: 120.8721
  收益率: 0.3099
  距离: 35.6023
  内存使用: 0.6959
  能量使用: 0.9575
  推理时间: 2.7126秒

批次 14:
  奖励值: 105.6271
  收益率: 0.2678
  距离: 26.9959
  内存使用: 0.5617
  能量使用: 0.7882
  推理时间: 2.3285秒

批次 15:
  奖励值: 124.8989
  收益率: 0.3167
  距离: 31.0952
  内存使用: 0.7216
  能量使用: 0.9852
  推理时间: 2.7708秒

批次 16:
  奖励值: 122.6236
  收益率: 0.3105
  距离: 32.3788
  内存使用: 0.6445
  能量使用: 0.9295
  推理时间: 2.8973秒

批次 17:
  奖励值: 136.3741
  收益率: 0.3442
  距离: 34.8200
  内存使用: 0.6987
  能量使用: 1.1330
  推理时间: 2.9649秒

批次 18:
  奖励值: 119.3114
  收益率: 0.3064
  距离: 32.3228
  内存使用: 0.6534
  能量使用: 1.0010
  推理时间: 2.6619秒

批次 19:
  奖励值: 120.1453
  收益率: 0.3017
  距离: 30.4674
  内存使用: 0.7071
  能量使用: 0.9349
  推理时间: 2.7152秒

批次 20:
  奖励值: 124.8923
  收益率: 0.3082
  距离: 30.4282
  内存使用: 0.6439
  能量使用: 0.9432
  推理时间: 2.6876秒

批次 21:
  奖励值: 120.4433
  收益率: 0.2888
  距离: 30.4829
  内存使用: 0.6014
  能量使用: 1.0291
  推理时间: 2.6960秒

批次 22:
  奖励值: 120.6994
  收益率: 0.3038
  距离: 33.7994
  内存使用: 0.7052
  能量使用: 0.8967
  推理时间: 2.6824秒

批次 23:
  奖励值: 121.4048
  收益率: 0.3070
  距离: 29.7329
  内存使用: 0.6557
  能量使用: 1.0019
  推理时间: 2.6559秒

批次 24:
  奖励值: 118.4095
  收益率: 0.2927
  距离: 27.9966
  内存使用: 0.6313
  能量使用: 0.9532
  推理时间: 2.6016秒

批次 25:
  奖励值: 134.1091
  收益率: 0.3248
  距离: 33.8727
  内存使用: 0.6697
  能量使用: 1.0393
  推理时间: 2.9477秒

批次 26:
  奖励值: 127.5889
  收益率: 0.3245
  距离: 33.5072
  内存使用: 0.6929
  能量使用: 1.0220
  推理时间: 2.7969秒

批次 27:
  奖励值: 108.5503
  收益率: 0.2802
  距离: 29.6607
  内存使用: 0.5525
  能量使用: 0.8861
  推理时间: 2.4361秒

批次 28:
  奖励值: 120.1559
  收益率: 0.3027
  距离: 32.3616
  内存使用: 0.6041
  能量使用: 0.9224
  推理时间: 2.6304秒

批次 29:
  奖励值: 130.0634
  收益率: 0.3284
  距离: 35.3780
  内存使用: 0.7563
  能量使用: 1.1390
  推理时间: 2.8935秒

批次 30:
  奖励值: 112.0398
  收益率: 0.2742
  距离: 29.7990
  内存使用: 0.5870
  能量使用: 0.9344
  推理时间: 2.4540秒

批次 31:
  奖励值: 123.3753
  收益率: 0.3120
  距离: 31.6873
  内存使用: 0.6335
  能量使用: 0.9285
  推理时间: 2.6983秒

批次 32:
  奖励值: 130.7879
  收益率: 0.3287
  距离: 34.6446
  内存使用: 0.7518
  能量使用: 1.0278
  推理时间: 2.8375秒

批次 33:
  奖励值: 116.1541
  收益率: 0.2865
  距离: 26.5319
  内存使用: 0.6535
  能量使用: 0.9065
  推理时间: 2.5324秒

批次 34:
  奖励值: 113.2108
  收益率: 0.2861
  距离: 33.0049
  内存使用: 0.6528
  能量使用: 0.9206
  推理时间: 2.5298秒

批次 35:
  奖励值: 129.0405
  收益率: 0.3268
  距离: 35.0704
  内存使用: 0.7246
  能量使用: 1.1241
  推理时间: 2.8559秒

批次 36:
  奖励值: 135.5697
  收益率: 0.3426
  距离: 35.7757
  内存使用: 0.7569
  能量使用: 1.1279
  推理时间: 2.9449秒

批次 37:
  奖励值: 128.4245
  收益率: 0.3279
  距离: 35.8990
  内存使用: 0.7763
  能量使用: 1.0756
  推理时间: 2.6416秒

批次 38:
  奖励值: 117.4472
  收益率: 0.3027
  距离: 34.3255
  内存使用: 0.6392
  能量使用: 0.9409
  推理时间: 2.6055秒

批次 39:
  奖励值: 113.6290
  收益率: 0.2960
  距离: 30.8178
  内存使用: 0.6024
  能量使用: 0.9218
  推理时间: 2.5416秒

批次 40:
  奖励值: 123.9503
  收益率: 0.3099
  距离: 31.4004
  内存使用: 0.6600
  能量使用: 1.0390
  推理时间: 2.6938秒

批次 41:
  奖励值: 115.6370
  收益率: 0.2930
  距离: 31.5131
  内存使用: 0.6162
  能量使用: 0.9967
  推理时间: 2.6281秒

批次 42:
  奖励值: 133.5625
  收益率: 0.3301
  距离: 32.8653
  内存使用: 0.7357
  能量使用: 1.0740
  推理时间: 2.8616秒

批次 43:
  奖励值: 121.8516
  收益率: 0.3155
  距离: 33.6571
  内存使用: 0.6843
  能量使用: 0.9553
  推理时间: 2.7280秒

批次 44:
  奖励值: 112.0749
  收益率: 0.2842
  距离: 27.1484
  内存使用: 0.6060
  能量使用: 0.8958
  推理时间: 2.4731秒

批次 45:
  奖励值: 121.1136
  收益率: 0.3066
  距离: 32.3611
  内存使用: 0.5934
  能量使用: 1.0183
  推理时间: 2.7580秒

批次 46:
  奖励值: 110.3333
  收益率: 0.2858
  距离: 32.9187
  内存使用: 0.6257
  能量使用: 0.9889
  推理时间: 2.4857秒

批次 47:
  奖励值: 109.0590
  收益率: 0.2756
  距离: 29.9291
  内存使用: 0.8997
  能量使用: 1.0531
  推理时间: 2.4984秒

批次 48:
  奖励值: 111.6620
  收益率: 0.2918
  距离: 30.4021
  内存使用: 0.6163
  能量使用: 0.8576
  推理时间: 2.5334秒

批次 49:
  奖励值: 114.4437
  收益率: 0.2903
  距离: 32.8578
  内存使用: 0.5970
  能量使用: 0.8577
  推理时间: 2.5407秒

批次 50:
  奖励值: 127.6542
  收益率: 0.3149
  距离: 34.1066
  内存使用: 0.7417
  能量使用: 1.0432
  推理时间: 2.8123秒

批次 51:
  奖励值: 120.8729
  收益率: 0.2970
  距离: 28.8797
  内存使用: 0.6310
  能量使用: 0.9435
  推理时间: 2.7818秒

批次 52:
  奖励值: 118.2261
  收益率: 0.2946
  距离: 30.8978
  内存使用: 0.6698
  能量使用: 0.9802
  推理时间: 4.2800秒

批次 53:
  奖励值: 128.7473
  收益率: 0.3298
  距离: 30.0317
  内存使用: 0.7004
  能量使用: 0.9731
  推理时间: 2.7605秒

批次 54:
  奖励值: 123.7826
  收益率: 0.3096
  距离: 31.8763
  内存使用: 0.7164
  能量使用: 1.0421
  推理时间: 2.5984秒

批次 55:
  奖励值: 111.7994
  收益率: 0.2807
  距离: 29.3653
  内存使用: 0.5420
  能量使用: 0.8939
  推理时间: 2.3202秒

批次 56:
  奖励值: 115.2814
  收益率: 0.2867
  距离: 29.2713
  内存使用: 0.6092
  能量使用: 0.9567
  推理时间: 2.4191秒

批次 57:
  奖励值: 119.3673
  收益率: 0.3104
  距离: 30.4989
  内存使用: 0.7197
  能量使用: 1.0468
  推理时间: 2.6036秒

批次 58:
  奖励值: 123.7681
  收益率: 0.3103
  距离: 32.2704
  内存使用: 0.6184
  能量使用: 0.9749
  推理时间: 2.5678秒

批次 59:
  奖励值: 114.8790
  收益率: 0.2895
  距离: 28.5616
  内存使用: 0.6066
  能量使用: 0.8739
  推理时间: 2.3669秒

批次 60:
  奖励值: 107.9141
  收益率: 0.2735
  距离: 26.6628
  内存使用: 0.8455
  能量使用: 0.8635
  推理时间: 2.3558秒

批次 61:
  奖励值: 123.2206
  收益率: 0.3111
  距离: 31.4608
  内存使用: 0.6952
  能量使用: 1.0028
  推理时间: 2.6222秒

批次 62:
  奖励值: 136.2900
  收益率: 0.3435
  距离: 34.8693
  内存使用: 0.8506
  能量使用: 1.1000
  推理时间: 2.8203秒

批次 63:
  奖励值: 113.1603
  收益率: 0.2801
  距离: 29.8047
  内存使用: 0.6129
  能量使用: 0.8619
  推理时间: 2.3076秒

批次 64:
  奖励值: 120.0478
  收益率: 0.3054
  距离: 32.7220
  内存使用: 0.6253
  能量使用: 0.9571
  推理时间: 2.5471秒

批次 65:
  奖励值: 125.0977
  收益率: 0.3128
  距离: 32.0322
  内存使用: 0.6885
  能量使用: 1.0840
  推理时间: 2.6272秒

批次 66:
  奖励值: 120.6751
  收益率: 0.3032
  距离: 30.9887
  内存使用: 0.7218
  能量使用: 0.9393
  推理时间: 2.4870秒

批次 67:
  奖励值: 108.2271
  收益率: 0.2717
  距离: 26.9825
  内存使用: 0.8488
  能量使用: 0.9145
  推理时间: 2.3412秒

批次 68:
  奖励值: 112.6340
  收益率: 0.2849
  距离: 29.0927
  内存使用: 0.5191
  能量使用: 0.9201
  推理时间: 2.3702秒

批次 69:
  奖励值: 115.4662
  收益率: 0.2880
  距离: 27.8537
  内存使用: 0.6960
  能量使用: 0.9178
  推理时间: 2.7493秒

批次 70:
  奖励值: 125.0079
  收益率: 0.3179
  距离: 35.4374
  内存使用: 0.7075
  能量使用: 1.0392
  推理时间: 3.0045秒

批次 71:
  奖励值: 116.4197
  收益率: 0.2889
  距离: 28.2812
  内存使用: 0.8958
  能量使用: 0.9835
  推理时间: 2.7218秒

批次 72:
  奖励值: 125.0964
  收益率: 0.3143
  距离: 30.4988
  内存使用: 0.6940
  能量使用: 1.0144
  推理时间: 2.7337秒

批次 73:
  奖励值: 121.9734
  收益率: 0.3171
  距离: 33.7424
  内存使用: 0.6504
  能量使用: 1.0603
  推理时间: 2.7564秒

批次 74:
  奖励值: 120.4381
  收益率: 0.2933
  距离: 31.7145
  内存使用: 0.6807
  能量使用: 0.9287
  推理时间: 2.7127秒

批次 75:
  奖励值: 117.6949
  收益率: 0.2815
  距离: 28.9937
  内存使用: 0.6689
  能量使用: 0.9476
  推理时间: 2.8164秒

批次 76:
  奖励值: 112.6612
  收益率: 0.2866
  距离: 29.0977
  内存使用: 0.5416
  能量使用: 0.8384
  推理时间: 2.6838秒

批次 77:
  奖励值: 125.3463
  收益率: 0.3089
  距离: 34.3978
  内存使用: 0.7048
  能量使用: 1.0048
  推理时间: 2.7738秒

批次 78:
  奖励值: 118.1006
  收益率: 0.3013
  距离: 31.9136
  内存使用: 0.6668
  能量使用: 1.0184
  推理时间: 2.6149秒

批次 79:
  奖励值: 106.2498
  收益率: 0.2701
  距离: 30.7733
  内存使用: 0.6082
  能量使用: 0.9188
  推理时间: 2.6479秒

批次 80:
  奖励值: 112.1274
  收益率: 0.2793
  距离: 29.4102
  内存使用: 0.6102
  能量使用: 0.9248
  推理时间: 2.5168秒

批次 81:
  奖励值: 112.8878
  收益率: 0.2808
  距离: 28.9073
  内存使用: 0.5816
  能量使用: 0.9355
  推理时间: 2.5051秒

批次 82:
  奖励值: 112.7392
  收益率: 0.2891
  距离: 30.0422
  内存使用: 0.6722
  能量使用: 1.0071
  推理时间: 2.5008秒

批次 83:
  奖励值: 121.2353
  收益率: 0.3044
  距离: 30.3517
  内存使用: 0.6396
  能量使用: 0.9381
  推理时间: 2.6657秒

批次 84:
  奖励值: 123.2879
  收益率: 0.3168
  距离: 33.7830
  内存使用: 0.6636
  能量使用: 0.9774
  推理时间: 2.7134秒

批次 85:
  奖励值: 114.2470
  收益率: 0.2878
  距离: 29.9065
  内存使用: 0.8993
  能量使用: 0.9002
  推理时间: 2.5577秒

批次 86:
  奖励值: 115.6039
  收益率: 0.3071
  距离: 35.2372
  内存使用: 0.6006
  能量使用: 0.9544
  推理时间: 2.6601秒

批次 87:
  奖励值: 132.0613
  收益率: 0.3423
  距离: 33.0786
  内存使用: 0.6818
  能量使用: 1.1171
  推理时间: 2.8511秒

批次 88:
  奖励值: 118.2274
  收益率: 0.3019
  距离: 31.1720
  内存使用: 0.6592
  能量使用: 0.9138
  推理时间: 2.6604秒

批次 89:
  奖励值: 126.5666
  收益率: 0.3174
  距离: 33.6956
  内存使用: 0.7529
  能量使用: 1.0182
  推理时间: 2.8028秒

批次 90:
  奖励值: 108.7018
  收益率: 0.2698
  距离: 25.8238
  内存使用: 0.5808
  能量使用: 0.9918
  推理时间: 2.4179秒

批次 91:
  奖励值: 128.1403
  收益率: 0.3252
  距离: 32.9071
  内存使用: 0.7099
  能量使用: 1.1024
  推理时间: 2.8662秒

批次 92:
  奖励值: 108.2575
  收益率: 0.2742
  距离: 28.9902
  内存使用: 0.6013
  能量使用: 0.8935
  推理时间: 2.4678秒

批次 93:
  奖励值: 110.3234
  收益率: 0.2733
  距离: 28.7315
  内存使用: 0.8995
  能量使用: 0.9222
  推理时间: 2.4847秒

批次 94:
  奖励值: 128.1962
  收益率: 0.3172
  距离: 32.9009
  内存使用: 0.7293
  能量使用: 0.9773
  推理时间: 2.8131秒

批次 95:
  奖励值: 113.9966
  收益率: 0.2841
  距离: 29.4731
  内存使用: 0.6062
  能量使用: 0.9275
  推理时间: 2.4983秒

批次 96:
  奖励值: 115.9465
  收益率: 0.2922
  距离: 29.6069
  内存使用: 0.5504
  能量使用: 0.9498
  推理时间: 2.6094秒

批次 97:
  奖励值: 124.4995
  收益率: 0.3118
  距离: 32.6641
  内存使用: 0.6925
  能量使用: 1.0444
  推理时间: 2.8081秒

批次 98:
  奖励值: 110.4799
  收益率: 0.2829
  距离: 28.3376
  内存使用: 0.5923
  能量使用: 0.9103
  推理时间: 2.4764秒

批次 99:
  奖励值: 112.5578
  收益率: 0.2826
  距离: 29.8713
  内存使用: 0.5541
  能量使用: 0.8589
  推理时间: 2.6303秒

批次 100:
  奖励值: 128.0602
  收益率: 0.3252
  距离: 31.7631
  内存使用: 0.6586
  能量使用: 1.0578
  推理时间: 2.7935秒


==================== 总结 ====================
平均收益率: 0.3021
平均能量使用: 0.9740
平均推理时间: 2.6647秒
