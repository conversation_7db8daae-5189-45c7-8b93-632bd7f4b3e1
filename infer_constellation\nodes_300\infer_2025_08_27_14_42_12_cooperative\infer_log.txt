推理数据数量: 100
每个序列任务数量: 300
星座卫星数量: 3
星座模式: cooperative
模型: gpn+indrnn
种子: 12348
内存总量: 0.3
电量总量: 5
检查点路径: constellation_smp/constellation_smp100/multi_constellation_comparison_2025_08_25_17_02_29/constellation_gpnindrnn_cooperative_2025_08_25_17_02_29

批次 1:
  奖励值: 76.5190
  收益率: 0.6122
  距离: 19.0390
  内存使用: 0.3022
  能量使用: 0.6209
  推理时间: 1.7523秒

批次 2:
  奖励值: 71.0439
  收益率: 0.6059
  距离: 21.6987
  内存使用: 0.3649
  能量使用: 0.6486
  推理时间: 1.7698秒

批次 3:
  奖励值: 72.5454
  收益率: 0.6093
  距离: 21.1025
  内存使用: 0.3550
  能量使用: 0.6518
  推理时间: 1.7377秒

批次 4:
  奖励值: 77.9238
  收益率: 0.6489
  距离: 20.8516
  内存使用: 0.3884
  能量使用: 0.7124
  推理时间: 1.8379秒

批次 5:
  奖励值: 76.3463
  收益率: 0.5956
  距离: 21.2724
  内存使用: 0.3681
  能量使用: 0.6669
  推理时间: 1.7819秒

批次 6:
  奖励值: 84.9575
  收益率: 0.6785
  距离: 19.7644
  内存使用: 0.3688
  能量使用: 0.7517
  推理时间: 1.9345秒

批次 7:
  奖励值: 75.5081
  收益率: 0.6305
  距离: 24.7100
  内存使用: 0.3375
  能量使用: 0.7363
  推理时间: 1.8435秒

批次 8:
  奖励值: 68.5985
  收益率: 0.5923
  距离: 22.3407
  内存使用: 0.3088
  能量使用: 0.5534
  推理时间: 1.6550秒

批次 9:
  奖励值: 80.7665
  收益率: 0.6531
  距离: 20.4316
  内存使用: 0.3772
  能量使用: 0.6582
  推理时间: 1.8040秒

批次 10:
  奖励值: 72.0973
  收益率: 0.6185
  距离: 20.8807
  内存使用: 0.3705
  能量使用: 0.5540
  推理时间: 1.6877秒

批次 11:
  奖励值: 75.1009
  收益率: 0.6039
  距离: 20.2330
  内存使用: 0.3114
  能量使用: 0.6537
  推理时间: 1.7660秒

批次 12:
  奖励值: 73.6051
  收益率: 0.6105
  距离: 19.1409
  内存使用: 0.3609
  能量使用: 0.6881
  推理时间: 1.6963秒

批次 13:
  奖励值: 68.7969
  收益率: 0.6042
  距离: 20.9149
  内存使用: 0.3112
  能量使用: 0.5801
  推理时间: 1.6402秒

批次 14:
  奖励值: 76.3961
  收益率: 0.6409
  距离: 22.2209
  内存使用: 0.3857
  能量使用: 0.6011
  推理时间: 1.8318秒

批次 15:
  奖励值: 75.9363
  收益率: 0.6140
  距离: 19.4738
  内存使用: 0.4089
  能量使用: 0.6447
  推理时间: 1.7433秒

批次 16:
  奖励值: 67.3280
  收益率: 0.5624
  距离: 17.7112
  内存使用: 0.2395
  能量使用: 0.5933
  推理时间: 1.6092秒

批次 17:
  奖励值: 71.5358
  收益率: 0.5971
  距离: 18.9998
  内存使用: 0.3215
  能量使用: 0.6070
  推理时间: 1.6939秒

批次 18:
  奖励值: 67.5454
  收益率: 0.5893
  距离: 19.7945
  内存使用: 0.2848
  能量使用: 0.6300
  推理时间: 1.6706秒

批次 19:
  奖励值: 78.8802
  收益率: 0.6476
  距离: 20.1854
  内存使用: 0.3774
  能量使用: 0.6768
  推理时间: 1.8197秒

批次 20:
  奖励值: 66.2899
  收益率: 0.5962
  距离: 22.0712
  内存使用: 0.3048
  能量使用: 0.6442
  推理时间: 1.6896秒

批次 21:
  奖励值: 70.5571
  收益率: 0.5917
  距离: 21.1497
  内存使用: 0.3023
  能量使用: 0.6338
  推理时间: 1.7141秒

批次 22:
  奖励值: 75.6837
  收益率: 0.6461
  距离: 21.4509
  内存使用: 0.3627
  能量使用: 0.6192
  推理时间: 1.7947秒

批次 23:
  奖励值: 70.8342
  收益率: 0.6161
  距离: 21.3349
  内存使用: 0.3430
  能量使用: 0.6537
  推理时间: 1.7161秒

批次 24:
  奖励值: 70.6283
  收益率: 0.5656
  距离: 20.0463
  内存使用: 0.2947
  能量使用: 0.6025
  推理时间: 1.6634秒

批次 25:
  奖励值: 68.9432
  收益率: 0.5970
  距离: 18.6580
  内存使用: 0.3671
  能量使用: 0.6886
  推理时间: 1.7061秒

批次 26:
  奖励值: 78.1990
  收益率: 0.6516
  距离: 20.8614
  内存使用: 0.4799
  能量使用: 0.6608
  推理时间: 1.8897秒

批次 27:
  奖励值: 73.7251
  收益率: 0.5859
  距离: 19.3287
  内存使用: 0.3334
  能量使用: 0.6878
  推理时间: 1.6608秒

批次 28:
  奖励值: 71.5899
  收益率: 0.5889
  距离: 21.4688
  内存使用: 0.3215
  能量使用: 0.6299
  推理时间: 1.7004秒

批次 29:
  奖励值: 83.6913
  收益率: 0.6283
  距离: 23.4399
  内存使用: 0.4184
  能量使用: 0.7591
  推理时间: 1.9329秒

批次 30:
  奖励值: 70.0703
  收益率: 0.5813
  距离: 19.7477
  内存使用: 0.3432
  能量使用: 0.5685
  推理时间: 1.6571秒

批次 31:
  奖励值: 77.5837
  收益率: 0.6351
  距离: 18.2282
  内存使用: 0.3193
  能量使用: 0.6850
  推理时间: 1.8061秒

批次 32:
  奖励值: 76.2883
  收益率: 0.6167
  距离: 22.3319
  内存使用: 0.3905
  能量使用: 0.7009
  推理时间: 1.8926秒

批次 33:
  奖励值: 72.5756
  收益率: 0.6346
  距离: 21.7434
  内存使用: 0.2899
  能量使用: 0.6157
  推理时间: 1.7656秒

批次 34:
  奖励值: 64.9235
  收益率: 0.5794
  距离: 18.8480
  内存使用: 0.3113
  能量使用: 0.5936
  推理时间: 1.5736秒

批次 35:
  奖励值: 72.7625
  收益率: 0.6065
  距离: 20.4252
  内存使用: 0.3106
  能量使用: 0.5392
  推理时间: 1.6941秒

批次 36:
  奖励值: 69.4243
  收益率: 0.5889
  距离: 21.7732
  内存使用: 0.3345
  能量使用: 0.6438
  推理时间: 1.7292秒

批次 37:
  奖励值: 72.8693
  收益率: 0.6055
  距离: 20.2712
  内存使用: 0.3848
  能量使用: 0.6054
  推理时间: 1.7268秒

批次 38:
  奖励值: 75.1952
  收益率: 0.6244
  距离: 21.8115
  内存使用: 0.3267
  能量使用: 0.6714
  推理时间: 1.7963秒

批次 39:
  奖励值: 69.0131
  收益率: 0.6054
  距离: 19.0415
  内存使用: 0.3311
  能量使用: 0.6012
  推理时间: 1.6762秒

批次 40:
  奖励值: 79.2773
  收益率: 0.6349
  距离: 20.4016
  内存使用: 0.2729
  能量使用: 0.5709
  推理时间: 1.7580秒

批次 41:
  奖励值: 70.0550
  收益率: 0.6113
  距离: 20.9946
  内存使用: 0.2699
  能量使用: 0.6256
  推理时间: 1.6903秒

批次 42:
  奖励值: 68.6028
  收益率: 0.5639
  距离: 18.8972
  内存使用: 0.3113
  能量使用: 0.5860
  推理时间: 1.6480秒

批次 43:
  奖励值: 73.0962
  收益率: 0.5968
  距离: 18.7107
  内存使用: 0.3025
  能量使用: 0.5944
  推理时间: 1.7042秒

批次 44:
  奖励值: 71.1151
  收益率: 0.6079
  距离: 20.2738
  内存使用: 0.2689
  能量使用: 0.6271
  推理时间: 1.6571秒

批次 45:
  奖励值: 74.3413
  收益率: 0.6384
  距离: 21.9316
  内存使用: 0.3727
  能量使用: 0.6133
  推理时间: 1.8177秒

批次 46:
  奖励值: 70.3311
  收益率: 0.5872
  距离: 21.9328
  内存使用: 0.3471
  能量使用: 0.6578
  推理时间: 1.7068秒

批次 47:
  奖励值: 71.0203
  收益率: 0.5852
  距离: 22.5372
  内存使用: 0.3454
  能量使用: 0.6767
  推理时间: 1.7032秒

批次 48:
  奖励值: 73.3721
  收益率: 0.6096
  距离: 21.8999
  内存使用: 0.3594
  能量使用: 0.5964
  推理时间: 1.6713秒

批次 49:
  奖励值: 74.3888
  收益率: 0.6344
  距离: 22.9976
  内存使用: 0.3496
  能量使用: 0.6751
  推理时间: 1.8646秒

批次 50:
  奖励值: 72.5838
  收益率: 0.6034
  距离: 17.9327
  内存使用: 0.3206
  能量使用: 0.5791
  推理时间: 1.6690秒

批次 51:
  奖励值: 65.0028
  收益率: 0.5486
  距离: 19.4027
  内存使用: 0.2289
  能量使用: 0.5629
  推理时间: 1.5629秒

批次 52:
  奖励值: 69.8979
  收益率: 0.5841
  距离: 17.6890
  内存使用: 0.3416
  能量使用: 0.5735
  推理时间: 1.6365秒

批次 53:
  奖励值: 70.4559
  收益率: 0.5866
  距离: 18.7934
  内存使用: 0.3085
  能量使用: 0.5862
  推理时间: 1.6697秒

批次 54:
  奖励值: 68.9797
  收益率: 0.6190
  距离: 23.8696
  内存使用: 0.3203
  能量使用: 0.6507
  推理时间: 1.6988秒

批次 55:
  奖励值: 68.5958
  收益率: 0.5920
  距离: 21.5629
  内存使用: 0.2799
  能量使用: 0.5656
  推理时间: 1.6711秒

批次 56:
  奖励值: 68.4499
  收益率: 0.6059
  距离: 23.0335
  内存使用: 0.3098
  能量使用: 0.5570
  推理时间: 1.6736秒

批次 57:
  奖励值: 77.0529
  收益率: 0.6125
  距离: 21.8633
  内存使用: 0.3783
  能量使用: 0.6526
  推理时间: 1.7971秒

批次 58:
  奖励值: 78.8131
  收益率: 0.6373
  距离: 20.9519
  内存使用: 0.3505
  能量使用: 0.6712
  推理时间: 1.8214秒

批次 59:
  奖励值: 75.4202
  收益率: 0.6395
  距离: 24.0088
  内存使用: 0.4399
  能量使用: 0.7109
  推理时间: 1.8652秒

批次 60:
  奖励值: 75.8123
  收益率: 0.6428
  距离: 23.3135
  内存使用: 0.4329
  能量使用: 0.6290
  推理时间: 1.8323秒

批次 61:
  奖励值: 69.1933
  收益率: 0.5966
  距离: 16.8601
  内存使用: 0.3140
  能量使用: 0.6394
  推理时间: 1.5825秒

批次 62:
  奖励值: 68.5760
  收益率: 0.5723
  距离: 17.1014
  内存使用: 0.3141
  能量使用: 0.6324
  推理时间: 1.6327秒

批次 63:
  奖励值: 71.1603
  收益率: 0.6004
  距离: 17.5682
  内存使用: 0.3248
  能量使用: 0.5862
  推理时间: 1.6381秒

批次 64:
  奖励值: 76.5593
  收益率: 0.6476
  距离: 22.5863
  内存使用: 0.4088
  能量使用: 0.6863
  推理时间: 1.8507秒

批次 65:
  奖励值: 70.7179
  收益率: 0.5801
  距离: 20.6508
  内存使用: 0.3123
  能量使用: 0.6012
  推理时间: 1.6355秒

批次 66:
  奖励值: 73.3953
  收益率: 0.5836
  距离: 18.4677
  内存使用: 0.3222
  能量使用: 0.6196
  推理时间: 1.6960秒

批次 67:
  奖励值: 71.0583
  收益率: 0.6260
  距离: 23.2503
  内存使用: 0.3955
  能量使用: 0.6376
  推理时间: 1.7508秒

批次 68:
  奖励值: 72.6246
  收益率: 0.5829
  距离: 19.7801
  内存使用: 0.3703
  能量使用: 0.6004
  推理时间: 1.7480秒

批次 69:
  奖励值: 75.8216
  收益率: 0.6284
  距离: 22.4650
  内存使用: 0.4082
  能量使用: 0.6837
  推理时间: 1.8363秒

批次 70:
  奖励值: 72.9158
  收益率: 0.6064
  距离: 16.7816
  内存使用: 0.3340
  能量使用: 0.6529
  推理时间: 1.7613秒

批次 71:
  奖励值: 70.9330
  收益率: 0.6069
  距离: 22.9480
  内存使用: 0.3549
  能量使用: 0.6310
  推理时间: 1.7174秒

批次 72:
  奖励值: 73.7004
  收益率: 0.6065
  距离: 19.0428
  内存使用: 0.3584
  能量使用: 0.5980
  推理时间: 1.7468秒

批次 73:
  奖励值: 76.3923
  收益率: 0.6342
  距离: 19.7005
  内存使用: 0.3042
  能量使用: 0.6106
  推理时间: 1.7695秒

批次 74:
  奖励值: 72.4490
  收益率: 0.6057
  距离: 20.3567
  内存使用: 0.3020
  能量使用: 0.6433
  推理时间: 1.7179秒

批次 75:
  奖励值: 70.0210
  收益率: 0.6153
  距离: 18.6599
  内存使用: 0.3215
  能量使用: 0.5765
  推理时间: 2.5219秒

批次 76:
  奖励值: 73.8108
  收益率: 0.6101
  距离: 21.0037
  内存使用: 0.3479
  能量使用: 0.6268
  推理时间: 1.7277秒

批次 77:
  奖励值: 67.4050
  收益率: 0.5581
  距离: 18.1297
  内存使用: 0.2842
  能量使用: 0.5761
  推理时间: 1.5667秒

批次 78:
  奖励值: 76.0393
  收益率: 0.6210
  距离: 20.8939
  内存使用: 0.3810
  能量使用: 0.6464
  推理时间: 1.8152秒

批次 79:
  奖励值: 73.8724
  收益率: 0.6331
  距离: 21.0414
  内存使用: 0.3591
  能量使用: 0.6546
  推理时间: 1.8043秒

批次 80:
  奖励值: 76.3444
  收益率: 0.6137
  距离: 20.1368
  内存使用: 0.3513
  能量使用: 0.6230
  推理时间: 1.7751秒

批次 81:
  奖励值: 63.0505
  收益率: 0.5488
  距离: 17.0702
  内存使用: 0.2449
  能量使用: 0.5840
  推理时间: 1.5208秒

批次 82:
  奖励值: 76.3440
  收益率: 0.6155
  距离: 22.1384
  内存使用: 0.3413
  能量使用: 0.6324
  推理时间: 1.7966秒

批次 83:
  奖励值: 81.7556
  收益率: 0.6473
  距离: 20.0635
  内存使用: 0.3940
  能量使用: 0.6313
  推理时间: 1.8573秒

批次 84:
  奖励值: 76.6487
  收益率: 0.6593
  距离: 25.2907
  内存使用: 0.3963
  能量使用: 0.6155
  推理时间: 1.8579秒

批次 85:
  奖励值: 74.3082
  收益率: 0.6215
  距离: 19.2277
  内存使用: 0.3374
  能量使用: 0.6186
  推理时间: 1.7499秒

批次 86:
  奖励值: 75.8494
  收益率: 0.6612
  距离: 24.0116
  内存使用: 0.4187
  能量使用: 0.6709
  推理时间: 1.9040秒

批次 87:
  奖励值: 77.5096
  收益率: 0.6465
  距离: 23.4917
  内存使用: 0.4144
  能量使用: 0.6747
  推理时间: 1.8365秒

批次 88:
  奖励值: 71.9565
  收益率: 0.6037
  距离: 23.3140
  内存使用: 0.3590
  能量使用: 0.6141
  推理时间: 1.7449秒

批次 89:
  奖励值: 71.1840
  收益率: 0.5886
  距离: 19.5486
  内存使用: 0.2877
  能量使用: 0.5956
  推理时间: 1.6237秒

批次 90:
  奖励值: 81.1895
  收益率: 0.6601
  距离: 23.7630
  内存使用: 0.4050
  能量使用: 0.6530
  推理时间: 1.8999秒

批次 91:
  奖励值: 71.0078
  收益率: 0.5864
  距离: 17.9067
  内存使用: 0.2775
  能量使用: 0.6003
  推理时间: 1.6347秒

批次 92:
  奖励值: 72.6842
  收益率: 0.6219
  距离: 22.0578
  内存使用: 0.3210
  能量使用: 0.6528
  推理时间: 1.7224秒

批次 93:
  奖励值: 72.1140
  收益率: 0.6041
  距离: 19.6521
  内存使用: 0.3262
  能量使用: 0.5936
  推理时间: 1.6579秒

批次 94:
  奖励值: 76.1137
  收益率: 0.6410
  距离: 23.5693
  内存使用: 0.3796
  能量使用: 0.6238
  推理时间: 1.8208秒

批次 95:
  奖励值: 67.6876
  收益率: 0.6048
  距离: 20.6069
  内存使用: 0.2792
  能量使用: 0.7387
  推理时间: 1.7162秒

批次 96:
  奖励值: 76.9347
  收益率: 0.6385
  距离: 23.5232
  内存使用: 0.3523
  能量使用: 0.6550
  推理时间: 1.8523秒

批次 97:
  奖励值: 76.2143
  收益率: 0.6392
  距离: 22.0006
  内存使用: 0.3201
  能量使用: 0.6507
  推理时间: 1.8182秒

批次 98:
  奖励值: 79.0110
  收益率: 0.6628
  距离: 22.9546
  内存使用: 0.3741
  能量使用: 0.6494
  推理时间: 1.8974秒

批次 99:
  奖励值: 77.1022
  收益率: 0.6598
  距离: 23.2710
  内存使用: 0.3010
  能量使用: 0.6236
  推理时间: 1.8487秒

批次 100:
  奖励值: 66.3625
  收益率: 0.5558
  距离: 17.9071
  内存使用: 0.2443
  能量使用: 0.5236
  推理时间: 1.5520秒


==================== 总结 ====================
平均收益率: 0.6120
平均能量使用: 0.6312
平均推理时间: 1.7479秒
