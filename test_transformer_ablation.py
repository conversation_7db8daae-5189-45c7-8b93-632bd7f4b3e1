"""
Transformer消融实验功能测试脚本
用于验证消融实验工具的正确性

测试内容:
1. 配置生成功能
2. 模型创建功能  
3. 简化训练流程
4. 结果分析功能
"""

import os
import sys
import torch
import numpy as np
import tempfile
import shutil

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from ablation_configs import TRANSFORMER_ABLATION_CONFIGS, validate_experiment_config
from transformer_ablation_study import TransformerAblationController
from constellation_smp.constellation_smp import ConstellationSMPDataset
from constellation_smp.gpn_constellation import GPNConstellation, ConstellationStateCritic
from hyperparameter import args

device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

def test_config_generation():
    """测试配置生成功能"""
    print("🧪 测试1: 配置生成功能")
    print("-" * 40)
    
    try:
        # 验证所有配置
        for i, config in enumerate(TRANSFORMER_ABLATION_CONFIGS):
            validate_experiment_config(config)
            print(f"  ✅ 配置 {i+1}: {config['name']} - {config['description']}")
        
        print(f"  📊 总配置数: {len(TRANSFORMER_ABLATION_CONFIGS)}")
        print("  ✅ 配置生成测试通过\n")
        return True
        
    except Exception as e:
        print(f"  ❌ 配置生成测试失败: {e}\n")
        return False

def test_model_creation():
    """测试模型创建功能"""
    print("🧪 测试2: 模型创建功能")
    print("-" * 40)
    
    try:
        # 创建小规模测试数据
        test_nodes = 20
        test_satellites = 2
        
        train_data = ConstellationSMPDataset(
            test_nodes, 100, args.seed,
            args.memory_total, args.power_total, test_satellites
        )
        
        # 测试不同配置的模型创建
        controller = TransformerAblationController(args)
        
        test_configs = TRANSFORMER_ABLATION_CONFIGS[:3]  # 测试前3个配置
        
        for config in test_configs:
            try:
                actor, critic = controller.create_model_for_config(
                    'hybrid', train_data, config['config'], config['use_transformer']
                )
                
                actor_params = sum(p.numel() for p in actor.parameters())
                critic_params = sum(p.numel() for p in critic.parameters())
                total_params = actor_params + critic_params
                
                print(f"  ✅ {config['name']}: {total_params:,} 参数")
                
                # 测试前向传播
                with torch.no_grad():
                    static, dynamic, _ = next(iter(torch.utils.data.DataLoader(train_data, 2, False)))
                    static = static.to(device)
                    dynamic = dynamic.to(device)
                    
                    tour_indices, satellite_indices, tour_log_prob, satellite_log_prob = actor(static, dynamic)
                    critic_est = critic(static, dynamic)
                    
                    print(f"     前向传播成功: tour_shape={tour_indices.shape}, critic_shape={critic_est.shape}")
                
            except Exception as e:
                print(f"  ❌ {config['name']} 模型创建失败: {e}")
                return False
        
        print("  ✅ 模型创建测试通过\n")
        return True
        
    except Exception as e:
        print(f"  ❌ 模型创建测试失败: {e}\n")
        return False

def test_training_pipeline():
    """测试训练流程"""
    print("🧪 测试3: 训练流程测试")
    print("-" * 40)
    
    try:
        # 创建临时目录
        temp_dir = tempfile.mkdtemp()
        
        # 修改args为测试配置
        original_epochs = args.epochs
        original_train_size = args.train_size
        original_valid_size = args.valid_size
        original_num_nodes = args.num_nodes
        
        args.epochs = 1  # 只训练1轮
        args.train_size = 50  # 小数据集
        args.valid_size = 20
        args.num_nodes = 20
        
        try:
            controller = TransformerAblationController(args)
            controller.setup_logging(os.path.join(temp_dir, 'test_log.txt'))
            
            # 测试单个配置的训练
            test_config = TRANSFORMER_ABLATION_CONFIGS[1]  # lightweight配置
            
            result = controller.run_single_experiment(
                test_config, 'hybrid', temp_dir
            )
            
            print(f"  ✅ 训练完成: {test_config['name']}")
            print(f"     奖励: {result['best_reward']:.4f}")
            print(f"     参数: {result['total_params']:,}")
            print(f"     时长: {result['training_duration']:.1f}秒")
            
            # 检查输出文件
            expected_files = ['actor.pt', 'critic.pt']
            save_dir = result['save_dir']
            
            for file_name in expected_files:
                file_path = os.path.join(save_dir, file_name)
                if os.path.exists(file_path):
                    print(f"     ✅ 文件存在: {file_name}")
                else:
                    print(f"     ⚠️ 文件缺失: {file_name}")
            
            print("  ✅ 训练流程测试通过\n")
            return True
            
        finally:
            # 恢复原始配置
            args.epochs = original_epochs
            args.train_size = original_train_size
            args.valid_size = original_valid_size
            args.num_nodes = original_num_nodes
            
            # 清理临时目录
            try:
                shutil.rmtree(temp_dir)
            except:
                pass
        
    except Exception as e:
        print(f"  ❌ 训练流程测试失败: {e}\n")
        import traceback
        traceback.print_exc()
        return False

def test_analysis_tools():
    """测试分析工具"""
    print("🧪 测试4: 分析工具测试")
    print("-" * 40)
    
    try:
        # 创建模拟结果数据
        mock_results = {
            'experiment_info': {
                'experiment_type': 'transformer_ablation',
                'timestamp': '2025-08-27 14:30:00',
                'num_nodes': 100,
                'num_satellites': 3,
                'epochs': 3,
                'constellation_mode': 'hybrid'
            },
            'results': [
                {
                    'config_name': 'baseline_no_transformer',
                    'best_reward': 0.75,
                    'revenue_rate_avg': 0.65,
                    'distance_avg': 2.5,
                    'memory_avg': 0.1,
                    'power_avg': 1.2,
                    'total_params': 2200000,
                    'training_duration': 120.0
                },
                {
                    'config_name': 'default',
                    'best_reward': 0.82,
                    'revenue_rate_avg': 0.71,
                    'distance_avg': 2.3,
                    'memory_avg': 0.08,
                    'power_avg': 1.1,
                    'total_params': 4200000,
                    'training_duration': 180.0
                }
            ]
        }
        
        # 创建临时目录和文件
        temp_dir = tempfile.mkdtemp()
        json_path = os.path.join(temp_dir, 'transformer_ablation_results.json')
        
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(mock_results, f, indent=2, ensure_ascii=False)
        
        # 测试分析功能
        from ablation_analysis import AblationAnalyzer
        
        analyzer = AblationAnalyzer(temp_dir)
        analyzer.load_results()
        
        # 测试统计分析
        significance_results = analyzer.calculate_statistical_significance()
        print(f"  ✅ 统计分析完成: {len(significance_results)} 个配置")
        
        # 测试快速摘要
        analyzer.print_quick_summary()
        
        # 清理
        shutil.rmtree(temp_dir)
        
        print("  ✅ 分析工具测试通过\n")
        return True
        
    except Exception as e:
        print(f"  ❌ 分析工具测试失败: {e}\n")
        return False

def run_comprehensive_test():
    """运行综合测试"""
    print("🔬 Transformer消融实验功能综合测试")
    print("=" * 60)
    print(f"设备: {device}")
    print(f"PyTorch版本: {torch.__version__}")
    print()
    
    test_results = []
    
    # 运行所有测试
    tests = [
        ("配置生成", test_config_generation),
        ("模型创建", test_model_creation),
        ("训练流程", test_training_pipeline),
        ("分析工具", test_analysis_tools)
    ]
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            test_results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试出现异常: {e}")
            test_results.append((test_name, False))
    
    # 输出测试总结
    print("📋 测试总结")
    print("=" * 30)
    
    passed_tests = sum(1 for _, result in test_results if result)
    total_tests = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    print(f"\n总体结果: {passed_tests}/{total_tests} 测试通过")
    
    if passed_tests == total_tests:
        print("🎉 所有测试通过！消融实验工具可以正常使用")
        return True
    else:
        print("⚠️ 部分测试失败，请检查相关功能")
        return False

def print_usage_guide():
    """打印使用指南"""
    print("📖 Transformer消融实验使用指南")
    print("=" * 50)
    print()
    print("1. 运行功能测试:")
    print("   python test_transformer_ablation.py")
    print()
    print("2. 快速消融实验:")
    print("   python run_transformer_ablation.py")
    print()
    print("3. 完整消融实验:")
    print("   python transformer_ablation_study.py")
    print()
    print("4. 集成到多星座训练:")
    print("   python \"train_multi_constellation_modes transformer.py\" --ablation")
    print()
    print("5. 分析实验结果:")
    print("   python ablation_analysis.py --results_dir path/to/results")
    print()
    print("📋 实验配置:")
    print("   • 8种Transformer配置")
    print("   • 从轻量级到超深层")
    print("   • 包含基线对比")
    print("   • 自动生成分析报告")

if __name__ == '__main__':
    if len(sys.argv) > 1 and sys.argv[1] in ['-h', '--help', 'help']:
        print_usage_guide()
        sys.exit(0)
    
    # 运行综合测试
    success = run_comprehensive_test()
    
    if success:
        print("\n🚀 可以开始运行消融实验了！")
        print("推荐命令:")
        print("  python run_transformer_ablation.py")
    else:
        print("\n🔧 请先修复测试中发现的问题")
    
    sys.exit(0 if success else 1)
