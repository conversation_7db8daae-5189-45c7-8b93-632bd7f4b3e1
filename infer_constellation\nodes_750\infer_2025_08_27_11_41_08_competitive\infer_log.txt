推理数据数量: 100
每个序列任务数量: 750
星座卫星数量: 3
星座模式: competitive
模型: gpn+indrnn
种子: 12348
内存总量: 0.3
电量总量: 5
检查点路径: constellation_smp/constellation_smp100/multi_constellation_comparison_2025_08_25_17_02_29/constellation_gpnindrnn_competitive_2025_08_26_05_01_59

批次 1:
  奖励值: 114.7541
  收益率: 0.3850
  距离: 30.6954
  内存使用: 0.8986
  能量使用: 0.8979
  推理时间: 2.7202秒

批次 2:
  奖励值: 119.2246
  收益率: 0.3962
  距离: 29.3728
  内存使用: 0.6506
  能量使用: 0.9274
  推理时间: 2.3910秒

批次 3:
  奖励值: 117.7636
  收益率: 0.3934
  距离: 27.4477
  内存使用: 0.6585
  能量使用: 0.9665
  推理时间: 2.4863秒

批次 4:
  奖励值: 113.2400
  收益率: 0.3864
  距离: 29.5972
  内存使用: 0.6105
  能量使用: 0.9122
  推理时间: 2.6854秒

批次 5:
  奖励值: 117.8026
  收益率: 0.4035
  距离: 29.1014
  内存使用: 0.8995
  能量使用: 0.9939
  推理时间: 2.4048秒

批次 6:
  奖励值: 127.9714
  收益率: 0.4407
  距离: 34.2872
  内存使用: 0.7001
  能量使用: 1.0699
  推理时间: 2.6373秒

批次 7:
  奖励值: 119.5563
  收益率: 0.4083
  距离: 35.6419
  内存使用: 0.6710
  能量使用: 0.9746
  推理时间: 2.4763秒

批次 8:
  奖励值: 118.2136
  收益率: 0.3962
  距离: 31.6273
  内存使用: 0.6422
  能量使用: 0.9033
  推理时间: 2.3287秒

批次 9:
  奖励值: 123.4015
  收益率: 0.4338
  距离: 32.1439
  内存使用: 0.5888
  能量使用: 1.0643
  推理时间: 2.5045秒

批次 10:
  奖励值: 120.3901
  收益率: 0.3976
  距离: 34.1119
  内存使用: 0.6699
  能量使用: 1.0379
  推理时间: 2.4872秒

批次 11:
  奖励值: 119.6062
  收益率: 0.4115
  距离: 28.8099
  内存使用: 0.6435
  能量使用: 0.9343
  推理时间: 2.3835秒

批次 12:
  奖励值: 114.6287
  收益率: 0.3829
  距离: 31.3456
  内存使用: 0.6444
  能量使用: 0.9525
  推理时间: 2.3272秒

批次 13:
  奖励值: 115.5005
  收益率: 0.3796
  距离: 30.4504
  内存使用: 0.6068
  能量使用: 0.9418
  推理时间: 2.3239秒

批次 14:
  奖励值: 114.3185
  收益率: 0.3949
  距离: 31.1300
  内存使用: 0.8819
  能量使用: 0.9511
  推理时间: 2.3625秒

批次 15:
  奖励值: 110.1430
  收益率: 0.3700
  距离: 29.0761
  内存使用: 0.5675
  能量使用: 0.8704
  推理时间: 2.2277秒

批次 16:
  奖励值: 120.4601
  收益率: 0.3971
  距离: 31.1143
  内存使用: 0.6318
  能量使用: 0.9337
  推理时间: 2.4208秒

批次 17:
  奖励值: 120.6842
  收益率: 0.3919
  距离: 32.8314
  内存使用: 0.6561
  能量使用: 0.9440
  推理时间: 2.4633秒

批次 18:
  奖励值: 111.0128
  收益率: 0.3744
  距离: 27.9975
  内存使用: 0.8730
  能量使用: 0.9039
  推理时间: 2.2853秒

批次 19:
  奖励值: 114.7990
  收益率: 0.3754
  距离: 30.6954
  内存使用: 0.6446
  能量使用: 0.8618
  推理时间: 2.3119秒

批次 20:
  奖励值: 119.3836
  收益率: 0.3970
  距离: 29.7983
  内存使用: 0.6241
  能量使用: 1.0105
  推理时间: 2.3635秒

批次 21:
  奖励值: 118.5611
  收益率: 0.3921
  距离: 35.6196
  内存使用: 0.6083
  能量使用: 1.0499
  推理时间: 2.5330秒

批次 22:
  奖励值: 118.1223
  收益率: 0.3932
  距离: 28.7417
  内存使用: 0.8732
  能量使用: 0.9406
  推理时间: 2.5149秒

批次 23:
  奖励值: 109.8838
  收益率: 0.3624
  距离: 25.5003
  内存使用: 0.8982
  能量使用: 0.8340
  推理时间: 2.3643秒

批次 24:
  奖励值: 110.1060
  收益率: 0.3795
  距离: 34.2911
  内存使用: 0.5907
  能量使用: 0.9061
  推理时间: 2.4894秒

批次 25:
  奖励值: 112.6821
  收益率: 0.3683
  距离: 30.7380
  内存使用: 0.6046
  能量使用: 0.8932
  推理时间: 2.3337秒

批次 26:
  奖励值: 122.9302
  收益率: 0.4130
  距离: 29.5686
  内存使用: 0.7438
  能量使用: 1.0272
  推理时间: 2.5059秒

批次 27:
  奖励值: 125.4289
  收益率: 0.4051
  距离: 31.4346
  内存使用: 0.6791
  能量使用: 1.0285
  推理时间: 2.8538秒

批次 28:
  奖励值: 124.0579
  收益率: 0.4034
  距离: 28.3203
  内存使用: 0.6085
  能量使用: 0.9554
  推理时间: 3.0578秒

批次 29:
  奖励值: 112.8364
  收益率: 0.3841
  距离: 33.2810
  内存使用: 0.5955
  能量使用: 0.8508
  推理时间: 2.4725秒

批次 30:
  奖励值: 122.0236
  收益率: 0.3931
  距离: 31.3894
  内存使用: 0.6140
  能量使用: 0.9495
  推理时间: 2.7247秒

批次 31:
  奖励值: 114.7240
  收益率: 0.3791
  距离: 31.3289
  内存使用: 0.6353
  能量使用: 0.9878
  推理时间: 2.6346秒

批次 32:
  奖励值: 116.1129
  收益率: 0.3755
  距离: 28.4512
  内存使用: 0.5892
  能量使用: 0.9338
  推理时间: 2.4360秒

批次 33:
  奖励值: 130.3156
  收益率: 0.4373
  距离: 35.1769
  内存使用: 0.7109
  能量使用: 1.0810
  推理时间: 2.7212秒

批次 34:
  奖励值: 114.8908
  收益率: 0.3957
  距离: 30.5588
  内存使用: 0.8983
  能量使用: 0.9611
  推理时间: 2.4066秒

批次 35:
  奖励值: 115.9633
  收益率: 0.3897
  距离: 31.7216
  内存使用: 0.6123
  能量使用: 0.9394
  推理时间: 2.5256秒

批次 36:
  奖励值: 122.8547
  收益率: 0.4125
  距离: 30.9132
  内存使用: 0.6150
  能量使用: 0.9623
  推理时间: 2.5404秒

批次 37:
  奖励值: 117.4282
  收益率: 0.3988
  距离: 30.7295
  内存使用: 0.6458
  能量使用: 0.9682
  推理时间: 2.5277秒

批次 38:
  奖励值: 124.5998
  收益率: 0.4144
  距离: 33.3109
  内存使用: 0.6598
  能量使用: 1.0298
  推理时间: 2.5141秒

批次 39:
  奖励值: 121.1648
  收益率: 0.4014
  距离: 29.6030
  内存使用: 0.6540
  能量使用: 1.0133
  推理时间: 2.7332秒

批次 40:
  奖励值: 118.0884
  收益率: 0.3981
  距离: 31.4698
  内存使用: 0.6326
  能量使用: 0.9207
  推理时间: 2.6220秒

批次 41:
  奖励值: 119.5772
  收益率: 0.4005
  距离: 27.1740
  内存使用: 0.6651
  能量使用: 1.0065
  推理时间: 2.5409秒

批次 42:
  奖励值: 118.6268
  收益率: 0.3829
  距离: 34.1754
  内存使用: 0.6392
  能量使用: 0.9318
  推理时间: 2.4516秒

批次 43:
  奖励值: 125.8640
  收益率: 0.4160
  距离: 30.8881
  内存使用: 0.6499
  能量使用: 0.9339
  推理时间: 2.7806秒

批次 44:
  奖励值: 116.3247
  收益率: 0.3868
  距离: 32.7384
  内存使用: 0.6301
  能量使用: 0.9821
  推理时间: 2.4029秒

批次 45:
  奖励值: 116.1398
  收益率: 0.3949
  距离: 30.7942
  内存使用: 0.5563
  能量使用: 0.9700
  推理时间: 2.4242秒

批次 46:
  奖励值: 110.7080
  收益率: 0.3622
  距离: 27.3328
  内存使用: 0.6140
  能量使用: 0.8373
  推理时间: 2.3002秒

批次 47:
  奖励值: 117.5190
  收益率: 0.3874
  距离: 29.0847
  内存使用: 0.6025
  能量使用: 0.8747
  推理时间: 2.5304秒

批次 48:
  奖励值: 116.6943
  收益率: 0.3916
  距离: 28.8871
  内存使用: 0.6158
  能量使用: 0.9084
  推理时间: 2.2762秒

批次 49:
  奖励值: 111.9599
  收益率: 0.3721
  距离: 28.6602
  内存使用: 0.6079
  能量使用: 0.8121
  推理时间: 2.4200秒

批次 50:
  奖励值: 127.5798
  收益率: 0.4135
  距离: 32.7334
  内存使用: 0.6983
  能量使用: 1.0087
  推理时间: 2.6386秒

批次 51:
  奖励值: 116.6456
  收益率: 0.4079
  距离: 30.3500
  内存使用: 0.5817
  能量使用: 0.9771
  推理时间: 2.5247秒

批次 52:
  奖励值: 113.8778
  收益率: 0.3765
  距离: 29.6499
  内存使用: 0.5816
  能量使用: 1.0129
  推理时间: 2.5533秒

批次 53:
  奖励值: 109.2865
  收益率: 0.3791
  距离: 34.7151
  内存使用: 0.5582
  能量使用: 0.9536
  推理时间: 2.4824秒

批次 54:
  奖励值: 120.6729
  收益率: 0.4056
  距离: 29.3974
  内存使用: 0.6644
  能量使用: 0.9923
  推理时间: 2.7127秒

批次 55:
  奖励值: 124.3607
  收益率: 0.4295
  距离: 34.1802
  内存使用: 0.6606
  能量使用: 1.0377
  推理时间: 2.7916秒

批次 56:
  奖励值: 114.7327
  收益率: 0.3904
  距离: 30.7737
  内存使用: 0.6274
  能量使用: 0.9334
  推理时间: 2.5611秒

批次 57:
  奖励值: 126.4283
  收益率: 0.4204
  距离: 33.5611
  内存使用: 0.6477
  能量使用: 0.9345
  推理时间: 2.7628秒

批次 58:
  奖励值: 101.8051
  收益率: 0.3321
  距离: 25.4567
  内存使用: 0.8995
  能量使用: 0.8337
  推理时间: 2.2685秒

批次 59:
  奖励值: 126.9144
  收益率: 0.4177
  距离: 31.7702
  内存使用: 0.7309
  能量使用: 1.0700
  推理时间: 2.7526秒

批次 60:
  奖励值: 116.7103
  收益率: 0.4021
  距离: 27.7704
  内存使用: 0.5585
  能量使用: 0.8505
  推理时间: 2.5459秒

批次 61:
  奖励值: 118.1426
  收益率: 0.3925
  距离: 29.1686
  内存使用: 0.5667
  能量使用: 0.9794
  推理时间: 2.5161秒

批次 62:
  奖励值: 111.4532
  收益率: 0.3757
  距离: 28.7538
  内存使用: 0.6037
  能量使用: 0.9360
  推理时间: 2.4318秒

批次 63:
  奖励值: 116.4015
  收益率: 0.4000
  距离: 32.4336
  内存使用: 0.6204
  能量使用: 0.9431
  推理时间: 2.6173秒

批次 64:
  奖励值: 113.9892
  收益率: 0.3834
  距离: 32.1183
  内存使用: 0.6216
  能量使用: 0.8817
  推理时间: 2.4711秒

批次 65:
  奖励值: 114.1280
  收益率: 0.3928
  距离: 28.4592
  内存使用: 0.5596
  能量使用: 0.9252
  推理时间: 2.4535秒

批次 66:
  奖励值: 118.0543
  收益率: 0.3995
  距离: 29.9556
  内存使用: 0.6404
  能量使用: 0.8955
  推理时间: 2.6370秒

批次 67:
  奖励值: 113.8610
  收益率: 0.3851
  距离: 27.7273
  内存使用: 0.8586
  能量使用: 0.8921
  推理时间: 2.4991秒

批次 68:
  奖励值: 116.5260
  收益率: 0.3842
  距离: 29.2052
  内存使用: 0.6299
  能量使用: 0.9129
  推理时间: 2.5067秒

批次 69:
  奖励值: 121.2402
  收益率: 0.3955
  距离: 29.4332
  内存使用: 0.6561
  能量使用: 1.0304
  推理时间: 2.6412秒

批次 70:
  奖励值: 116.4677
  收益率: 0.3941
  距离: 31.4627
  内存使用: 0.6212
  能量使用: 0.9806
  推理时间: 2.5330秒

批次 71:
  奖励值: 123.0726
  收益率: 0.4166
  距离: 32.5821
  内存使用: 0.6542
  能量使用: 1.0185
  推理时间: 2.6804秒

批次 72:
  奖励值: 116.5250
  收益率: 0.3762
  距离: 25.2882
  内存使用: 0.5964
  能量使用: 0.8655
  推理时间: 2.4740秒

批次 73:
  奖励值: 123.2779
  收益率: 0.4101
  距离: 31.7067
  内存使用: 0.6257
  能量使用: 0.9119
  推理时间: 2.6348秒

批次 74:
  奖励值: 119.8205
  收益率: 0.4089
  距离: 32.9445
  内存使用: 0.6613
  能量使用: 0.9532
  推理时间: 2.6199秒

批次 75:
  奖励值: 119.1465
  收益率: 0.4045
  距离: 30.3838
  内存使用: 0.6850
  能量使用: 1.0381
  推理时间: 2.6169秒

批次 76:
  奖励值: 114.4332
  收益率: 0.3846
  距离: 29.5974
  内存使用: 0.6147
  能量使用: 0.9215
  推理时间: 2.5091秒

批次 77:
  奖励值: 126.9124
  收益率: 0.4127
  距离: 31.3256
  内存使用: 0.6072
  能量使用: 1.0425
  推理时间: 2.7055秒

批次 78:
  奖励值: 134.7018
  收益率: 0.4413
  距离: 35.7845
  内存使用: 0.7051
  能量使用: 1.1067
  推理时间: 2.9187秒

批次 79:
  奖励值: 117.1128
  收益率: 0.3841
  距离: 30.7860
  内存使用: 0.6145
  能量使用: 0.9740
  推理时间: 2.5702秒

批次 80:
  奖励值: 110.9537
  收益率: 0.3749
  距离: 28.7526
  内存使用: 0.6375
  能量使用: 0.9026
  推理时间: 2.4054秒

批次 81:
  奖励值: 122.4330
  收益率: 0.4222
  距离: 30.7004
  内存使用: 0.6856
  能量使用: 0.9481
  推理时间: 2.7235秒

批次 82:
  奖励值: 119.6216
  收益率: 0.4051
  距离: 31.1450
  内存使用: 0.6666
  能量使用: 0.9452
  推理时间: 2.5796秒

批次 83:
  奖励值: 114.2258
  收益率: 0.3939
  距离: 30.2002
  内存使用: 0.6095
  能量使用: 0.9386
  推理时间: 2.4955秒

批次 84:
  奖励值: 109.4511
  收益率: 0.3614
  距离: 29.0679
  内存使用: 0.6950
  能量使用: 0.9349
  推理时间: 2.4472秒

批次 85:
  奖励值: 113.9117
  收益率: 0.3872
  距离: 30.5298
  内存使用: 0.5957
  能量使用: 0.8510
  推理时间: 2.5392秒

批次 86:
  奖励值: 121.5342
  收益率: 0.4001
  距离: 27.3522
  内存使用: 0.6301
  能量使用: 0.9493
  推理时间: 2.6673秒

批次 87:
  奖励值: 124.7270
  收益率: 0.4210
  距离: 35.5994
  内存使用: 0.7152
  能量使用: 1.0313
  推理时间: 2.7200秒

批次 88:
  奖励值: 126.8403
  收益率: 0.4206
  距离: 31.8960
  内存使用: 0.6673
  能量使用: 0.9561
  推理时间: 2.7476秒

批次 89:
  奖励值: 113.6856
  收益率: 0.3849
  距离: 28.9648
  内存使用: 0.5793
  能量使用: 0.8846
  推理时间: 2.4749秒

批次 90:
  奖励值: 114.7667
  收益率: 0.3869
  距离: 30.4820
  内存使用: 0.6427
  能量使用: 0.9419
  推理时间: 2.5075秒

批次 91:
  奖励值: 120.2141
  收益率: 0.3944
  距离: 30.9734
  内存使用: 0.6667
  能量使用: 0.9317
  推理时间: 2.5205秒

批次 92:
  奖励值: 117.6308
  收益率: 0.3836
  距离: 33.9249
  内存使用: 0.8995
  能量使用: 0.9140
  推理时间: 2.5623秒

批次 93:
  奖励值: 120.6754
  收益率: 0.3886
  距离: 30.6689
  内存使用: 0.6388
  能量使用: 0.9415
  推理时间: 2.6085秒

批次 94:
  奖励值: 109.9985
  收益率: 0.3852
  距离: 28.5684
  内存使用: 0.5665
  能量使用: 0.8682
  推理时间: 2.4017秒

批次 95:
  奖励值: 126.9149
  收益率: 0.4284
  距离: 30.7380
  内存使用: 0.7044
  能量使用: 0.9278
  推理时间: 2.6791秒

批次 96:
  奖励值: 120.8831
  收益率: 0.4076
  距离: 30.1676
  内存使用: 0.6398
  能量使用: 1.0263
  推理时间: 2.6352秒

批次 97:
  奖励值: 120.9066
  收益率: 0.4129
  距离: 31.4351
  内存使用: 0.6550
  能量使用: 0.8966
  推理时间: 2.6267秒

批次 98:
  奖励值: 118.0473
  收益率: 0.3973
  距离: 31.8930
  内存使用: 0.6504
  能量使用: 0.9620
  推理时间: 2.5302秒

批次 99:
  奖励值: 126.6245
  收益率: 0.4128
  距离: 32.8499
  内存使用: 0.7052
  能量使用: 0.9960
  推理时间: 2.6838秒

批次 100:
  奖励值: 127.3265
  收益率: 0.4158
  距离: 33.8399
  内存使用: 0.6813
  能量使用: 1.0258
  推理时间: 2.7591秒


==================== 总结 ====================
平均收益率: 0.3961
平均能量使用: 0.9513
平均推理时间: 2.5418秒
