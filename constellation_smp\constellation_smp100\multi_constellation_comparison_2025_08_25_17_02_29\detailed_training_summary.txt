多星座模式训练详细日志
================================================================================

实验时间: 2025-08-27 05:52:32
实验配置:
  问题规模: 100节点, 3卫星
  训练参数: 3轮, 批次64, 学习率0.0001
  数据规模: 训练100000, 验证10000
  使用Transformer: False

COOPERATIVE 模式详细结果:
--------------------------------------------------
模型架构:
  Actor参数数量: 2,211,337
  Critic参数数量: 494,285
  总参数数量: 2,705,622
训练结果:
  最佳验证奖励: 36.188799
测试性能:
  平均收益率: 0.890810
  平均距离: 11.289327
  平均内存使用: 0.015734
  平均功耗: 0.341496
  综合性能评分: 3.084825
文件路径:
  模型保存: constellation_smp\constellation_smp100\multi_constellation_comparison_2025_08_25_17_02_29\constellation_gpnindrnn_cooperative_2025_08_25_17_02_29
  权重文件: actor.pt, critic.pt
  训练日志: log.txt
  测试结果: test_cooperative/

COMPETITIVE 模式详细结果:
--------------------------------------------------
模型架构:
  Actor参数数量: 2,211,337
  Critic参数数量: 494,285
  总参数数量: 2,705,622
训练结果:
  最佳验证奖励: 34.181347
测试性能:
  平均收益率: 0.837978
  平均距离: 10.270835
  平均内存使用: -0.012591
  平均功耗: 0.311448
  综合性能评分: 3.082347
文件路径:
  模型保存: constellation_smp\constellation_smp100\multi_constellation_comparison_2025_08_25_17_02_29\constellation_gpnindrnn_competitive_2025_08_26_05_01_59
  权重文件: actor.pt, critic.pt
  训练日志: log.txt
  测试结果: test_competitive/

HYBRID 模式详细结果:
--------------------------------------------------
模型架构:
  Actor参数数量: 2,408,201
  Critic参数数量: 691,149
  总参数数量: 3,099,350
训练结果:
  最佳验证奖励: 35.824593
测试性能:
  平均收益率: 0.882519
  平均距离: 11.117229
  平均内存使用: 0.012614
  平均功耗: 0.337736
  综合性能评分: 3.091402
文件路径:
  模型保存: constellation_smp\constellation_smp100\multi_constellation_comparison_2025_08_25_17_02_29\constellation_gpnindrnn_hybrid_2025_08_26_17_20_30
  权重文件: actor.pt, critic.pt
  训练日志: log.txt
  测试结果: test_hybrid/

对比分析:
==================================================
最佳奖励模式: cooperative (36.1888)
最佳收益率模式: cooperative (0.8908)
最短距离模式: competitive (10.2708)
最低功耗模式: competitive (0.3114)

推荐使用: cooperative 模式
推荐理由: 在关键性能指标上表现最佳
