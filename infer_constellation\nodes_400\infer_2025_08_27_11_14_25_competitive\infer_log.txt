推理数据数量: 100
每个序列任务数量: 400
星座卫星数量: 3
星座模式: competitive
模型: gpn+indrnn
种子: 12348
内存总量: 0.3
电量总量: 5
检查点路径: constellation_smp/constellation_smp100/multi_constellation_comparison_2025_08_25_17_02_29/constellation_gpnindrnn_competitive_2025_08_26_05_01_59

批次 1:
  奖励值: 97.1820
  收益率: 0.5908
  距离: 25.5331
  内存使用: 0.4938
  能量使用: 0.8092
  推理时间: 2.2249秒

批次 2:
  奖励值: 84.9742
  收益率: 0.5380
  距离: 23.1303
  内存使用: 0.3936
  能量使用: 0.7277
  推理时间: 2.0201秒

批次 3:
  奖励值: 76.9054
  收益率: 0.5087
  距离: 21.3723
  内存使用: 0.3748
  能量使用: 0.6421
  推理时间: 1.8283秒

批次 4:
  奖励值: 91.1247
  收益率: 0.5786
  距离: 24.9652
  内存使用: 0.4544
  能量使用: 0.7310
  推理时间: 2.0908秒

批次 5:
  奖励值: 82.9889
  收益率: 0.5085
  距离: 17.6056
  内存使用: 0.3513
  能量使用: 0.6654
  推理时间: 1.8785秒

批次 6:
  奖励值: 89.5403
  收益率: 0.5371
  距离: 23.9453
  内存使用: 0.4185
  能量使用: 0.7555
  推理时间: 2.0364秒

批次 7:
  奖励值: 85.9570
  收益率: 0.5460
  距离: 21.7790
  内存使用: 0.4511
  能量使用: 0.6559
  推理时间: 1.9785秒

批次 8:
  奖励值: 77.7284
  收益率: 0.4849
  距离: 23.3003
  内存使用: 0.3637
  能量使用: 0.6792
  推理时间: 1.8629秒

批次 9:
  奖励值: 83.4058
  收益率: 0.5242
  距离: 20.3309
  内存使用: 0.3743
  能量使用: 0.6832
  推理时间: 1.9174秒

批次 10:
  奖励值: 82.3968
  收益率: 0.5194
  距离: 24.1443
  内存使用: 0.4159
  能量使用: 0.6462
  推理时间: 1.8953秒

批次 11:
  奖励值: 88.5701
  收益率: 0.5362
  距离: 24.6898
  内存使用: 0.4594
  能量使用: 0.7106
  推理时间: 2.0257秒

批次 12:
  奖励值: 92.0554
  收益率: 0.5735
  距离: 24.7259
  内存使用: 0.3937
  能量使用: 0.7696
  推理时间: 2.1398秒

批次 13:
  奖励值: 81.8976
  收益率: 0.5168
  距离: 22.7766
  内存使用: 0.3740
  能量使用: 0.6542
  推理时间: 1.9749秒

批次 14:
  奖励值: 88.7336
  收益率: 0.5542
  距离: 23.8239
  内存使用: 0.4300
  能量使用: 0.6790
  推理时间: 2.0366秒

批次 15:
  奖励值: 82.3976
  收益率: 0.5262
  距离: 22.7412
  内存使用: 0.3628
  能量使用: 0.7007
  推理时间: 1.8815秒

批次 16:
  奖励值: 79.8030
  收益率: 0.5273
  距离: 22.9317
  内存使用: 0.3705
  能量使用: 0.6919
  推理时间: 1.8543秒

批次 17:
  奖励值: 90.5470
  收益率: 0.5524
  距离: 22.1609
  内存使用: 0.4217
  能量使用: 0.7234
  推理时间: 2.2250秒

批次 18:
  奖励值: 75.1317
  收益率: 0.5119
  距离: 25.4009
  内存使用: 0.6645
  能量使用: 0.6711
  推理时间: 1.8389秒

批次 19:
  奖励值: 88.2594
  收益率: 0.5541
  距离: 28.1361
  内存使用: 0.4195
  能量使用: 0.7256
  推理时间: 2.0657秒

批次 20:
  奖励值: 74.8233
  收益率: 0.4824
  距离: 16.0478
  内存使用: 0.6418
  能量使用: 0.6638
  推理时间: 1.7448秒

批次 21:
  奖励值: 83.1631
  收益率: 0.5368
  距离: 23.7632
  内存使用: 0.3931
  能量使用: 0.7174
  推理时间: 1.9563秒

批次 22:
  奖励值: 89.9191
  收益率: 0.5534
  距离: 23.2696
  内存使用: 0.3932
  能量使用: 0.6990
  推理时间: 2.0584秒

批次 23:
  奖励值: 93.7574
  收益率: 0.5737
  距离: 25.2381
  内存使用: 0.4608
  能量使用: 0.7862
  推理时间: 2.1298秒

批次 24:
  奖励值: 87.2480
  收益率: 0.5421
  距离: 21.1571
  内存使用: 0.4170
  能量使用: 0.7299
  推理时间: 1.9397秒

批次 25:
  奖励值: 81.0203
  收益率: 0.5088
  距离: 21.8064
  内存使用: 0.3113
  能量使用: 0.6419
  推理时间: 1.8665秒

批次 26:
  奖励值: 96.7554
  收益率: 0.6057
  距离: 24.6148
  内存使用: 0.4680
  能量使用: 0.8064
  推理时间: 2.2397秒

批次 27:
  奖励值: 87.1930
  收益率: 0.5302
  距离: 22.9335
  内存使用: 0.3811
  能量使用: 0.6995
  推理时间: 2.0214秒

批次 28:
  奖励值: 82.2164
  收益率: 0.5389
  距离: 23.0239
  内存使用: 0.3720
  能量使用: 0.7712
  推理时间: 1.8887秒

批次 29:
  奖励值: 81.3182
  收益率: 0.5195
  距离: 22.5241
  内存使用: 0.3748
  能量使用: 0.6648
  推理时间: 1.8645秒

批次 30:
  奖励值: 94.2635
  收益率: 0.5696
  距离: 25.4320
  内存使用: 0.4553
  能量使用: 0.7856
  推理时间: 2.1385秒

批次 31:
  奖励值: 86.2597
  收益率: 0.5469
  距离: 23.2575
  内存使用: 0.4795
  能量使用: 0.7436
  推理时间: 1.9826秒

批次 32:
  奖励值: 88.7406
  收益率: 0.5446
  距离: 22.7975
  内存使用: 0.4047
  能量使用: 0.7165
  推理时间: 2.0045秒

批次 33:
  奖励值: 74.1698
  收益率: 0.4844
  距离: 24.2675
  内存使用: 0.3026
  能量使用: 0.6904
  推理时间: 1.7825秒

批次 34:
  奖励值: 78.9772
  收益率: 0.4923
  距离: 19.2400
  内存使用: 0.4029
  能量使用: 0.6027
  推理时间: 1.7875秒

批次 35:
  奖励值: 83.1616
  收益率: 0.5216
  距离: 23.5217
  内存使用: 0.4499
  能量使用: 0.7052
  推理时间: 1.9561秒

批次 36:
  奖励值: 89.3298
  收益率: 0.5597
  距离: 22.6125
  内存使用: 0.3972
  能量使用: 0.7307
  推理时间: 2.0555秒

批次 37:
  奖励值: 85.0255
  收益率: 0.5501
  距离: 21.0145
  内存使用: 0.4211
  能量使用: 0.7134
  推理时间: 1.9621秒

批次 38:
  奖励值: 84.4708
  收益率: 0.5249
  距离: 21.5603
  内存使用: 0.3962
  能量使用: 0.7018
  推理时间: 1.9584秒

批次 39:
  奖励值: 82.8384
  收益率: 0.5416
  距离: 25.9956
  内存使用: 0.3963
  能量使用: 0.7313
  推理时间: 2.0558秒

批次 40:
  奖励值: 85.8431
  收益率: 0.5548
  距离: 23.5842
  内存使用: 0.4231
  能量使用: 0.7066
  推理时间: 2.0209秒

批次 41:
  奖励值: 75.5132
  收益率: 0.4915
  距离: 21.8602
  内存使用: 0.2718
  能量使用: 0.6415
  推理时间: 1.8022秒

批次 42:
  奖励值: 99.3479
  收益率: 0.5956
  距离: 25.0307
  内存使用: 0.4997
  能量使用: 0.7739
  推理时间: 2.2780秒

批次 43:
  奖励值: 86.3941
  收益率: 0.5237
  距离: 22.6699
  内存使用: 0.4637
  能量使用: 0.6782
  推理时间: 1.9864秒

批次 44:
  奖励值: 91.4832
  收益率: 0.5891
  距离: 27.3739
  内存使用: 0.4638
  能量使用: 0.7959
  推理时间: 2.1679秒

批次 45:
  奖励值: 83.7442
  收益率: 0.5326
  距离: 22.3853
  内存使用: 0.3725
  能量使用: 0.7538
  推理时间: 1.9117秒

批次 46:
  奖励值: 96.1339
  收益率: 0.6026
  距离: 27.2038
  内存使用: 0.4712
  能量使用: 0.8572
  推理时间: 2.2288秒

批次 47:
  奖励值: 76.9825
  收益率: 0.4864
  距离: 20.0943
  内存使用: 0.3397
  能量使用: 0.6010
  推理时间: 1.8085秒

批次 48:
  奖励值: 92.1042
  收益率: 0.5717
  距离: 25.1704
  内存使用: 0.4640
  能量使用: 0.7573
  推理时间: 2.1407秒

批次 49:
  奖励值: 80.0448
  收益率: 0.5205
  距离: 21.8719
  内存使用: 0.3867
  能量使用: 0.6517
  推理时间: 1.8365秒

批次 50:
  奖励值: 96.8233
  收益率: 0.5854
  距离: 23.0413
  内存使用: 0.5260
  能量使用: 0.7862
  推理时间: 2.1775秒

批次 51:
  奖励值: 81.9619
  收益率: 0.5171
  距离: 22.8125
  内存使用: 0.3860
  能量使用: 0.6778
  推理时间: 1.9026秒

批次 52:
  奖励值: 82.1885
  收益率: 0.5380
  距离: 22.9933
  内存使用: 0.3623
  能量使用: 0.6674
  推理时间: 1.9375秒

批次 53:
  奖励值: 81.3442
  收益率: 0.5335
  距离: 24.0454
  内存使用: 0.3876
  能量使用: 0.6786
  推理时间: 1.8753秒

批次 54:
  奖励值: 82.5002
  收益率: 0.5091
  距离: 23.0260
  内存使用: 0.4762
  能量使用: 0.6973
  推理时间: 1.8662秒

批次 55:
  奖励值: 89.1144
  收益率: 0.5340
  距离: 23.1283
  内存使用: 0.4599
  能量使用: 0.7830
  推理时间: 2.0591秒

批次 56:
  奖励值: 81.4000
  收益率: 0.5201
  距离: 22.3399
  内存使用: 0.3526
  能量使用: 0.7075
  推理时间: 1.8976秒

批次 57:
  奖励值: 87.3611
  收益率: 0.5501
  距离: 24.1391
  内存使用: 0.4226
  能量使用: 0.7162
  推理时间: 1.9787秒

批次 58:
  奖励值: 80.7464
  收益率: 0.5282
  距离: 23.0739
  内存使用: 0.3793
  能量使用: 0.6539
  推理时间: 1.8440秒

批次 59:
  奖励值: 84.9092
  收益率: 0.5511
  距离: 25.4978
  内存使用: 0.3937
  能量使用: 0.6902
  推理时间: 2.0084秒

批次 60:
  奖励值: 85.0857
  收益率: 0.5329
  距离: 21.7437
  内存使用: 0.3530
  能量使用: 0.7318
  推理时间: 1.9515秒

批次 61:
  奖励值: 86.1864
  收益率: 0.5375
  距离: 24.4886
  内存使用: 0.4251
  能量使用: 0.7134
  推理时间: 1.9531秒

批次 62:
  奖励值: 84.0163
  收益率: 0.5218
  距离: 24.8701
  内存使用: 0.4247
  能量使用: 0.6725
  推理时间: 1.9535秒

批次 63:
  奖励值: 91.9062
  收益率: 0.5541
  距离: 23.9622
  内存使用: 0.4540
  能量使用: 0.7714
  推理时间: 2.1197秒

批次 64:
  奖励值: 75.0740
  收益率: 0.4877
  距离: 20.1610
  内存使用: 0.3104
  能量使用: 0.5806
  推理时间: 1.7223秒

批次 65:
  奖励值: 83.9232
  收益率: 0.5382
  距离: 23.8970
  内存使用: 0.3454
  能量使用: 0.7112
  推理时间: 1.8934秒

批次 66:
  奖励值: 87.5464
  收益率: 0.5393
  距离: 25.6033
  内存使用: 0.3988
  能量使用: 0.7052
  推理时间: 2.0854秒

批次 67:
  奖励值: 87.3840
  收益率: 0.5243
  距离: 24.8523
  内存使用: 0.6815
  能量使用: 0.6982
  推理时间: 2.0655秒

批次 68:
  奖励值: 96.2746
  收益率: 0.5840
  距离: 24.4089
  内存使用: 0.4585
  能量使用: 0.7835
  推理时间: 2.0740秒

批次 69:
  奖励值: 81.6366
  收益率: 0.5296
  距离: 21.4281
  内存使用: 0.3680
  能量使用: 0.7307
  推理时间: 2.0455秒

批次 70:
  奖励值: 85.4731
  收益率: 0.5530
  距离: 25.7621
  内存使用: 0.7358
  能量使用: 0.7559
  推理时间: 2.0783秒

批次 71:
  奖励值: 87.1722
  收益率: 0.5388
  距离: 23.1415
  内存使用: 0.4243
  能量使用: 0.6898
  推理时间: 1.9539秒

批次 72:
  奖励值: 86.0774
  收益率: 0.5065
  距离: 20.8982
  内存使用: 0.4161
  能量使用: 0.7308
  推理时间: 1.7592秒

批次 73:
  奖励值: 89.5989
  收益率: 0.5522
  距离: 23.8640
  内存使用: 0.4209
  能量使用: 0.7174
  推理时间: 2.1104秒

批次 74:
  奖励值: 71.7290
  收益率: 0.4750
  距离: 20.4427
  内存使用: 0.6193
  能量使用: 0.6800
  推理时间: 1.7327秒

批次 75:
  奖励值: 94.2898
  收益率: 0.5749
  距离: 25.1732
  内存使用: 0.4720
  能量使用: 0.7752
  推理时间: 2.0555秒

批次 76:
  奖励值: 89.6545
  收益率: 0.5617
  距离: 26.1016
  内存使用: 0.4274
  能量使用: 0.7620
  推理时间: 1.9775秒

批次 77:
  奖励值: 80.1964
  收益率: 0.4935
  距离: 20.3380
  内存使用: 0.6251
  能量使用: 0.6441
  推理时间: 1.7815秒

批次 78:
  奖励值: 86.6552
  收益率: 0.5273
  距离: 16.8849
  内存使用: 0.3347
  能量使用: 0.6701
  推理时间: 1.8859秒

批次 79:
  奖励值: 87.7566
  收益率: 0.5412
  距离: 24.4675
  内存使用: 0.4609
  能量使用: 0.7585
  推理时间: 2.1291秒

批次 80:
  奖励值: 79.9182
  收益率: 0.5047
  距离: 21.7832
  内存使用: 0.3704
  能量使用: 0.6100
  推理时间: 1.8553秒

批次 81:
  奖励值: 84.5169
  收益率: 0.5246
  距离: 21.6197
  内存使用: 0.4281
  能量使用: 0.7184
  推理时间: 1.9963秒

批次 82:
  奖励值: 83.4934
  收益率: 0.5351
  距离: 22.7873
  内存使用: 0.3979
  能量使用: 0.6279
  推理时间: 1.9771秒

批次 83:
  奖励值: 87.9328
  收益率: 0.5492
  距离: 23.9944
  内存使用: 0.4242
  能量使用: 0.7158
  推理时间: 2.1817秒

批次 84:
  奖励值: 81.5616
  收益率: 0.5258
  距离: 21.0414
  内存使用: 0.3140
  能量使用: 0.7213
  推理时间: 1.6871秒

批次 85:
  奖励值: 79.4015
  收益率: 0.5019
  距离: 22.2272
  内存使用: 0.6773
  能量使用: 0.6925
  推理时间: 1.9312秒

批次 86:
  奖励值: 83.9113
  收益率: 0.5337
  距离: 21.0949
  内存使用: 0.3660
  能量使用: 0.7164
  推理时间: 2.0529秒

批次 87:
  奖励值: 86.0222
  收益率: 0.5308
  距离: 21.4558
  内存使用: 0.4175
  能量使用: 0.6824
  推理时间: 2.0347秒

批次 88:
  奖励值: 86.6347
  收益率: 0.5447
  距离: 25.3642
  内存使用: 0.3840
  能量使用: 0.7825
  推理时间: 2.4990秒

批次 89:
  奖励值: 87.0568
  收益率: 0.5354
  距离: 22.7952
  内存使用: 0.4303
  能量使用: 0.6610
  推理时间: 2.1381秒

批次 90:
  奖励值: 84.2922
  收益率: 0.5224
  距离: 21.9347
  内存使用: 0.3789
  能量使用: 0.7021
  推理时间: 1.9961秒

批次 91:
  奖励值: 80.7615
  收益率: 0.5322
  距离: 25.6678
  内存使用: 0.4419
  能量使用: 0.7673
  推理时间: 2.1375秒

批次 92:
  奖励值: 82.3218
  收益率: 0.5330
  距离: 23.0475
  内存使用: 0.4284
  能量使用: 0.6806
  推理时间: 1.9236秒

批次 93:
  奖励值: 83.6554
  收益率: 0.5255
  距离: 23.3028
  内存使用: 0.3463
  能量使用: 0.6402
  推理时间: 1.9366秒

批次 94:
  奖励值: 92.9264
  收益率: 0.5653
  距离: 25.0190
  内存使用: 0.5147
  能量使用: 0.8134
  推理时间: 2.1210秒

批次 95:
  奖励值: 87.7629
  收益率: 0.5419
  距离: 23.7711
  内存使用: 0.4166
  能量使用: 0.6865
  推理时间: 1.9995秒

批次 96:
  奖励值: 90.7143
  收益率: 0.5849
  距离: 26.8334
  内存使用: 0.4570
  能量使用: 0.6810
  推理时间: 2.1247秒

批次 97:
  奖励值: 90.8001
  收益率: 0.5516
  距离: 22.4433
  内存使用: 0.4304
  能量使用: 0.7262
  推理时间: 2.0318秒

批次 98:
  奖励值: 87.1504
  收益率: 0.5557
  距离: 25.1487
  内存使用: 0.4428
  能量使用: 0.7077
  推理时间: 2.0888秒

批次 99:
  奖励值: 82.7597
  收益率: 0.5334
  距离: 20.3144
  内存使用: 0.4436
  能量使用: 0.7227
  推理时间: 1.9585秒

批次 100:
  奖励值: 97.3258
  收益率: 0.5996
  距离: 24.3753
  内存使用: 0.5361
  能量使用: 0.8370
  推理时间: 2.2521秒


==================== 总结 ====================
平均收益率: 0.5376
平均能量使用: 0.7107
平均推理时间: 1.9915秒
