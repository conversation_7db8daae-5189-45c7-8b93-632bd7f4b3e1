"""
星座任务规划模型推理验证脚本 - 消融实验版本

功能特点:
1. ✅ 支持消融实验训练的多个模型权重批量推理
2. ✅ 自动检测和加载消融实验结果目录
3. ✅ 支持Transformer增强模型的验证
4. ✅ 自动解析模型配置（星座模式、Transformer设置）
5. ✅ 多规模节点测试（100-2000节点）
6. ✅ 详细的性能指标记录和对比
7. ✅ 自动生成消融实验推理对比报告

使用方法:
1. 自动模式: python infer/constellation_infer.py --auto
   - 自动查找最新的消融实验结果
   - 批量推理所有配置的模型

2. 指定目录: python infer/constellation_infer.py --ablation_dir path/to/ablation/results
   - 推理指定消融实验目录中的所有模型

3. 单模型模式: python infer/constellation_infer.py --checkpoint path/to/model
   - 推理单个模型（原始功能）

输出:
- 每个配置的详细推理结果
- 消融实验推理对比报告
- 性能排名和分析
- CSV格式的结果数据

注意事项:
- 确保消融实验目录结构正确
- 脚本会自动检测每个模型的配置
- 支持不同节点规模的性能测试
"""

import sys
import os
import csv
import json
import glob
import argparse
from pathlib import Path

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import torch
from torch.utils.data import DataLoader
from constellation_smp.gpn_constellation import GPNConstellation
from constellation_smp.constellation_smp import ConstellationSMPDataset, reward, render
import time
import datetime
import numpy as np
import os
from hyperparameter import args as hyperparameter_args
from ablation_configs import TRANSFORMER_ABLATION_CONFIGS

# 推理参数
CHECKPOINT_PATH = 'constellation_smp/constellation_smp100/multi_constellation_comparison_2025_08_25_17_02_29/constellation_gpnindrnn_hybrid_2025_08_26_17_20_30'
NUM_SATELLITES = 3
MEMORY_TOTAL = 0.3
POWER_TOTAL = 5
INFER_NUM_DATA = 100
SEED = 12348
BATCH_SIZE = 1
RENDER_RESULTS = True
OUTPUT_DIR = 'infer_constellation'

# 定义要测试的任务节点数量列表
NODE_SCALES = [100, 200, 300, 400, 500, 750, 1000, 1250, 1500, 2000]

# 消融实验推理参数
ABLATION_INFER_NUM_DATA = 50  # 消融实验推理时使用较少数据以节省时间
ABLATION_NODE_SCALES = [100, 200, 500, 1000]  # 消融实验测试的节点规模


class AblationInferenceController:
    """消融实验推理控制器"""

    def __init__(self, ablation_results_dir):
        self.ablation_results_dir = ablation_results_dir
        self.model_configs = []
        self.inference_results = []

    def discover_ablation_models(self):
        """发现消融实验中的所有模型"""
        print("🔍 发现消融实验模型...")

        # 查找所有config_*目录
        config_dirs = []
        for item in os.listdir(self.ablation_results_dir):
            if item.startswith('config_') and os.path.isdir(os.path.join(self.ablation_results_dir, item)):
                config_dirs.append(item)

        print(f"找到 {len(config_dirs)} 个配置目录")

        # 解析每个配置
        for config_dir in config_dirs:
            config_path = os.path.join(self.ablation_results_dir, config_dir)
            actor_path = os.path.join(config_path, 'actor.pt')

            if os.path.exists(actor_path):
                # 解析配置名称
                parts = config_dir.split('_')
                if len(parts) >= 3:
                    config_name = parts[1]  # config_NAME_mode_timestamp
                    constellation_mode = parts[2] if len(parts) > 2 else 'hybrid'

                    # 从消融配置中查找对应的配置
                    transformer_config_info = None
                    for ablation_config in TRANSFORMER_ABLATION_CONFIGS:
                        if ablation_config['name'] == config_name:
                            transformer_config_info = ablation_config
                            break

                    model_info = {
                        'config_name': config_name,
                        'config_dir': config_path,
                        'actor_path': actor_path,
                        'constellation_mode': constellation_mode,
                        'transformer_config_info': transformer_config_info,
                        'use_transformer': transformer_config_info['use_transformer'] if transformer_config_info else False,
                        'transformer_config': transformer_config_info['config'] if transformer_config_info else None,
                        'description': transformer_config_info['description'] if transformer_config_info else f'配置: {config_name}'
                    }

                    self.model_configs.append(model_info)
                    print(f"  ✅ {config_name}: {model_info['description']}")
                else:
                    print(f"  ⚠️ 无法解析配置目录名: {config_dir}")
            else:
                print(f"  ❌ 缺少actor.pt: {config_dir}")

        print(f"\n📊 总计发现 {len(self.model_configs)} 个可用模型")
        return self.model_configs

    def run_single_model_inference(self, model_info, node_scales=None):
        """运行单个模型的推理"""
        if node_scales is None:
            node_scales = ABLATION_NODE_SCALES

        config_name = model_info['config_name']
        print(f"\n{'='*60}")
        print(f"开始推理配置: {config_name}")
        print(f"描述: {model_info['description']}")
        print(f"{'='*60}")

        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

        model_results = {
            'config_name': config_name,
            'description': model_info['description'],
            'use_transformer': model_info['use_transformer'],
            'transformer_config': model_info['transformer_config'],
            'constellation_mode': model_info['constellation_mode'],
            'node_scale_results': {}
        }

        for num_nodes in node_scales:
            print(f"\n🔬 测试节点规模: {num_nodes}")

            try:
                # 创建测试数据
                test_data = ConstellationSMPDataset(
                    num_nodes, ABLATION_INFER_NUM_DATA, SEED,
                    MEMORY_TOTAL, POWER_TOTAL, NUM_SATELLITES
                )
                test_loader = DataLoader(test_data, BATCH_SIZE, False, num_workers=0)

                # 创建模型
                actor = GPNConstellation(
                    hyperparameter_args.static_size,
                    hyperparameter_args.dynamic_size,
                    hyperparameter_args.hidden_size,
                    NUM_SATELLITES,
                    hyperparameter_args.rnn,
                    hyperparameter_args.num_layers,
                    test_data.update_dynamic,
                    test_data.update_mask,
                    num_nodes,
                    hyperparameter_args.dropout,
                    constellation_mode=model_info['constellation_mode'],
                    use_transformer=model_info['use_transformer'],
                    transformer_config=model_info['transformer_config']
                ).to(device)

                # 加载权重
                state_dict = torch.load(model_info['actor_path'], map_location=device, weights_only=False)
                actor.load_state_dict(state_dict)
                actor.eval()

                # 推理
                rewards, revenue_rates, distances, memories, powers, infer_times = [], [], [], [], [], []

                for batch_idx, batch in enumerate(test_loader):
                    if batch_idx >= ABLATION_INFER_NUM_DATA:
                        break

                    static, dynamic, _ = batch
                    static = static.to(device)
                    dynamic = dynamic.to(device)

                    start_time = time.time()

                    with torch.no_grad():
                        tour_indices, satellite_indices, _, _ = actor.forward(static, dynamic)

                    # 计算指标
                    reward_value, revenue_rate, distance, memory, power = reward(
                        static, tour_indices, satellite_indices, model_info['constellation_mode']
                    )

                    end_time = time.time()
                    infer_time = end_time - start_time

                    # 记录结果
                    rewards.append(reward_value.mean().item())
                    revenue_rates.append(revenue_rate.mean().item())
                    distances.append(distance.mean().item())
                    memories.append(memory.mean().item())
                    powers.append(power.mean().item())
                    infer_times.append(infer_time)

                # 计算平均值
                scale_result = {
                    'num_nodes': num_nodes,
                    'avg_reward': np.mean(rewards),
                    'avg_revenue_rate': np.mean(revenue_rates),
                    'avg_distance': np.mean(distances),
                    'avg_memory': np.mean(memories),
                    'avg_power': np.mean(powers),
                    'avg_infer_time': np.mean(infer_times),
                    'std_reward': np.std(rewards),
                    'std_revenue_rate': np.std(revenue_rates)
                }

                model_results['node_scale_results'][num_nodes] = scale_result

                print(f"  节点{num_nodes}: 奖励={scale_result['avg_reward']:.4f}, "
                      f"收益率={scale_result['avg_revenue_rate']:.4f}, "
                      f"推理时间={scale_result['avg_infer_time']:.4f}s")

            except Exception as e:
                print(f"  ❌ 节点{num_nodes}推理失败: {e}")
                continue

        return model_results

    def run_ablation_inference(self, node_scales=None):
        """运行消融实验的批量推理"""
        if not self.model_configs:
            self.discover_ablation_models()

        if not self.model_configs:
            print("❌ 未找到可用的模型配置")
            return []

        print(f"\n🚀 开始消融实验批量推理")
        print(f"模型数量: {len(self.model_configs)}")
        print(f"节点规模: {node_scales or ABLATION_NODE_SCALES}")
        print(f"每规模推理数据量: {ABLATION_INFER_NUM_DATA}")

        # 估算时间
        total_tests = len(self.model_configs) * len(node_scales or ABLATION_NODE_SCALES)
        estimated_time = total_tests * ABLATION_INFER_NUM_DATA * 0.01  # 每个样本约0.01秒
        print(f"预估总时间: {estimated_time:.1f}秒 ({estimated_time/60:.1f}分钟)")

        # 确认开始
        response = input("\n是否开始批量推理? (y/n): ")
        if response.lower() != 'y':
            print("推理已取消")
            return []

        # 运行所有模型的推理
        for i, model_info in enumerate(self.model_configs):
            print(f"\n进度: {i+1}/{len(self.model_configs)}")

            try:
                result = self.run_single_model_inference(model_info, node_scales)
                self.inference_results.append(result)
            except Exception as e:
                print(f"❌ 配置 {model_info['config_name']} 推理失败: {e}")
                continue

        return self.inference_results

    def generate_ablation_inference_report(self, output_dir):
        """生成消融实验推理对比报告"""
        if not self.inference_results:
            print("❌ 没有推理结果可以生成报告")
            return

        print(f"\n📊 生成消融实验推理对比报告...")

        # 创建输出目录
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)

        timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')

        # 1. 生成详细CSV报告
        csv_path = os.path.join(output_dir, f'ablation_inference_detailed_{timestamp}.csv')

        with open(csv_path, 'w', newline='', encoding='utf-8-sig') as f:
            writer = csv.writer(f)

            # 写入表头
            header = ['配置名称', '描述', '使用Transformer', '星座模式', '节点数',
                     '平均奖励', '平均收益率', '平均距离', '平均内存', '平均功耗', '平均推理时间']
            writer.writerow(header)

            # 写入数据
            for result in self.inference_results:
                config_name = result['config_name']
                description = result['description']
                use_transformer = result['use_transformer']
                constellation_mode = result['constellation_mode']

                for num_nodes, scale_result in result['node_scale_results'].items():
                    row = [
                        config_name, description, use_transformer, constellation_mode, num_nodes,
                        f"{scale_result['avg_reward']:.6f}",
                        f"{scale_result['avg_revenue_rate']:.6f}",
                        f"{scale_result['avg_distance']:.6f}",
                        f"{scale_result['avg_memory']:.6f}",
                        f"{scale_result['avg_power']:.6f}",
                        f"{scale_result['avg_infer_time']:.6f}"
                    ]
                    writer.writerow(row)

        # 2. 生成汇总CSV报告
        summary_csv_path = os.path.join(output_dir, f'ablation_inference_summary_{timestamp}.csv')

        with open(summary_csv_path, 'w', newline='', encoding='utf-8-sig') as f:
            writer = csv.writer(f)

            # 计算每个配置在所有节点规模上的平均性能
            header = ['配置名称', '描述', '使用Transformer', '平均奖励', '平均收益率',
                     '平均距离', '平均推理时间', '性能排名']
            writer.writerow(header)

            summary_data = []
            for result in self.inference_results:
                if not result['node_scale_results']:
                    continue

                # 计算所有节点规模的平均值
                all_rewards = [scale_result['avg_reward'] for scale_result in result['node_scale_results'].values()]
                all_revenue_rates = [scale_result['avg_revenue_rate'] for scale_result in result['node_scale_results'].values()]
                all_distances = [scale_result['avg_distance'] for scale_result in result['node_scale_results'].values()]
                all_infer_times = [scale_result['avg_infer_time'] for scale_result in result['node_scale_results'].values()]

                summary_data.append({
                    'config_name': result['config_name'],
                    'description': result['description'],
                    'use_transformer': result['use_transformer'],
                    'avg_reward': np.mean(all_rewards),
                    'avg_revenue_rate': np.mean(all_revenue_rates),
                    'avg_distance': np.mean(all_distances),
                    'avg_infer_time': np.mean(all_infer_times)
                })

            # 按平均奖励排序
            summary_data.sort(key=lambda x: x['avg_reward'], reverse=True)

            # 添加排名并写入CSV
            for i, data in enumerate(summary_data):
                row = [
                    data['config_name'], data['description'], data['use_transformer'],
                    f"{data['avg_reward']:.6f}", f"{data['avg_revenue_rate']:.6f}",
                    f"{data['avg_distance']:.6f}", f"{data['avg_infer_time']:.6f}",
                    i + 1
                ]
                writer.writerow(row)

        # 3. 生成文本分析报告
        report_path = os.path.join(output_dir, f'ablation_inference_analysis_{timestamp}.txt')

        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("消融实验推理性能分析报告\n")
            f.write("=" * 60 + "\n\n")

            f.write(f"分析时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"测试配置数量: {len(self.inference_results)}\n")
            f.write(f"测试节点规模: {ABLATION_NODE_SCALES}\n")
            f.write(f"每规模推理数据量: {ABLATION_INFER_NUM_DATA}\n\n")

            f.write("配置性能排名 (按平均奖励):\n")
            f.write("-" * 50 + "\n")

            for i, data in enumerate(summary_data):
                f.write(f"\n{i+1}. {data['config_name'].upper()}:\n")
                f.write(f"   描述: {data['description']}\n")
                f.write(f"   使用Transformer: {'是' if data['use_transformer'] else '否'}\n")
                f.write(f"   平均奖励: {data['avg_reward']:.6f}\n")
                f.write(f"   平均收益率: {data['avg_revenue_rate']:.6f}\n")
                f.write(f"   平均距离: {data['avg_distance']:.6f}\n")
                f.write(f"   平均推理时间: {data['avg_infer_time']:.6f}秒\n")

            # 性能分析
            if len(summary_data) > 1:
                best_config = summary_data[0]
                baseline_config = next((d for d in summary_data if d['config_name'] == 'baseline_no_transformer'), None)

                f.write(f"\n性能分析:\n")
                f.write("=" * 30 + "\n")
                f.write(f"最佳配置: {best_config['config_name']}\n")

                if baseline_config:
                    reward_improvement = ((best_config['avg_reward'] - baseline_config['avg_reward']) / baseline_config['avg_reward']) * 100
                    revenue_improvement = ((best_config['avg_revenue_rate'] - baseline_config['avg_revenue_rate']) / baseline_config['avg_revenue_rate']) * 100
                    time_change = ((best_config['avg_infer_time'] - baseline_config['avg_infer_time']) / baseline_config['avg_infer_time']) * 100

                    f.write(f"相对基线提升:\n")
                    f.write(f"  奖励提升: {reward_improvement:+.2f}%\n")
                    f.write(f"  收益率提升: {revenue_improvement:+.2f}%\n")
                    f.write(f"  推理时间变化: {time_change:+.2f}%\n")

        print(f"✅ 消融实验推理报告已生成:")
        print(f"  详细结果: {csv_path}")
        print(f"  汇总结果: {summary_csv_path}")
        print(f"  分析报告: {report_path}")

        return csv_path, summary_csv_path, report_path


def find_latest_ablation_results():
    """查找最新的消融实验结果目录"""
    base_dir = os.path.join(hyperparameter_args.task, f"{hyperparameter_args.task}{hyperparameter_args.num_nodes}")

    if not os.path.exists(base_dir):
        return None

    # 查找消融实验目录
    ablation_dirs = []
    for item in os.listdir(base_dir):
        if item.startswith('transformer_ablation_study_'):
            full_path = os.path.join(base_dir, item)
            if os.path.isdir(full_path):
                ablation_dirs.append(full_path)

    if ablation_dirs:
        # 按修改时间排序，返回最新的
        latest_dir = max(ablation_dirs, key=os.path.getmtime)
        return latest_dir

    return None


def find_all_ablation_results():
    """查找所有消融实验结果目录"""
    base_dir = os.path.join(hyperparameter_args.task, f"{hyperparameter_args.task}{hyperparameter_args.num_nodes}")

    if not os.path.exists(base_dir):
        return []

    ablation_dirs = []
    for item in os.listdir(base_dir):
        if item.startswith('transformer_ablation_study_'):
            full_path = os.path.join(base_dir, item)
            if os.path.isdir(full_path):
                # 检查是否包含配置目录
                config_dirs = [d for d in os.listdir(full_path) if d.startswith('config_')]
                if config_dirs:
                    ablation_dirs.append({
                        'path': full_path,
                        'name': item,
                        'config_count': len(config_dirs),
                        'mtime': os.path.getmtime(full_path)
                    })

    # 按修改时间排序
    ablation_dirs.sort(key=lambda x: x['mtime'], reverse=True)
    return ablation_dirs


def select_ablation_experiment():
    """交互式选择消融实验"""
    print("🔍 查找消融实验结果...")

    all_experiments = find_all_ablation_results()

    if not all_experiments:
        print("❌ 未找到任何消融实验结果")
        print("请先运行消融实验:")
        print("  python transformer_ablation_study.py")
        print("  python quick_ablation.py")
        return None

    print(f"找到 {len(all_experiments)} 个消融实验:")
    print()

    for i, exp in enumerate(all_experiments):
        mtime_str = datetime.datetime.fromtimestamp(exp['mtime']).strftime('%Y-%m-%d %H:%M:%S')
        print(f"  {i+1}. {exp['name']}")
        print(f"     配置数量: {exp['config_count']}")
        print(f"     修改时间: {mtime_str}")
        print()

    # 用户选择
    while True:
        try:
            choice = input(f"请选择实验 (1-{len(all_experiments)}, 或按Enter选择最新): ").strip()

            if choice == "":
                selected_exp = all_experiments[0]
                break

            choice_idx = int(choice) - 1
            if 0 <= choice_idx < len(all_experiments):
                selected_exp = all_experiments[choice_idx]
                break
            else:
                print(f"请输入1-{len(all_experiments)}之间的数字")

        except ValueError:
            print("请输入有效的数字")
        except KeyboardInterrupt:
            print("\n操作已取消")
            return None

    print(f"✅ 选择了实验: {selected_exp['name']}")
    return selected_exp['path']


def get_model_config_from_checkpoint(checkpoint_path):
    """
    从检查点目录的log.txt文件中读取并解析模型配置。
    返回: (constellation_mode, use_transformer, transformer_config)
    """
    log_file_path = os.path.join(checkpoint_path, 'log.txt')
    default_mode = 'hybrid'  # 默认模式
    default_transformer = False
    default_transformer_config = None

    if not os.path.exists(log_file_path):
        print(f"警告: 在 {checkpoint_path} 中未找到 log.txt。使用默认配置。")
        return default_mode, default_transformer, default_transformer_config
    
    constellation_mode = default_mode
    use_transformer = default_transformer
    transformer_config = {}

    try:
        # 尝试多种编码方式
        encodings = ['utf-8', 'gbk', 'gb2312', 'latin-1']
        content = None

        for encoding in encodings:
            try:
                with open(log_file_path, 'r', encoding=encoding) as f:
                    content = f.read()
                    break
            except UnicodeDecodeError:
                continue

        if content is None:
            raise Exception("无法使用任何编码方式读取日志文件")

        for line in content.split('\n'):
            for line in f:
                line = line.strip()

                # 解析星座模式
                if '星座模式' in line or 'constellation mode' in line.lower() or 'constellation_mode:' in line:
                    parts = line.split(':')
                    if len(parts) > 1:
                        mode = parts[1].strip().lower()
                        if mode in ['cooperative', 'competitive', 'hybrid']:
                            constellation_mode = mode
                            print(f"从检查点自动检测到星座模式: {mode}")

                # 解析Transformer配置
                elif 'use_transformer:' in line:
                    parts = line.split(':')
                    if len(parts) > 1:
                        use_transformer = parts[1].strip().lower() == 'true'

                elif 'transformer_layers:' in line:
                    parts = line.split(':')
                    if len(parts) > 1:
                        transformer_config['num_layers'] = int(parts[1].strip())

                elif 'transformer_heads:' in line:
                    parts = line.split(':')
                    if len(parts) > 1:
                        transformer_config['num_heads'] = int(parts[1].strip())

                elif 'transformer_d_model:' in line:
                    parts = line.split(':')
                    if len(parts) > 1:
                        transformer_config['d_model'] = int(parts[1].strip())

                elif 'transformer_d_ff:' in line:
                    parts = line.split(':')
                    if len(parts) > 1:
                        transformer_config['d_ff'] = int(parts[1].strip())

                elif 'transformer_dropout:' in line:
                    parts = line.split(':')
                    if len(parts) > 1:
                        transformer_config['dropout'] = float(parts[1].strip())

                elif 'transformer_activation:' in line:
                    parts = line.split(':')
                    if len(parts) > 1:
                        transformer_config['activation'] = parts[1].strip()

    except Exception as e:
        print(f"读取或解析 log.txt 时出错: {e}。使用默认配置。")

    # 如果启用了Transformer但缺少配置，使用默认值
    if use_transformer and not transformer_config:
        transformer_config = {
            'd_model': hyperparameter_args.hidden_size,
            'num_heads': 8,
            'd_ff': hyperparameter_args.hidden_size * 4,
            'num_layers': 4,
            'max_len': 5000,
            'dropout': 0.1,
            'activation': 'gelu'
        }
        print("使用默认Transformer配置")

    print(f"检测到配置: 星座模式={constellation_mode}, 使用Transformer={use_transformer}")
    if use_transformer:
        print(f"Transformer配置: {transformer_config}")

    return constellation_mode, use_transformer, transformer_config


def detect_transformer_from_weights(checkpoint_path):
    """
    通过检查权重文件中的键来检测是否使用了Transformer
    """
    actor_path = os.path.join(checkpoint_path, 'actor.pt')

    if not os.path.exists(actor_path):
        return False, None

    try:
        # 加载权重文件 - 注意：这里需要weights_only=False来检测Transformer配置
        state_dict = torch.load(actor_path, map_location='cpu', weights_only=False)

        # 检查是否包含Transformer相关的键
        transformer_keys = [
            'constellation_encoder.transformer.input_projection.weight',
            'constellation_encoder.transformer.transformer.layers.0.self_attention.w_q.weight',
            'constellation_encoder.transformer_projection.weight'
        ]

        has_transformer = any(key in state_dict for key in transformer_keys)

        if has_transformer:
            # 尝试从权重文件推断Transformer配置
            transformer_config = {}

            # 推断层数
            layer_count = 0
            while f'constellation_encoder.transformer.transformer.layers.{layer_count}.self_attention.w_q.weight' in state_dict:
                layer_count += 1
            transformer_config['num_layers'] = layer_count

            # 推断模型维度
            if 'constellation_encoder.transformer.input_projection.weight' in state_dict:
                d_model = state_dict['constellation_encoder.transformer.input_projection.weight'].shape[0]
                transformer_config['d_model'] = d_model

            # 推断注意力头数
            if 'constellation_encoder.transformer.transformer.layers.0.self_attention.w_q.weight' in state_dict:
                q_weight = state_dict['constellation_encoder.transformer.transformer.layers.0.self_attention.w_q.weight']
                d_model = q_weight.shape[0]
                # 假设每个头的维度是64
                num_heads = d_model // 64 if d_model % 64 == 0 else 8
                transformer_config['num_heads'] = num_heads

            # 推断前馈网络维度
            if 'constellation_encoder.transformer.transformer.layers.0.feed_forward.linear1.weight' in state_dict:
                d_ff = state_dict['constellation_encoder.transformer.transformer.layers.0.feed_forward.linear1.weight'].shape[0]
                transformer_config['d_ff'] = d_ff

            # 设置默认值
            transformer_config.setdefault('dropout', 0.1)
            transformer_config.setdefault('activation', 'gelu')
            transformer_config.setdefault('max_len', 5000)

            print(f"从权重文件检测到Transformer配置: {transformer_config}")
            return True, transformer_config

        return False, None

    except Exception as e:
        print(f"检测权重文件时出错: {e}")
        return False, None

def run_inference_for_nodes(num_nodes, device, constellation_mode, use_transformer=False, transformer_config=None):
    """
    为指定数量的节点运行推理
    """
    print(f"\n{'='*20} 开始为 {num_nodes} 个节点在 {constellation_mode} 模式下运行推理 {'='*20}")
    
    # 创建输出目录
    scale_output_dir = os.path.join(OUTPUT_DIR, f'nodes_{num_nodes}')
    if not os.path.exists(scale_output_dir):
        os.makedirs(scale_output_dir)

    # 设置静态和动态大小
    static_size = getattr(hyperparameter_args, 'static_size', 9)
    dynamic_size = getattr(hyperparameter_args, 'dynamic_size', 7)
    
    # 创建测试数据集
    try:
        test_data = ConstellationSMPDataset(
            num_nodes,
            INFER_NUM_DATA,
            SEED,
            MEMORY_TOTAL,
            POWER_TOTAL,
            NUM_SATELLITES
        )
        test_loader = DataLoader(test_data, BATCH_SIZE, False, num_workers=0)
    except Exception as e:
        print(f"创建数据集时出错 (节点数: {num_nodes}): {e}")
        return
    
    # 创建模型
    try:
        actor = GPNConstellation(
            static_size,
            dynamic_size,
            hyperparameter_args.hidden_size,
            NUM_SATELLITES,
            hyperparameter_args.rnn,
            hyperparameter_args.num_layers,
            test_data.update_dynamic,
            test_data.update_mask,
            num_nodes,
            hyperparameter_args.dropout,
            constellation_mode=constellation_mode,  # 显式传递星座模式
            use_transformer=use_transformer,        # 支持Transformer
            transformer_config=transformer_config   # Transformer配置
        ).to(device)

        # 打印模型信息
        total_params = sum(p.numel() for p in actor.parameters())
        print(f"模型参数总数: {total_params:,}")
        if use_transformer:
            print("✓ 使用Transformer增强模型")
        else:
            print("✓ 使用传统模型")

    except Exception as e:
        print(f"创建模型时出错 (节点数: {num_nodes}): {e}")
        return
    
    # 加载模型权重
    try:
        actor_path = os.path.join(CHECKPOINT_PATH, 'actor.pt')
        # 注意：为了兼容性，这里暂时使用weights_only=False
        # 在生产环境中，建议使用weights_only=True并处理相关兼容性问题
        state_dict = torch.load(actor_path, map_location=device, weights_only=False)
        actor.load_state_dict(state_dict)
        actor.eval()
        print(f"✓ 成功加载模型权重: {actor_path}")
    except Exception as e:
        print(f"加载模型权重时出错 (节点数: {num_nodes}): {e}")
        print(f"尝试的路径: {actor_path}")
        return
    
    # 创建保存目录
    now = '%s' % datetime.datetime.now().strftime('%Y_%m_%d_%H_%M_%S')
    save_dir = os.path.join(scale_output_dir, f'infer_{now}_{constellation_mode}')
    if not os.path.exists(save_dir):
        os.makedirs(save_dir)
    
    # 创建详细结果的CSV文件
    detailed_csv_path = os.path.join(save_dir, 'inference_details.csv')
    
    # 创建日志文件
    infer_log_path = os.path.join(save_dir, 'infer_log.txt')
    with open(infer_log_path, 'a+', encoding='utf-8') as f:
        f.write(f'推理数据数量: {INFER_NUM_DATA}\n')
        f.write(f'每个序列任务数量: {num_nodes}\n')
        f.write(f'星座卫星数量: {NUM_SATELLITES}\n')
        f.write(f'星座模式: {constellation_mode}\n')
        f.write(f'模型: {hyperparameter_args.model}+{hyperparameter_args.rnn}\n')
        f.write(f'种子: {SEED}\n')
        f.write(f'内存总量: {MEMORY_TOTAL}\n')
        f.write(f'电量总量: {POWER_TOTAL}\n')
        f.write(f'检查点路径: {CHECKPOINT_PATH}\n\n')
    
    # 评估指标
    plan_revenue_rates, infer_times, powers = [], [], []
    
    # 使用 'with' 语句确保CSV文件被正确关闭
    with open(detailed_csv_path, 'w', newline='', encoding='utf-8') as csvfile:
        csv_writer = csv.writer(csvfile)
        # 写入CSV表头
        csv_writer.writerow(['batch_idx', 'reward', 'revenue_rate', 'distance', 'memory_usage', 'power_usage', 'inference_time_s'])

        # 遍历测试数据
        for batch_idx, batch in enumerate(test_loader):
            if batch_idx >= INFER_NUM_DATA:
                break
                
            static, dynamic, _ = batch
            static = static.to(device)
            dynamic = dynamic.to(device)
            
            # 记录原始数据以计算最终指标
            static_record = static.clone()
            
            # 打印任务总量信息
            if batch_idx == 0:
                print(f'总收益: {torch.sum(static[:, 4, :], dim=1).item()}')
                print(f'总内存需求: {torch.sum(static[:, 5, :], dim=1).item()}')
                print(f'总电量需求: {torch.sum(static[:, 6, :], dim=1).item()}')
                print(f'每颗卫星内存供应: {MEMORY_TOTAL}')
                print(f'每颗卫星电量供应: {POWER_TOTAL}')
                
            # 推理时间开始
            start_time = time.time()
            
            # 模型推理
            try:
                with torch.no_grad():
                    tour_indices, satellite_indices, _, _ = actor.forward(static, dynamic)
                
                # 计算奖励和其他指标，传入星座模式
                reward_value, revenue_rate, distance, memory, power = reward(static, tour_indices, satellite_indices, constellation_mode)
            except Exception as e:
                print(f"批次 {batch_idx} 推理过程中出错 (节点数: {num_nodes}): {e}")
                continue
            
            # 推理时间结束
            end_time = time.time()
            infer_time = end_time - start_time
            
            # 提取标量值以便记录
            reward_val = reward_value.mean().item()
            revenue_rate_val = revenue_rate.mean().item()
            distance_val = distance.mean().item()
            memory_val = memory.mean().item()
            power_val = power.mean().item()
            
            # 保存指标
            plan_revenue_rates.append(revenue_rate_val)
            powers.append(power_val)
            infer_times.append(infer_time)
            
            # 打印每个批次的结果
            print(f'批次 {batch_idx}/{INFER_NUM_DATA}:')
            print(f'  奖励值: {reward_val:.4f}')
            print(f'  收益率: {revenue_rate_val:.4f}')
            print(f'  距离: {distance_val:.4f}')
            print(f'  内存使用: {memory_val:.4f}')
            print(f'  能量使用: {power_val:.4f}')
            print(f'  推理时间: {infer_time:.4f}秒')
            
            # 记录到日志
            with open(infer_log_path, 'a+', encoding='utf-8') as f:
                f.write(f'批次 {batch_idx + 1}:\n')
                f.write(f'  奖励值: {reward_val:.4f}\n')
                f.write(f'  收益率: {revenue_rate_val:.4f}\n')
                f.write(f'  距离: {distance_val:.4f}\n')
                f.write(f'  内存使用: {memory_val:.4f}\n')
                f.write(f'  能量使用: {power_val:.4f}\n')
                f.write(f'  推理时间: {infer_time:.4f}秒\n\n')

            # 将详细结果写入CSV
            csv_writer.writerow([batch_idx, f'{reward_val:.4f}', f'{revenue_rate_val:.4f}', f'{distance_val:.4f}', f'{memory_val:.4f}', f'{power_val:.4f}', f'{infer_time:.4f}'])

            # 渲染结果
            if RENDER_RESULTS:
                img_save_path = os.path.join(save_dir, f'batch_{batch_idx}_nodes_{num_nodes}.png')
                # 始终渲染批次中的第一个样本（索引为0），并传入卫星数量
                render(static, tour_indices, satellite_indices, img_save_path, NUM_SATELLITES, 0)
            
    # 计算平均指标
    avg_revenue_rate = np.mean(plan_revenue_rates) if plan_revenue_rates else 0
    avg_power = np.mean(powers) if powers else 0
    avg_infer_time = np.mean(infer_times) if infer_times else 0
    
    # 打印和记录总结
    summary = (
        f'推理完成 (节点数: {num_nodes}):\n'
        f'  平均收益率: {avg_revenue_rate:.4f}\n'
        f'  平均能量使用: {avg_power:.4f}\n'
        f'  平均推理时间: {avg_infer_time:.4f}秒'
    )
    print(summary)
    with open(infer_log_path, 'a+', encoding='utf-8') as f:
        f.write('\n' + '='*20 + ' 总结 ' + '='*20 + '\n')
        f.write(f'平均收益率: {avg_revenue_rate:.4f}\n')
        f.write(f'平均能量使用: {avg_power:.4f}\n')
        f.write(f'平均推理时间: {avg_infer_time:.4f}秒\n')

    # 确保在出错时也能返回
    if not plan_revenue_rates:
        return 0, 0, 0
        
    return avg_revenue_rate, avg_power, avg_infer_time

def main_single_model():
    """原始的单模型推理功能"""
    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")

    # 确保检查点路径存在
    if not os.path.exists(CHECKPOINT_PATH) or not os.path.exists(os.path.join(CHECKPOINT_PATH, 'actor.pt')):
        print(f"错误: 检查点路径或actor.pt不存在于: {CHECKPOINT_PATH}")
        return

    # 确保根输出目录存在
    if not os.path.exists(OUTPUT_DIR):
        os.makedirs(OUTPUT_DIR)

    # 从检查点自动检测模型配置
    constellation_mode, use_transformer, transformer_config = get_model_config_from_checkpoint(CHECKPOINT_PATH)

    # 通过权重文件进一步检测Transformer配置（更可靠）
    weight_has_transformer, weight_transformer_config = detect_transformer_from_weights(CHECKPOINT_PATH)

    if weight_has_transformer:
        print("✓ 权重文件检测到Transformer组件，覆盖日志配置")
        use_transformer = True
        if weight_transformer_config:
            transformer_config = weight_transformer_config
    elif use_transformer:
        print("⚠ 日志显示使用Transformer但权重文件中未找到，可能配置有误")
        use_transformer = False
        transformer_config = None

    print(f"最终配置: 星座模式={constellation_mode}, 使用Transformer={use_transformer}")
    if use_transformer and transformer_config:
        print(f"Transformer配置: {transformer_config}")

    # 创建总结果的CSV文件 - 修复权限问题
    timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
    summary_csv_path = os.path.join(OUTPUT_DIR, f'inference_summary_{timestamp}.csv')

    try:
        with open(summary_csv_path, 'w', newline='', encoding='utf-8') as summary_file:
            summary_writer = csv.writer(summary_file)
            summary_writer.writerow(['num_nodes', 'constellation_mode', 'use_transformer', 'avg_revenue_rate', 'avg_power_usage', 'avg_inference_time_s'])

            # 为每个节点规模运行推理
            for num_nodes in NODE_SCALES:
                result = run_inference_for_nodes(num_nodes, device, constellation_mode, use_transformer, transformer_config)
                if result is None:
                    print(f"节点数 {num_nodes} 的推理运行失败，跳过...")
                    continue
                avg_revenue_rate, avg_power, avg_infer_time = result
                # 将总结结果写入CSV - 添加Transformer信息
                summary_writer.writerow([num_nodes, constellation_mode, use_transformer, f'{avg_revenue_rate:.4f}', f'{avg_power:.4f}', f'{avg_infer_time:.4f}'])

        print(f"✓ 推理总结已保存到: {summary_csv_path}")

    except PermissionError as e:
        print(f"⚠️ 无法写入CSV文件 {summary_csv_path}: {e}")
        print("可能原因: 文件被其他程序打开，或者没有写入权限")
        print("推理结果已在控制台显示，请手动记录结果")


def main():
    """主函数 - 支持消融实验推理"""
    parser = argparse.ArgumentParser(description='星座任务规划模型推理验证脚本 - 消融实验版本')
    parser.add_argument('--auto', action='store_true',
                       help='自动查找最新的消融实验结果并进行批量推理')
    parser.add_argument('--ablation_dir', type=str,
                       help='指定消融实验结果目录路径')
    parser.add_argument('--checkpoint', type=str,
                       help='单模型推理：指定模型检查点路径')
    parser.add_argument('--interactive', action='store_true',
                       help='交互式选择消融实验')
    parser.add_argument('--node_scales', type=str, default='100,200,500,1000',
                       help='测试的节点规模，用逗号分隔 (默认: 100,200,500,1000)')

    args = parser.parse_args()

    print("🚀 星座任务规划模型推理验证脚本 - 消融实验版本")
    print("=" * 70)

    # 解析节点规模
    try:
        node_scales = [int(x.strip()) for x in args.node_scales.split(',')]
    except ValueError:
        print("❌ 节点规模参数格式错误，使用默认值")
        node_scales = ABLATION_NODE_SCALES

    print(f"测试节点规模: {node_scales}")

    # 根据参数选择运行模式
    if args.checkpoint:
        # 单模型推理模式
        print("📋 运行模式: 单模型推理")
        global CHECKPOINT_PATH
        CHECKPOINT_PATH = args.checkpoint
        main_single_model()

    elif args.ablation_dir:
        # 指定消融实验目录
        print("📋 运行模式: 指定消融实验目录推理")
        if not os.path.exists(args.ablation_dir):
            print(f"❌ 指定的消融实验目录不存在: {args.ablation_dir}")
            return

        controller = AblationInferenceController(args.ablation_dir)
        controller.run_ablation_inference(node_scales)
        controller.generate_ablation_inference_report(OUTPUT_DIR)

    elif args.auto:
        # 自动查找最新消融实验
        print("📋 运行模式: 自动查找最新消融实验")
        latest_ablation_dir = find_latest_ablation_results()

        if not latest_ablation_dir:
            print("❌ 未找到消融实验结果")
            print("请先运行消融实验:")
            print("  python transformer_ablation_study.py")
            print("  python quick_ablation.py")
            return

        print(f"✅ 找到最新消融实验: {os.path.basename(latest_ablation_dir)}")

        controller = AblationInferenceController(latest_ablation_dir)
        controller.run_ablation_inference(node_scales)
        controller.generate_ablation_inference_report(OUTPUT_DIR)

    elif args.interactive:
        # 交互式选择消融实验
        print("📋 运行模式: 交互式选择消融实验")
        selected_dir = select_ablation_experiment()

        if not selected_dir:
            return

        controller = AblationInferenceController(selected_dir)
        controller.run_ablation_inference(node_scales)
        controller.generate_ablation_inference_report(OUTPUT_DIR)

    else:
        # 默认：显示使用帮助
        print("📋 使用方法:")
        print("  1. 自动推理最新消融实验:")
        print("     python infer/constellation_infer.py --auto")
        print()
        print("  2. 交互式选择消融实验:")
        print("     python infer/constellation_infer.py --interactive")
        print()
        print("  3. 指定消融实验目录:")
        print("     python infer/constellation_infer.py --ablation_dir path/to/ablation/results")
        print()
        print("  4. 单模型推理:")
        print("     python infer/constellation_infer.py --checkpoint path/to/model")
        print()
        print("  5. 自定义节点规模:")
        print("     python infer/constellation_infer.py --auto --node_scales 100,500,1000")
        print()
        print("💡 推荐使用 --auto 或 --interactive 模式进行消融实验推理")

        # 检查是否有消融实验结果
        latest_ablation_dir = find_latest_ablation_results()
        if latest_ablation_dir:
            print(f"\n✅ 检测到最新消融实验: {os.path.basename(latest_ablation_dir)}")
            print("可以直接运行: python infer/constellation_infer.py --auto")
        else:
            print(f"\n⚠️ 未检测到消融实验结果")
            print("请先运行消融实验生成模型权重")

if __name__ == '__main__':
    main() 