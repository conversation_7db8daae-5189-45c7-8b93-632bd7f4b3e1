推理数据数量: 100
每个序列任务数量: 200
星座卫星数量: 3
星座模式: competitive
模型: gpn+indrnn
种子: 12348
内存总量: 0.3
电量总量: 5
检查点路径: constellation_smp/constellation_smp100/multi_constellation_comparison_2025_08_25_17_02_29/constellation_gpnindrnn_competitive_2025_08_26_05_01_59

批次 1:
  奖励值: 49.6309
  收益率: 0.6387
  距离: 14.3545
  内存使用: 0.1681
  能量使用: 0.4155
  推理时间: 1.2468秒

批次 2:
  奖励值: 54.5251
  收益率: 0.6677
  距离: 14.1310
  内存使用: 0.1702
  能量使用: 0.4486
  推理时间: 1.1892秒

批次 3:
  奖励值: 47.8900
  收益率: 0.6239
  距离: 14.4997
  内存使用: 0.1276
  能量使用: 0.3926
  推理时间: 1.2013秒

批次 4:
  奖励值: 55.7845
  收益率: 0.6755
  距离: 15.5749
  内存使用: 0.2074
  能量使用: 0.4488
  推理时间: 1.3887秒

批次 5:
  奖励值: 50.2157
  收益率: 0.6495
  距离: 14.9989
  内存使用: 0.1664
  能量使用: 0.4033
  推理时间: 1.2604秒

批次 6:
  奖励值: 57.5454
  收益率: 0.6798
  距离: 15.4481
  内存使用: 0.1857
  能量使用: 0.4754
  推理时间: 1.4103秒

批次 7:
  奖励值: 60.3256
  收益率: 0.7007
  距离: 17.3365
  内存使用: 0.1402
  能量使用: 0.4721
  推理时间: 1.3886秒

批次 8:
  奖励值: 55.9525
  收益率: 0.6788
  距离: 14.6320
  内存使用: 0.1976
  能量使用: 0.4654
  推理时间: 1.3690秒

批次 9:
  奖励值: 52.7801
  收益率: 0.6493
  距离: 13.4757
  内存使用: 0.1339
  能量使用: 0.4302
  推理时间: 1.3343秒

批次 10:
  奖励值: 53.4623
  收益率: 0.6227
  距离: 13.3110
  内存使用: 0.1747
  能量使用: 0.3896
  推理时间: 1.2514秒

批次 11:
  奖励值: 49.1056
  收益率: 0.6067
  距离: 13.3124
  内存使用: 0.0996
  能量使用: 0.3768
  推理时间: 1.2627秒

批次 12:
  奖励值: 52.8529
  收益率: 0.6597
  距离: 14.2600
  内存使用: 0.1812
  能量使用: 0.4530
  推理时间: 1.3159秒

批次 13:
  奖励值: 53.9597
  收益率: 0.6813
  距离: 15.8523
  内存使用: 0.1653
  能量使用: 0.4629
  推理时间: 1.4610秒

批次 14:
  奖励值: 62.1976
  收益率: 0.7169
  距离: 14.7827
  内存使用: 0.1787
  能量使用: 0.4435
  推理时间: 1.4625秒

批次 15:
  奖励值: 53.2511
  收益率: 0.6696
  距离: 16.4466
  内存使用: 0.1932
  能量使用: 0.4579
  推理时间: 1.4768秒

批次 16:
  奖励值: 53.9557
  收益率: 0.6610
  距离: 15.4761
  内存使用: 0.1547
  能量使用: 0.4578
  推理时间: 1.3792秒

批次 17:
  奖励值: 57.8141
  收益率: 0.6805
  距离: 14.5091
  内存使用: 0.2079
  能量使用: 0.4548
  推理时间: 1.4196秒

批次 18:
  奖励值: 54.0012
  收益率: 0.6379
  距离: 13.9637
  内存使用: 0.1766
  能量使用: 0.4573
  推理时间: 1.3435秒

批次 19:
  奖励值: 53.3439
  收益率: 0.6648
  距离: 12.9144
  内存使用: 0.1298
  能量使用: 0.4681
  推理时间: 1.4049秒

批次 20:
  奖励值: 53.0231
  收益率: 0.6602
  距离: 16.1556
  内存使用: 0.1206
  能量使用: 0.4776
  推理时间: 1.2599秒

批次 21:
  奖励值: 59.3291
  收益率: 0.6890
  距离: 14.0688
  内存使用: 0.2188
  能量使用: 0.4543
  推理时间: 1.3045秒

批次 22:
  奖励值: 49.1707
  收益率: 0.6080
  距离: 13.8129
  内存使用: 0.1092
  能量使用: 0.3365
  推理时间: 1.0953秒

批次 23:
  奖励值: 53.4844
  收益率: 0.6685
  距离: 16.5457
  内存使用: 0.2001
  能量使用: 0.4855
  推理时间: 1.3381秒

批次 24:
  奖励值: 54.6123
  收益率: 0.6723
  距离: 16.4882
  内存使用: 0.2173
  能量使用: 0.4699
  推理时间: 1.3588秒

批次 25:
  奖励值: 51.9510
  收益率: 0.6538
  距离: 14.0681
  内存使用: 0.1576
  能量使用: 0.4722
  推理时间: 1.2890秒

批次 26:
  奖励值: 54.3801
  收益率: 0.6695
  距离: 15.4165
  内存使用: 0.1576
  能量使用: 0.4657
  推理时间: 1.3180秒

批次 27:
  奖励值: 48.1448
  收益率: 0.6083
  距离: 14.9295
  内存使用: 0.1504
  能量使用: 0.4305
  推理时间: 1.2238秒

批次 28:
  奖励值: 49.7065
  收益率: 0.6498
  距离: 15.7432
  内存使用: 0.1453
  能量使用: 0.4985
  推理时间: 1.3690秒

批次 29:
  奖励值: 58.0001
  收益率: 0.6792
  距离: 16.7564
  内存使用: 0.1762
  能量使用: 0.4673
  推理时间: 1.5205秒

批次 30:
  奖励值: 52.8319
  收益率: 0.6578
  距离: 12.1538
  内存使用: 0.1771
  能量使用: 0.4054
  推理时间: 1.3235秒

批次 31:
  奖励值: 51.3675
  收益率: 0.6443
  距离: 13.6596
  内存使用: 0.1240
  能量使用: 0.4813
  推理时间: 1.3377秒

批次 32:
  奖励值: 51.7536
  收益率: 0.6622
  距离: 15.4977
  内存使用: 0.1480
  能量使用: 0.4471
  推理时间: 1.3794秒

批次 33:
  奖励值: 52.0621
  收益率: 0.6469
  距离: 13.3237
  内存使用: 0.1219
  能量使用: 0.4301
  推理时间: 1.3008秒

批次 34:
  奖励值: 51.6848
  收益率: 0.6433
  距离: 13.7207
  内存使用: 0.1390
  能量使用: 0.3707
  推理时间: 1.2590秒

批次 35:
  奖励值: 52.5419
  收益率: 0.6608
  距离: 14.2890
  内存使用: 0.1266
  能量使用: 0.5006
  推理时间: 1.3830秒

批次 36:
  奖励值: 52.1791
  收益率: 0.6708
  距离: 13.6247
  内存使用: 0.1291
  能量使用: 0.4879
  推理时间: 1.3542秒

批次 37:
  奖励值: 52.3095
  收益率: 0.6744
  距离: 17.3875
  内存使用: 0.1034
  能量使用: 0.4901
  推理时间: 1.3900秒

批次 38:
  奖励值: 50.8352
  收益率: 0.6317
  距离: 13.3688
  内存使用: 0.1366
  能量使用: 0.4241
  推理时间: 1.3157秒

批次 39:
  奖励值: 51.5181
  收益率: 0.6656
  距离: 16.9220
  内存使用: 0.1811
  能量使用: 0.4608
  推理时间: 1.4163秒

批次 40:
  奖励值: 49.0332
  收益率: 0.6257
  距离: 13.3564
  内存使用: 0.1267
  能量使用: 0.4505
  推理时间: 1.2385秒

批次 41:
  奖励值: 51.1937
  收益率: 0.6595
  距离: 13.3091
  内存使用: 0.0588
  能量使用: 0.4447
  推理时间: 1.3113秒

批次 42:
  奖励值: 54.6312
  收益率: 0.6584
  距离: 11.9564
  内存使用: 0.1492
  能量使用: 0.4320
  推理时间: 1.3374秒

批次 43:
  奖励值: 52.9441
  收益率: 0.6555
  距离: 14.5348
  内存使用: 0.1372
  能量使用: 0.4398
  推理时间: 1.3705秒

批次 44:
  奖励值: 53.3110
  收益率: 0.6773
  距离: 16.1984
  内存使用: 0.1591
  能量使用: 0.4205
  推理时间: 1.4125秒

批次 45:
  奖励值: 51.3014
  收益率: 0.6429
  距离: 14.2527
  内存使用: 0.1365
  能量使用: 0.4079
  推理时间: 1.3115秒

批次 46:
  奖励值: 55.3285
  收益率: 0.6791
  距离: 15.7432
  内存使用: 0.1317
  能量使用: 0.4681
  推理时间: 1.4335秒

批次 47:
  奖励值: 46.3443
  收益率: 0.5982
  距离: 14.6365
  内存使用: 0.0499
  能量使用: 0.4251
  推理时间: 1.1818秒

批次 48:
  奖励值: 49.9254
  收益率: 0.6415
  距离: 13.4022
  内存使用: 0.0858
  能量使用: 0.4611
  推理时间: 1.2086秒

批次 49:
  奖励值: 47.0980
  收益率: 0.6002
  距离: 13.0961
  内存使用: 0.0709
  能量使用: 0.4371
  推理时间: 1.1342秒

批次 50:
  奖励值: 50.9298
  收益率: 0.6415
  距离: 14.9306
  内存使用: 0.1633
  能量使用: 0.4464
  推理时间: 1.2544秒

批次 51:
  奖励值: 52.3528
  收益率: 0.6366
  距离: 13.1504
  内存使用: 0.1218
  能量使用: 0.4221
  推理时间: 1.2219秒

批次 52:
  奖励值: 54.6964
  收益率: 0.6617
  距离: 14.3799
  内存使用: 0.1756
  能量使用: 0.4518
  推理时间: 1.2821秒

批次 53:
  奖励值: 63.2757
  收益率: 0.7124
  距离: 15.8643
  内存使用: 0.2338
  能量使用: 0.5505
  推理时间: 1.4423秒

批次 54:
  奖励值: 53.1759
  收益率: 0.6696
  距离: 14.9959
  内存使用: 0.1595
  能量使用: 0.4929
  推理时间: 1.3114秒

批次 55:
  奖励值: 50.9143
  收益率: 0.6480
  距离: 13.6188
  内存使用: 0.1542
  能量使用: 0.3984
  推理时间: 1.2176秒

批次 56:
  奖励值: 55.7668
  收益率: 0.6485
  距离: 13.6414
  内存使用: 0.1552
  能量使用: 0.4698
  推理时间: 1.4132秒

批次 57:
  奖励值: 50.1080
  收益率: 0.6342
  距离: 15.0094
  内存使用: 0.1374
  能量使用: 0.4373
  推理时间: 1.2892秒

批次 58:
  奖励值: 53.2156
  收益率: 0.6710
  距离: 16.9220
  内存使用: 0.1406
  能量使用: 0.5061
  推理时间: 1.3030秒

批次 59:
  奖励值: 51.6626
  收益率: 0.6641
  距离: 16.7047
  内存使用: 0.1226
  能量使用: 0.4475
  推理时间: 1.3225秒

批次 60:
  奖励值: 51.8259
  收益率: 0.6413
  距离: 13.8009
  内存使用: 0.1157
  能量使用: 0.4316
  推理时间: 1.2995秒

批次 61:
  奖励值: 53.0324
  收益率: 0.6595
  距离: 15.7006
  内存使用: 0.1677
  能量使用: 0.4670
  推理时间: 1.3565秒

批次 62:
  奖励值: 50.5776
  收益率: 0.6303
  距离: 14.8842
  内存使用: 0.1337
  能量使用: 0.4269
  推理时间: 1.2931秒

批次 63:
  奖励值: 47.2631
  收益率: 0.6217
  距离: 12.7631
  内存使用: 0.0819
  能量使用: 0.4289
  推理时间: 1.2525秒

批次 64:
  奖励值: 56.0346
  收益率: 0.6946
  距离: 15.0643
  内存使用: 0.2006
  能量使用: 0.4660
  推理时间: 1.4343秒

批次 65:
  奖励值: 53.5070
  收益率: 0.6551
  距离: 17.4018
  内存使用: 0.1626
  能量使用: 0.4217
  推理时间: 1.4479秒

批次 66:
  奖励值: 48.8262
  收益率: 0.6127
  距离: 15.4051
  内存使用: 0.1361
  能量使用: 0.4064
  推理时间: 1.2802秒

批次 67:
  奖励值: 49.7314
  收益率: 0.6164
  距离: 12.8524
  内存使用: 0.1064
  能量使用: 0.3783
  推理时间: 1.4575秒

批次 68:
  奖励值: 52.9106
  收益率: 0.6707
  距离: 15.7552
  内存使用: 0.1444
  能量使用: 0.4792
  推理时间: 1.3308秒

批次 69:
  奖励值: 49.3373
  收益率: 0.6139
  距离: 13.9832
  内存使用: 0.0934
  能量使用: 0.4489
  推理时间: 1.2744秒

批次 70:
  奖励值: 51.2642
  收益率: 0.6443
  距离: 15.6962
  内存使用: 0.1418
  能量使用: 0.4336
  推理时间: 1.4150秒

批次 71:
  奖励值: 48.8955
  收益率: 0.6041
  距离: 12.5355
  内存使用: 0.0808
  能量使用: 0.3927
  推理时间: 1.2349秒

批次 72:
  奖励值: 47.2095
  收益率: 0.6162
  距离: 13.2409
  内存使用: 0.1192
  能量使用: 0.4210
  推理时间: 1.2826秒

批次 73:
  奖励值: 55.3931
  收益率: 0.6913
  距离: 15.7579
  内存使用: 0.1576
  能量使用: 0.4982
  推理时间: 1.4377秒

批次 74:
  奖励值: 50.7881
  收益率: 0.6362
  距离: 14.6235
  内存使用: 0.1162
  能量使用: 0.4840
  推理时间: 1.3153秒

批次 75:
  奖励值: 51.5504
  收益率: 0.6332
  距离: 14.7159
  内存使用: 0.1537
  能量使用: 0.4297
  推理时间: 1.3271秒

批次 76:
  奖励值: 50.6451
  收益率: 0.6343
  距离: 14.4163
  内存使用: 0.0972
  能量使用: 0.3964
  推理时间: 1.2109秒

批次 77:
  奖励值: 48.9379
  收益率: 0.6196
  距离: 13.9682
  内存使用: 0.1149
  能量使用: 0.4411
  推理时间: 1.2720秒

批次 78:
  奖励值: 50.6218
  收益率: 0.6300
  距离: 10.6392
  内存使用: 0.0879
  能量使用: 0.4293
  推理时间: 1.2348秒

批次 79:
  奖励值: 48.7108
  收益率: 0.6243
  距离: 13.9909
  内存使用: 0.1456
  能量使用: 0.4418
  推理时间: 1.2173秒

批次 80:
  奖励值: 57.3878
  收益率: 0.6712
  距离: 13.9500
  内存使用: 0.1764
  能量使用: 0.4981
  推理时间: 1.6848秒

批次 81:
  奖励值: 50.8650
  收益率: 0.6697
  距离: 14.9989
  内存使用: 0.4464
  能量使用: 0.4247
  推理时间: 1.2439秒

批次 82:
  奖励值: 51.5304
  收益率: 0.6360
  距离: 15.1574
  内存使用: 0.1701
  能量使用: 0.4117
  推理时间: 1.1948秒

批次 83:
  奖励值: 57.1418
  收益率: 0.6755
  距离: 12.0923
  内存使用: 0.1407
  能量使用: 0.4835
  推理时间: 1.2203秒

批次 84:
  奖励值: 51.9747
  收益率: 0.6633
  距离: 14.9145
  内存使用: 0.0928
  能量使用: 0.4433
  推理时间: 1.1781秒

批次 85:
  奖励值: 49.8400
  收益率: 0.6360
  距离: 12.9456
  内存使用: 0.1166
  能量使用: 0.4579
  推理时间: 1.1590秒

批次 86:
  奖励值: 45.3635
  收益率: 0.6143
  距离: 11.9277
  内存使用: 0.1656
  能量使用: 0.3798
  推理时间: 1.3711秒

批次 87:
  奖励值: 54.7795
  收益率: 0.6791
  距离: 16.8209
  内存使用: 0.1683
  能量使用: 0.4518
  推理时间: 2.2601秒

批次 88:
  奖励值: 51.1627
  收益率: 0.6317
  距离: 15.0700
  内存使用: 0.1433
  能量使用: 0.4612
  推理时间: 1.2547秒

批次 89:
  奖励值: 52.1012
  收益率: 0.6446
  距离: 13.7056
  内存使用: 0.1508
  能量使用: 0.5413
  推理时间: 1.5273秒

批次 90:
  奖励值: 55.6031
  收益率: 0.6642
  距离: 14.8024
  内存使用: 0.1408
  能量使用: 0.4506
  推理时间: 1.3794秒

批次 91:
  奖励值: 51.5725
  收益率: 0.6503
  距离: 12.6999
  内存使用: 0.1139
  能量使用: 0.4411
  推理时间: 1.2662秒

批次 92:
  奖励值: 52.7357
  收益率: 0.6630
  距离: 14.7865
  内存使用: 0.1301
  能量使用: 0.4120
  推理时间: 1.2721秒

批次 93:
  奖励值: 50.6697
  收益率: 0.6452
  距离: 14.0527
  内存使用: 0.1410
  能量使用: 0.4669
  推理时间: 1.2935秒

批次 94:
  奖励值: 51.8137
  收益率: 0.6602
  距离: 17.3948
  内存使用: 0.1580
  能量使用: 0.4198
  推理时间: 1.2542秒

批次 95:
  奖励值: 53.2775
  收益率: 0.6388
  距离: 13.4568
  内存使用: 0.1309
  能量使用: 0.4575
  推理时间: 1.2885秒

批次 96:
  奖励值: 55.3846
  收益率: 0.6764
  距离: 13.1768
  内存使用: 0.1651
  能量使用: 0.4889
  推理时间: 1.3480秒

批次 97:
  奖励值: 58.1122
  收益率: 0.7037
  距离: 17.4493
  内存使用: 0.2187
  能量使用: 0.5076
  推理时间: 1.4291秒

批次 98:
  奖励值: 46.9690
  收益率: 0.6047
  距离: 13.6981
  内存使用: 0.0335
  能量使用: 0.3305
  推理时间: 1.1742秒

批次 99:
  奖励值: 55.8734
  收益率: 0.6680
  距离: 16.8541
  内存使用: 0.1494
  能量使用: 0.4497
  推理时间: 1.3122秒

批次 100:
  奖励值: 47.1527
  收益率: 0.6165
  距离: 14.2143
  内存使用: 0.0743
  能量使用: 0.4060
  推理时间: 1.1435秒


==================== 总结 ====================
平均收益率: 0.6517
平均能量使用: 0.4455
平均推理时间: 1.3273秒
