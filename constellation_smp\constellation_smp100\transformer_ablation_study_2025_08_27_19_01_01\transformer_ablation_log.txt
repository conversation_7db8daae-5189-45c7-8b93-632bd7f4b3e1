Transformer配置消融实验
================================================================================
实验时间: 2025_08_27_19_01_01
设备: cuda
问题规模: 100节点, 3卫星
训练配置: 3轮, 批次大小64
将运行 8 个配置:
  1. baseline_no_transformer: 基线配置，不使用Transformer
  2. lightweight: 轻量级配置: 1层, 2头, 128维
  3. default: 默认配置: 2层, 4头, 256维
  4. deep: 深层配置: 3层, 8头, 512维
  5. ultra_deep: 超深层配置: 4层, 8头, 256维
  6. wide: 宽模型配置: 2层, 16头, 512维
  7. high_dropout: 高dropout配置: dropout=0.3
  8. relu_activation: ReLU激活函数配置
使用星座模式: hybrid

开始运行配置 1/8

================================================================================
开始运行配置: baseline_no_transformer
描述: 基线配置，不使用Transformer
================================================================================
基线配置: 不使用Transformer
模型信息:
  Actor参数数量: 2,408,201
  Critic参数数量: 691,149
  总参数数量: 3,099,350
  Epoch 1/3: 训练奖励=30.0941, 验证奖励=31.6401, 损失=11.5073
  Epoch 2/3: 训练奖励=32.1066, 验证奖励=34.4002, 损失=4.1403
  Epoch 3/3: 训练奖励=32.5310, 验证奖励=35.8246, 损失=3.7702

配置 baseline_no_transformer 完整结果:
==================================================
  最佳验证奖励: 35.8246
  平均收益率: 0.8825
  平均距离: 11.1172
  平均内存使用: 0.0126
  平均功耗: 0.3377
  总参数: 3,099,350
  训练时长: 44534.3秒
==================================================

开始运行配置 2/8

================================================================================
开始运行配置: lightweight
描述: 轻量级配置: 1层, 2头, 128维
================================================================================
Transformer配置: {'d_model': 128, 'num_heads': 2, 'd_ff': 256, 'num_layers': 1, 'max_len': 5000, 'dropout': 0.1, 'activation': 'gelu'}
模型信息:
  Actor参数数量: 2,675,209
  Critic参数数量: 691,149
  总参数数量: 3,366,358
