推理数据数量: 100
每个序列任务数量: 1500
星座卫星数量: 3
星座模式: cooperative
模型: gpn+indrnn
种子: 12348
内存总量: 0.3
电量总量: 5
检查点路径: constellation_smp/constellation_smp100/multi_constellation_comparison_2025_08_25_17_02_29/constellation_gpnindrnn_cooperative_2025_08_25_17_02_29

批次 1:
  奖励值: 116.1346
  收益率: 0.1960
  距离: 29.3769
  内存使用: 0.6517
  能量使用: 0.9023
  推理时间: 2.5771秒

批次 2:
  奖励值: 113.5197
  收益率: 0.1926
  距离: 31.4343
  内存使用: 0.6035
  能量使用: 0.9228
  推理时间: 2.3948秒

批次 3:
  奖励值: 133.1046
  收益率: 0.2305
  距离: 37.3629
  内存使用: 0.6605
  能量使用: 1.1675
  推理时间: 3.0306秒

批次 4:
  奖励值: 120.2921
  收益率: 0.2006
  距离: 33.3913
  内存使用: 0.6552
  能量使用: 0.9692
  推理时间: 2.6234秒

批次 5:
  奖励值: 121.7046
  收益率: 0.2045
  距离: 30.6052
  内存使用: 0.6595
  能量使用: 0.9260
  推理时间: 2.5803秒

批次 6:
  奖励值: 126.0604
  收益率: 0.2055
  距离: 31.0110
  内存使用: 0.6918
  能量使用: 0.9765
  推理时间: 2.8244秒

批次 7:
  奖励值: 123.3031
  收益率: 0.2082
  距离: 36.6709
  内存使用: 0.7302
  能量使用: 0.9926
  推理时间: 2.8133秒

批次 8:
  奖励值: 126.4282
  收益率: 0.2148
  距离: 36.5468
  内存使用: 0.7792
  能量使用: 1.0175
  推理时间: 2.8059秒

批次 9:
  奖励值: 134.6262
  收益率: 0.2288
  距离: 38.0358
  内存使用: 0.7617
  能量使用: 1.1502
  推理时间: 2.9721秒

批次 10:
  奖励值: 112.4697
  收益率: 0.1921
  距离: 30.2244
  内存使用: 0.8994
  能量使用: 0.9367
  推理时间: 2.6707秒

批次 11:
  奖励值: 124.5201
  收益率: 0.2100
  距离: 32.2706
  内存使用: 0.6789
  能量使用: 1.0103
  推理时间: 2.7625秒

批次 12:
  奖励值: 126.3255
  收益率: 0.2091
  距离: 31.3115
  内存使用: 0.6478
  能量使用: 0.9897
  推理时间: 2.9194秒

批次 13:
  奖励值: 121.5364
  收益率: 0.2046
  距离: 31.1522
  内存使用: 0.6289
  能量使用: 0.9461
  推理时间: 2.8078秒

批次 14:
  奖励值: 118.5624
  收益率: 0.1978
  距离: 29.8029
  内存使用: 0.8999
  能量使用: 0.9725
  推理时间: 2.5970秒

批次 15:
  奖励值: 131.4192
  收益率: 0.2208
  距离: 33.7228
  内存使用: 0.6924
  能量使用: 1.1466
  推理时间: 3.0799秒

批次 16:
  奖励值: 138.1817
  收益率: 0.2362
  距离: 38.1411
  内存使用: 0.7746
  能量使用: 1.1208
  推理时间: 3.2159秒

批次 17:
  奖励值: 133.9849
  收益率: 0.2217
  距离: 32.7616
  内存使用: 0.6677
  能量使用: 1.0008
  推理时间: 3.0364秒

批次 18:
  奖励值: 134.4451
  收益率: 0.2313
  距离: 34.3105
  内存使用: 0.7932
  能量使用: 1.0061
  推理时间: 2.9231秒

批次 19:
  奖励值: 120.2244
  收益率: 0.2018
  距离: 29.5563
  内存使用: 0.6376
  能量使用: 0.9573
  推理时间: 2.7994秒

批次 20:
  奖励值: 131.6390
  收益率: 0.2227
  距离: 34.9394
  内存使用: 0.7169
  能量使用: 1.0657
  推理时间: 3.0820秒

批次 21:
  奖励值: 135.3553
  收益率: 0.2266
  距离: 33.2996
  内存使用: 0.7575
  能量使用: 1.0324
  推理时间: 2.8668秒

批次 22:
  奖励值: 137.3763
  收益率: 0.2249
  距离: 34.1034
  内存使用: 0.8090
  能量使用: 1.1349
  推理时间: 2.9504秒

批次 23:
  奖励值: 133.8585
  收益率: 0.2228
  距离: 35.3354
  内存使用: 0.6999
  能量使用: 1.0476
  推理时间: 3.0738秒

批次 24:
  奖励值: 147.3344
  收益率: 0.2575
  距离: 43.6624
  内存使用: 0.8179
  能量使用: 1.2510
  推理时间: 3.2146秒

批次 25:
  奖励值: 131.3434
  收益率: 0.2166
  距离: 33.5359
  内存使用: 0.7020
  能量使用: 1.0752
  推理时间: 2.8029秒

批次 26:
  奖励值: 133.7355
  收益率: 0.2236
  距离: 33.6299
  内存使用: 0.8100
  能量使用: 1.0891
  推理时间: 2.9974秒

批次 27:
  奖励值: 131.4888
  收益率: 0.2235
  距离: 35.7416
  内存使用: 0.6827
  能量使用: 0.9979
  推理时间: 2.9448秒

批次 28:
  奖励值: 129.9878
  收益率: 0.2196
  距离: 35.4400
  内存使用: 0.6943
  能量使用: 1.0902
  推理时间: 2.9951秒

批次 29:
  奖励值: 133.7736
  收益率: 0.2305
  距离: 34.3641
  内存使用: 0.6826
  能量使用: 1.0060
  推理时间: 2.8978秒

批次 30:
  奖励值: 129.4815
  收益率: 0.2202
  距离: 31.6680
  内存使用: 0.7319
  能量使用: 1.0370
  推理时间: 2.8282秒

批次 31:
  奖励值: 125.2271
  收益率: 0.2137
  距离: 32.8928
  内存使用: 0.6585
  能量使用: 0.9946
  推理时间: 2.8183秒

批次 32:
  奖励值: 125.7650
  收益率: 0.2077
  距离: 32.4014
  内存使用: 0.6564
  能量使用: 1.0122
  推理时间: 2.7493秒

批次 33:
  奖励值: 144.3611
  收益率: 0.2434
  距离: 36.6584
  内存使用: 0.8310
  能量使用: 1.1884
  推理时间: 3.1174秒

批次 34:
  奖励值: 135.6822
  收益率: 0.2302
  距离: 37.2239
  内存使用: 0.7545
  能量使用: 1.0572
  推理时间: 3.1454秒

批次 35:
  奖励值: 116.3426
  收益率: 0.1971
  距离: 32.6124
  内存使用: 0.8996
  能量使用: 1.0027
  推理时间: 2.6452秒

批次 36:
  奖励值: 140.7862
  收益率: 0.2391
  距离: 37.3609
  内存使用: 0.8258
  能量使用: 1.2407
  推理时间: 3.2884秒

批次 37:
  奖励值: 129.3221
  收益率: 0.2145
  距离: 35.0026
  内存使用: 0.7233
  能量使用: 1.0298
  推理时间: 2.6940秒

批次 38:
  奖励值: 116.1727
  收益率: 0.1961
  距离: 34.6031
  内存使用: 0.6569
  能量使用: 0.9946
  推理时间: 2.5719秒

批次 39:
  奖励值: 112.6886
  收益率: 0.1904
  距离: 30.4768
  内存使用: 0.8999
  能量使用: 0.8461
  推理时间: 2.5274秒

批次 40:
  奖励值: 133.6035
  收益率: 0.2244
  距离: 35.7588
  内存使用: 0.7493
  能量使用: 1.0849
  推理时间: 3.0615秒

批次 41:
  奖励值: 137.3149
  收益率: 0.2312
  距离: 39.6339
  内存使用: 0.7833
  能量使用: 1.1529
  推理时间: 3.0341秒

批次 42:
  奖励值: 116.5227
  收益率: 0.1934
  距离: 27.8983
  内存使用: 0.6945
  能量使用: 0.9618
  推理时间: 2.5175秒

批次 43:
  奖励值: 116.8848
  收益率: 0.2023
  距离: 34.4392
  内存使用: 0.8998
  能量使用: 0.8913
  推理时间: 2.7247秒

批次 44:
  奖励值: 133.0407
  收益率: 0.2260
  距离: 34.1232
  内存使用: 0.7084
  能量使用: 1.0871
  推理时间: 2.8870秒

批次 45:
  奖励值: 121.8795
  收益率: 0.2117
  距离: 34.7543
  内存使用: 0.8943
  能量使用: 0.9924
  推理时间: 2.7032秒

批次 46:
  奖励值: 138.6665
  收益率: 0.2278
  距离: 34.1499
  内存使用: 0.7632
  能量使用: 1.1539
  推理时间: 3.0102秒

批次 47:
  奖励值: 139.6916
  收益率: 0.2381
  距离: 36.2843
  内存使用: 0.7265
  能量使用: 1.2059
  推理时间: 3.0379秒

批次 48:
  奖励值: 149.9927
  收益率: 0.2496
  距离: 35.7889
  内存使用: 0.8841
  能量使用: 1.1416
  推理时间: 3.2312秒

批次 49:
  奖励值: 118.8761
  收益率: 0.1975
  距离: 30.8615
  内存使用: 0.6438
  能量使用: 0.9424
  推理时间: 2.5927秒

批次 50:
  奖励值: 113.9471
  收益率: 0.1949
  距离: 30.6468
  内存使用: 0.8996
  能量使用: 0.9592
  推理时间: 2.5878秒

批次 51:
  奖励值: 136.1518
  收益率: 0.2307
  距离: 35.3942
  内存使用: 0.7720
  能量使用: 1.1303
  推理时间: 2.9889秒

批次 52:
  奖励值: 128.5871
  收益率: 0.2180
  距离: 29.5544
  内存使用: 0.7018
  能量使用: 0.9695
  推理时间: 2.8232秒

批次 53:
  奖励值: 123.5701
  收益率: 0.2043
  距离: 34.3555
  内存使用: 0.6374
  能量使用: 1.0667
  推理时间: 2.6505秒

批次 54:
  奖励值: 118.1049
  收益率: 0.1978
  距离: 31.7504
  内存使用: 0.6410
  能量使用: 0.9988
  推理时间: 2.6207秒

批次 55:
  奖励值: 127.5163
  收益率: 0.2119
  距离: 32.3260
  内存使用: 0.6762
  能量使用: 0.9652
  推理时间: 2.7595秒

批次 56:
  奖励值: 141.1810
  收益率: 0.2410
  距离: 38.4793
  内存使用: 0.8097
  能量使用: 1.1490
  推理时间: 3.1856秒

批次 57:
  奖励值: 131.8781
  收益率: 0.2227
  距离: 34.8804
  内存使用: 0.7210
  能量使用: 1.0424
  推理时间: 2.8800秒

批次 58:
  奖励值: 137.0299
  收益率: 0.2272
  距离: 33.6700
  内存使用: 0.8154
  能量使用: 1.0548
  推理时间: 2.9435秒

批次 59:
  奖励值: 130.8349
  收益率: 0.2179
  距离: 33.9915
  内存使用: 0.7113
  能量使用: 1.0274
  推理时间: 2.8488秒

批次 60:
  奖励值: 135.0475
  收益率: 0.2250
  距离: 36.5289
  内存使用: 0.7426
  能量使用: 1.0335
  推理时间: 2.9570秒

批次 61:
  奖励值: 137.0497
  收益率: 0.2331
  距离: 36.4391
  内存使用: 0.7390
  能量使用: 1.1225
  推理时间: 3.0125秒

批次 62:
  奖励值: 130.6183
  收益率: 0.2211
  距离: 33.4693
  内存使用: 0.6974
  能量使用: 0.9965
  推理时间: 2.8052秒

批次 63:
  奖励值: 136.6795
  收益率: 0.2299
  距离: 35.5777
  内存使用: 0.7689
  能量使用: 1.0943
  推理时间: 2.9808秒

批次 64:
  奖励值: 131.2893
  收益率: 0.2244
  距离: 35.8216
  内存使用: 0.7126
  能量使用: 1.0872
  推理时间: 2.9650秒

批次 65:
  奖励值: 136.9135
  收益率: 0.2264
  距离: 33.3345
  内存使用: 0.7540
  能量使用: 1.0843
  推理时间: 2.9757秒

批次 66:
  奖励值: 141.2495
  收益率: 0.2325
  距离: 37.6467
  内存使用: 0.8112
  能量使用: 1.2430
  推理时间: 3.1076秒

批次 67:
  奖励值: 141.1558
  收益率: 0.2348
  距离: 32.8539
  内存使用: 0.7531
  能量使用: 1.0813
  推理时间: 3.0209秒

批次 68:
  奖励值: 133.6864
  收益率: 0.2218
  距离: 34.0937
  内存使用: 0.7436
  能量使用: 0.9899
  推理时间: 2.9021秒

批次 69:
  奖励值: 125.7660
  收益率: 0.2164
  距离: 36.7400
  内存使用: 0.6869
  能量使用: 1.0411
  推理时间: 2.8808秒

批次 70:
  奖励值: 133.4210
  收益率: 0.2258
  距离: 34.7139
  内存使用: 0.7427
  能量使用: 1.1013
  推理时间: 2.9137秒

批次 71:
  奖励值: 119.2445
  收益率: 0.2020
  距离: 31.1265
  内存使用: 0.6220
  能量使用: 0.9626
  推理时间: 2.6068秒

批次 72:
  奖励值: 114.6923
  收益率: 0.1919
  距离: 30.2021
  内存使用: 0.8998
  能量使用: 0.9134
  推理时间: 2.5729秒

批次 73:
  奖励值: 114.5741
  收益率: 0.1931
  距离: 28.7845
  内存使用: 0.6101
  能量使用: 0.8573
  推理时间: 2.5596秒

批次 74:
  奖励值: 125.7455
  收益率: 0.2141
  距离: 36.5130
  内存使用: 0.7433
  能量使用: 1.0527
  推理时间: 2.7185秒

批次 75:
  奖励值: 114.4517
  收益率: 0.1956
  距离: 32.8083
  内存使用: 0.6336
  能量使用: 0.8547
  推理时间: 2.4949秒

批次 76:
  奖励值: 127.5684
  收益率: 0.2108
  距离: 31.2219
  内存使用: 0.6717
  能量使用: 0.9696
  推理时间: 2.8002秒

批次 77:
  奖励值: 117.4022
  收益率: 0.1998
  距离: 33.6325
  内存使用: 0.6229
  能量使用: 0.9401
  推理时间: 2.6052秒

批次 78:
  奖励值: 117.6674
  收益率: 0.1986
  距离: 31.6338
  内存使用: 0.6632
  能量使用: 0.9682
  推理时间: 2.5783秒

批次 79:
  奖励值: 125.5199
  收益率: 0.2127
  距离: 30.8342
  内存使用: 0.7087
  能量使用: 1.0172
  推理时间: 2.7401秒

批次 80:
  奖励值: 114.7270
  收益率: 0.1998
  距离: 33.3315
  内存使用: 0.8996
  能量使用: 0.9148
  推理时间: 2.6251秒

批次 81:
  奖励值: 122.4819
  收益率: 0.2060
  距离: 28.8075
  内存使用: 0.6422
  能量使用: 0.9865
  推理时间: 2.6652秒

批次 82:
  奖励值: 119.0609
  收益率: 0.1960
  距离: 31.2127
  内存使用: 0.6401
  能量使用: 1.0254
  推理时间: 2.6433秒

批次 83:
  奖励值: 116.8165
  收益率: 0.1925
  距离: 31.6338
  内存使用: 0.5993
  能量使用: 0.9338
  推理时间: 2.5758秒

批次 84:
  奖励值: 128.2415
  收益率: 0.2118
  距离: 30.6679
  内存使用: 0.7735
  能量使用: 1.0757
  推理时间: 2.8255秒

批次 85:
  奖励值: 122.4922
  收益率: 0.2022
  距离: 32.3903
  内存使用: 0.6139
  能量使用: 1.0113
  推理时间: 2.7022秒

批次 86:
  奖励值: 117.7013
  收益率: 0.1959
  距离: 31.1892
  内存使用: 0.6567
  能量使用: 0.9515
  推理时间: 2.5583秒

批次 87:
  奖励值: 125.0745
  收益率: 0.2074
  距离: 34.5328
  内存使用: 0.6833
  能量使用: 1.0350
  推理时间: 2.7418秒

批次 88:
  奖励值: 124.8885
  收益率: 0.2139
  距离: 32.1156
  内存使用: 0.7170
  能量使用: 0.9904
  推理时间: 2.8021秒

批次 89:
  奖励值: 144.6768
  收益率: 0.2420
  距离: 37.0705
  内存使用: 0.7546
  能量使用: 1.2211
  推理时间: 3.1609秒

批次 90:
  奖励值: 124.3018
  收益率: 0.2061
  距离: 33.0305
  内存使用: 0.6964
  能量使用: 1.0695
  推理时间: 2.6544秒

批次 91:
  奖励值: 126.2870
  收益率: 0.2159
  距离: 34.1100
  内存使用: 0.6650
  能量使用: 1.0510
  推理时间: 2.8106秒

批次 92:
  奖励值: 124.6058
  收益率: 0.2088
  距离: 32.6611
  内存使用: 0.6923
  能量使用: 1.0349
  推理时间: 2.7531秒

批次 93:
  奖励值: 141.2576
  收益率: 0.2357
  距离: 33.2139
  内存使用: 0.7215
  能量使用: 1.1494
  推理时间: 3.0326秒

批次 94:
  奖励值: 130.7481
  收益率: 0.2181
  距离: 33.5456
  内存使用: 0.7235
  能量使用: 1.0345
  推理时间: 2.8442秒

批次 95:
  奖励值: 143.2304
  收益率: 0.2427
  距离: 38.2174
  内存使用: 0.8261
  能量使用: 1.0956
  推理时间: 3.1748秒

批次 96:
  奖励值: 128.2222
  收益率: 0.2199
  距离: 38.4730
  内存使用: 0.6944
  能量使用: 0.9881
  推理时间: 2.8448秒

批次 97:
  奖励值: 133.3968
  收益率: 0.2241
  距离: 35.0796
  内存使用: 0.7479
  能量使用: 0.9790
  推理时间: 2.9250秒

批次 98:
  奖励值: 135.8564
  收益率: 0.2290
  距离: 36.5970
  内存使用: 0.7722
  能量使用: 1.1179
  推理时间: 3.0257秒

批次 99:
  奖励值: 135.0602
  收益率: 0.2300
  距离: 35.7314
  内存使用: 0.6926
  能量使用: 1.0555
  推理时间: 2.9273秒

批次 100:
  奖励值: 119.3076
  收益率: 0.1999
  距离: 29.9305
  内存使用: 0.5558
  能量使用: 0.8985
  推理时间: 2.6185秒


==================== 总结 ====================
平均收益率: 0.2160
平均能量使用: 1.0331
平均推理时间: 2.8384秒
