"""
快速Transformer消融实验启动脚本
提供简化的接口来运行消融实验

特点:
1. 自动配置实验参数
2. 提供多种实验模式
3. 实时进度显示
4. 自动结果分析
"""

import os
import sys
import subprocess
import datetime
import argparse
import torch

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from hyperparameter import args as default_args

device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

def print_banner():
    """打印启动横幅"""
    print("🚀 Transformer配置消融实验快速启动器")
    print("=" * 60)
    print("基于消融实验设计方案.md的实验设计")
    print("支持8种不同的Transformer配置对比")
    print()

def get_experiment_config():
    """获取实验配置"""
    print("📋 实验配置选择")
    print("-" * 30)
    
    # 实验模式选择
    print("选择实验模式:")
    print("  1. 快速测试 (1轮训练, 小数据集)")
    print("  2. 标准实验 (3轮训练, 中等数据集)")
    print("  3. 完整实验 (默认轮数, 完整数据集)")
    print("  4. 自定义配置")
    
    while True:
        try:
            choice = input("\n请选择模式 (1-4): ").strip()
            if choice in ['1', '2', '3', '4']:
                break
            print("请输入有效选择 (1-4)")
        except KeyboardInterrupt:
            print("\n实验已取消")
            sys.exit(0)
    
    # 根据选择配置参数
    if choice == '1':
        config = {
            'epochs': 1,
            'train_size': 500,
            'valid_size': 100,
            'batch_size': 32,
            'description': '快速测试模式'
        }
    elif choice == '2':
        config = {
            'epochs': 3,
            'train_size': 5000,
            'valid_size': 1000,
            'batch_size': 64,
            'description': '标准实验模式'
        }
    elif choice == '3':
        config = {
            'epochs': default_args.epochs,
            'train_size': default_args.train_size,
            'valid_size': default_args.valid_size,
            'batch_size': default_args.batch_size,
            'description': '完整实验模式'
        }
    else:  # 自定义
        print("\n自定义配置:")
        config = {}
        config['epochs'] = int(input(f"训练轮数 (默认{default_args.epochs}): ") or default_args.epochs)
        config['train_size'] = int(input(f"训练数据量 (默认{default_args.train_size}): ") or default_args.train_size)
        config['valid_size'] = int(input(f"验证数据量 (默认{default_args.valid_size}): ") or default_args.valid_size)
        config['batch_size'] = int(input(f"批次大小 (默认{default_args.batch_size}): ") or default_args.batch_size)
        config['description'] = '自定义配置'
    
    return config

def estimate_experiment_time(config):
    """估算实验时间"""
    # 基于经验的时间估算公式
    base_time_per_epoch = 2  # 分钟
    
    # 根据数据量调整
    data_factor = config['train_size'] / 5000
    
    # 根据配置数量调整 (8个配置)
    total_time = config['epochs'] * base_time_per_epoch * data_factor * 8
    
    return total_time

def run_ablation_experiment(config):
    """运行消融实验"""
    print(f"\n🚀 开始运行消融实验")
    print(f"配置: {config['description']}")
    print("-" * 40)
    
    # 显示实验参数
    print("实验参数:")
    for key, value in config.items():
        if key != 'description':
            print(f"  {key}: {value}")
    
    # 估算时间
    estimated_time = estimate_experiment_time(config)
    print(f"\n预估时间: {estimated_time:.1f} 分钟")
    
    # 确认开始
    response = input("\n是否开始实验? (y/n): ")
    if response.lower() != 'y':
        print("实验已取消")
        return False
    
    print(f"\n⏰ 实验开始时间: {datetime.datetime.now().strftime('%H:%M:%S')}")
    
    try:
        # 构建命令
        cmd = [
            sys.executable,
            "transformer_ablation_study.py",
            "--epochs", str(config['epochs']),
            "--train_size", str(config['train_size']),
            "--valid_size", str(config['valid_size']),
            "--batch_size", str(config['batch_size'])
        ]
        
        print("执行命令:", " ".join(cmd))
        print("\n" + "="*60)
        
        # 运行实验
        start_time = datetime.datetime.now()
        
        result = subprocess.run(cmd, capture_output=False, text=True)
        
        end_time = datetime.datetime.now()
        duration = end_time - start_time
        
        print("="*60)
        print(f"⏰ 实验结束时间: {end_time.strftime('%H:%M:%S')}")
        print(f"⏱️ 实际耗时: {duration}")
        
        if result.returncode == 0:
            print("✅ 消融实验成功完成!")
            return True
        else:
            print("❌ 消融实验失败!")
            return False
            
    except Exception as e:
        print(f"❌ 运行实验时出错: {e}")
        return False

def find_latest_results():
    """查找最新的实验结果"""
    base_dir = os.path.join(default_args.task, f"{default_args.task}{default_args.num_nodes}")
    
    if not os.path.exists(base_dir):
        return None
    
    # 查找最新的消融实验目录
    ablation_dirs = []
    for item in os.listdir(base_dir):
        if item.startswith('transformer_ablation_study_'):
            full_path = os.path.join(base_dir, item)
            if os.path.isdir(full_path):
                ablation_dirs.append(full_path)
    
    if ablation_dirs:
        # 按修改时间排序，返回最新的
        latest_dir = max(ablation_dirs, key=os.path.getmtime)
        results_dir = os.path.join(latest_dir, 'ablation_results')
        if os.path.exists(results_dir):
            return results_dir
    
    return None

def analyze_latest_results():
    """分析最新的实验结果"""
    print("\n🔍 查找最新实验结果...")
    
    results_dir = find_latest_results()
    
    if not results_dir:
        print("❌ 未找到实验结果目录")
        print("请先运行消融实验")
        return False
    
    print(f"✅ 找到结果目录: {results_dir}")
    
    try:
        # 运行分析
        cmd = [sys.executable, "ablation_analysis.py", "--results_dir", results_dir]
        
        result = subprocess.run(cmd, capture_output=False, text=True)
        
        if result.returncode == 0:
            print("✅ 结果分析完成!")
            return True
        else:
            print("❌ 结果分析失败!")
            return False
            
    except Exception as e:
        print(f"❌ 分析结果时出错: {e}")
        return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='Transformer消融实验快速启动器')
    parser.add_argument('--analyze_only', action='store_true', 
                       help='仅分析最新结果，不运行新实验')
    parser.add_argument('--test_only', action='store_true',
                       help='仅运行功能测试')
    
    args = parser.parse_args()
    
    print_banner()
    
    if args.test_only:
        print("🧪 运行功能测试...")
        try:
            result = subprocess.run([sys.executable, "test_transformer_ablation.py"], 
                                  capture_output=False, text=True)
            sys.exit(result.returncode)
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            sys.exit(1)
    
    if args.analyze_only:
        print("🔍 仅分析最新结果...")
        success = analyze_latest_results()
        sys.exit(0 if success else 1)
    
    # 正常流程
    print("当前环境:")
    print(f"  设备: {device}")
    print(f"  默认节点数: {default_args.num_nodes}")
    print(f"  默认卫星数: {default_args.num_satellites}")
    print()
    
    # 获取实验配置
    config = get_experiment_config()
    
    # 运行实验
    success = run_ablation_experiment(config)
    
    if success:
        # 询问是否分析结果
        response = input("\n是否立即分析实验结果? (y/n): ")
        if response.lower() == 'y':
            analyze_latest_results()
        
        print("\n🎉 实验流程完成!")
        print("📁 查看结果目录获取详细分析")
    else:
        print("\n❌ 实验失败")
        sys.exit(1)

if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断操作")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ 程序出错: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
