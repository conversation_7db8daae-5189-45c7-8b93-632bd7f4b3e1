推理数据数量: 100
每个序列任务数量: 1500
星座卫星数量: 3
星座模式: competitive
模型: gpn+indrnn
种子: 12348
内存总量: 0.3
电量总量: 5
检查点路径: constellation_smp/constellation_smp100/multi_constellation_comparison_2025_08_25_17_02_29/constellation_gpnindrnn_competitive_2025_08_26_05_01_59

批次 1:
  奖励值: 141.1349
  收益率: 0.2383
  距离: 35.8542
  内存使用: 0.8132
  能量使用: 1.0628
  推理时间: 2.7801秒

批次 2:
  奖励值: 117.6572
  收益率: 0.1995
  距离: 32.2788
  内存使用: 0.6441
  能量使用: 0.9626
  推理时间: 2.5918秒

批次 3:
  奖励值: 145.0712
  收益率: 0.2485
  距离: 37.1168
  内存使用: 0.7767
  能量使用: 1.1641
  推理时间: 3.1488秒

批次 4:
  奖励值: 143.0320
  收益率: 0.2368
  距离: 37.4087
  内存使用: 0.8015
  能量使用: 1.1257
  推理时间: 2.8873秒

批次 5:
  奖励值: 141.1162
  收益率: 0.2388
  距离: 37.6671
  内存使用: 0.7558
  能量使用: 1.1447
  推理时间: 2.8347秒

批次 6:
  奖励值: 128.2856
  收益率: 0.2087
  距离: 31.0069
  内存使用: 0.6264
  能量使用: 0.9955
  推理时间: 2.5696秒

批次 7:
  奖励值: 127.6611
  收益率: 0.2120
  距离: 33.1945
  内存使用: 0.6966
  能量使用: 0.9866
  推理时间: 2.7308秒

批次 8:
  奖励值: 144.8632
  收益率: 0.2405
  距离: 34.3209
  内存使用: 0.8027
  能量使用: 1.1500
  推理时间: 3.1111秒

批次 9:
  奖励值: 133.0359
  收益率: 0.2239
  距离: 34.7806
  内存使用: 0.7001
  能量使用: 1.0476
  推理时间: 2.5953秒

批次 10:
  奖励值: 113.2557
  收益率: 0.1918
  距离: 28.2608
  内存使用: 0.8998
  能量使用: 0.9280
  推理时间: 2.2985秒

批次 11:
  奖励值: 124.7628
  收益率: 0.2113
  距离: 33.6397
  内存使用: 0.7052
  能量使用: 0.9294
  推理时间: 2.7844秒

批次 12:
  奖励值: 126.9369
  收益率: 0.2112
  距离: 32.8630
  内存使用: 0.6844
  能量使用: 0.9788
  推理时间: 2.8284秒

批次 13:
  奖励值: 127.4050
  收益率: 0.2129
  距离: 30.6074
  内存使用: 0.6201
  能量使用: 0.9625
  推理时间: 2.4796秒

批次 14:
  奖励值: 126.8548
  收益率: 0.2126
  距离: 33.4128
  内存使用: 0.8998
  能量使用: 0.9778
  推理时间: 2.7084秒

批次 15:
  奖励值: 124.8810
  收益率: 0.2106
  距离: 33.1544
  内存使用: 0.6514
  能量使用: 1.0322
  推理时间: 2.6935秒

批次 16:
  奖励值: 128.1171
  收益率: 0.2151
  距离: 30.2895
  内存使用: 0.6981
  能量使用: 0.9782
  推理时间: 2.7416秒

批次 17:
  奖励值: 136.7994
  收益率: 0.2267
  距离: 33.9332
  内存使用: 0.6750
  能量使用: 0.9929
  推理时间: 3.5349秒

批次 18:
  奖励值: 139.0672
  收益率: 0.2384
  距离: 34.5056
  内存使用: 0.7015
  能量使用: 1.0585
  推理时间: 2.9123秒

批次 19:
  奖励值: 122.5573
  收益率: 0.2077
  距离: 32.8985
  内存使用: 0.6411
  能量使用: 0.9458
  推理时间: 2.7081秒

批次 20:
  奖励值: 127.6199
  收益率: 0.2150
  距离: 32.6405
  内存使用: 0.6931
  能量使用: 1.0265
  推理时间: 2.7410秒

批次 21:
  奖励值: 129.2372
  收益率: 0.2173
  距离: 32.9348
  内存使用: 0.7296
  能量使用: 1.0612
  推理时间: 2.6536秒

批次 22:
  奖励值: 139.6007
  收益率: 0.2279
  距离: 33.9418
  内存使用: 0.7594
  能量使用: 1.0974
  推理时间: 2.9659秒

批次 23:
  奖励值: 143.5563
  收益率: 0.2373
  距离: 35.5882
  内存使用: 0.7541
  能量使用: 1.1450
  推理时间: 3.0901秒

批次 24:
  奖励值: 144.8659
  收益率: 0.2483
  距离: 36.4579
  内存使用: 0.7770
  能量使用: 1.2332
  推理时间: 2.9375秒

批次 25:
  奖励值: 131.9329
  收益率: 0.2176
  距离: 33.7205
  内存使用: 0.7446
  能量使用: 1.0691
  推理时间: 2.8053秒

批次 26:
  奖励值: 108.1820
  收益率: 0.1833
  距离: 30.2227
  内存使用: 0.8996
  能量使用: 0.9137
  推理时间: 2.4156秒

批次 27:
  奖励值: 123.2592
  收益率: 0.2098
  距离: 33.9176
  内存使用: 0.5890
  能量使用: 0.9752
  推理时间: 2.6957秒

批次 28:
  奖励值: 130.4825
  收益率: 0.2178
  距离: 32.1424
  内存使用: 0.6866
  能量使用: 1.0060
  推理时间: 2.7653秒

批次 29:
  奖励值: 136.7028
  收益率: 0.2344
  距离: 33.6358
  内存使用: 0.7228
  能量使用: 1.0357
  推理时间: 2.7549秒

批次 30:
  奖励值: 139.8711
  收益率: 0.2360
  距离: 31.8794
  内存使用: 0.7524
  能量使用: 1.0261
  推理时间: 2.9783秒

批次 31:
  奖励值: 120.2279
  收益率: 0.2052
  距离: 31.5111
  内存使用: 0.6758
  能量使用: 0.9572
  推理时间: 2.4706秒

批次 32:
  奖励值: 156.2148
  收益率: 0.2577
  距离: 39.8846
  内存使用: 0.8138
  能量使用: 1.2719
  推理时间: 3.1584秒

批次 33:
  奖励值: 151.1713
  收益率: 0.2535
  距离: 36.7063
  内存使用: 0.8259
  能量使用: 1.1252
  推理时间: 3.1702秒

批次 34:
  奖励值: 134.2828
  收益率: 0.2240
  距离: 31.6993
  内存使用: 0.7051
  能量使用: 1.0300
  推理时间: 2.6630秒

批次 35:
  奖励值: 143.6979
  收益率: 0.2410
  距离: 37.3281
  内存使用: 0.8309
  能量使用: 1.0453
  推理时间: 2.8805秒

批次 36:
  奖励值: 143.4998
  收益率: 0.2422
  距离: 36.2638
  内存使用: 0.8257
  能量使用: 1.1107
  推理时间: 2.8674秒

批次 37:
  奖励值: 132.0527
  收益率: 0.2160
  距离: 31.5850
  内存使用: 0.7463
  能量使用: 0.9632
  推理时间: 2.6574秒

批次 38:
  奖励值: 126.5519
  收益率: 0.2116
  距离: 35.0834
  内存使用: 0.6379
  能量使用: 0.9749
  推理时间: 2.5946秒

批次 39:
  奖励值: 140.2254
  收益率: 0.2335
  距离: 33.6283
  内存使用: 0.7347
  能量使用: 0.9863
  推理时间: 2.7934秒

批次 40:
  奖励值: 141.4090
  收益率: 0.2327
  距离: 31.3856
  内存使用: 0.6949
  能量使用: 1.1015
  推理时间: 2.9837秒

批次 41:
  奖励值: 125.4202
  收益率: 0.2079
  距离: 31.6157
  内存使用: 0.8998
  能量使用: 1.0441
  推理时间: 2.5785秒

批次 42:
  奖励值: 132.8156
  收益率: 0.2214
  距离: 33.1427
  内存使用: 0.7550
  能量使用: 1.0710
  推理时间: 2.8153秒

批次 43:
  奖励值: 139.0567
  收益率: 0.2376
  距离: 37.1902
  内存使用: 0.8025
  能量使用: 1.0940
  推理时间: 2.9681秒

批次 44:
  奖励值: 119.3776
  收益率: 0.2046
  距离: 32.7996
  内存使用: 0.8997
  能量使用: 0.9455
  推理时间: 2.6564秒

批次 45:
  奖励值: 122.4994
  收益率: 0.2103
  距离: 31.6866
  内存使用: 0.8999
  能量使用: 1.0145
  推理时间: 2.4538秒

批次 46:
  奖励值: 124.2157
  收益率: 0.2058
  距离: 32.8676
  内存使用: 0.8998
  能量使用: 1.0012
  推理时间: 2.7149秒

批次 47:
  奖励值: 133.3455
  收益率: 0.2248
  距离: 31.3779
  内存使用: 0.6798
  能量使用: 1.0512
  推理时间: 2.7299秒

批次 48:
  奖励值: 144.6094
  收益率: 0.2421
  距离: 36.4705
  内存使用: 0.8221
  能量使用: 1.0885
  推理时间: 2.9348秒

批次 49:
  奖励值: 124.2716
  收益率: 0.2054
  距离: 30.8431
  内存使用: 0.6288
  能量使用: 0.9848
  推理时间: 2.6685秒

批次 50:
  奖励值: 134.9846
  收益率: 0.2299
  距离: 35.3863
  内存使用: 0.7350
  能量使用: 1.1094
  推理时间: 2.9173秒

批次 51:
  奖励值: 142.7346
  收益率: 0.2435
  距离: 39.3536
  内存使用: 0.8398
  能量使用: 1.1375
  推理时间: 3.1784秒

批次 52:
  奖励值: 128.4921
  收益率: 0.2209
  距离: 33.4522
  内存使用: 0.7215
  能量使用: 1.0421
  推理时间: 2.6355秒

批次 53:
  奖励值: 120.3778
  收益率: 0.1959
  距离: 29.2742
  内存使用: 0.6028
  能量使用: 0.9674
  推理时间: 2.6371秒

批次 54:
  奖励值: 132.8252
  收益率: 0.2226
  距离: 36.0958
  内存使用: 0.6283
  能量使用: 1.0255
  推理时间: 2.9252秒

批次 55:
  奖励值: 123.1397
  收益率: 0.2026
  距离: 28.3095
  内存使用: 0.6682
  能量使用: 0.9430
  推理时间: 2.3804秒

批次 56:
  奖励值: 130.6882
  收益率: 0.2225
  距离: 34.8675
  内存使用: 0.7400
  能量使用: 1.0160
  推理时间: 2.7222秒

批次 57:
  奖励值: 130.9388
  收益率: 0.2206
  距离: 34.0070
  内存使用: 0.6988
  能量使用: 1.0166
  推理时间: 2.6559秒

批次 58:
  奖励值: 145.0746
  收益率: 0.2416
  距离: 37.0581
  内存使用: 0.8432
  能量使用: 1.1495
  推理时间: 3.0169秒

批次 59:
  奖励值: 129.7960
  收益率: 0.2166
  距离: 34.2701
  内存使用: 0.7136
  能量使用: 1.0242
  推理时间: 2.6328秒

批次 60:
  奖励值: 129.8605
  收益率: 0.2145
  距离: 32.6785
  内存使用: 0.6639
  能量使用: 0.9365
  推理时间: 2.8585秒

批次 61:
  奖励值: 145.7334
  收益率: 0.2464
  距离: 36.8075
  内存使用: 0.7746
  能量使用: 1.1510
  推理时间: 2.9636秒

批次 62:
  奖励值: 131.7850
  收益率: 0.2223
  距离: 32.7375
  内存使用: 0.6885
  能量使用: 1.0038
  推理时间: 2.7200秒

批次 63:
  奖励值: 132.3886
  收益率: 0.2230
  距离: 34.8577
  内存使用: 0.7579
  能量使用: 1.0072
  推理时间: 2.7861秒

批次 64:
  奖励值: 143.0330
  收益率: 0.2462
  距离: 41.4468
  内存使用: 0.7257
  能量使用: 1.1614
  推理时间: 2.9412秒

批次 65:
  奖励值: 138.5413
  收益率: 0.2279
  距离: 32.0845
  内存使用: 0.7314
  能量使用: 1.1014
  推理时间: 2.9971秒

批次 66:
  奖励值: 143.7415
  收益率: 0.2346
  距离: 35.7307
  内存使用: 0.8199
  能量使用: 1.1665
  推理时间: 3.1120秒

批次 67:
  奖励值: 140.9220
  收益率: 0.2376
  距离: 37.1766
  内存使用: 0.7397
  能量使用: 1.0395
  推理时间: 2.9326秒

批次 68:
  奖励值: 136.1398
  收益率: 0.2266
  距离: 35.7314
  内存使用: 0.7114
  能量使用: 1.0775
  推理时间: 2.9871秒

批次 69:
  奖励值: 142.4348
  收益率: 0.2396
  距离: 34.3683
  内存使用: 0.7925
  能量使用: 1.1695
  推理时间: 2.9692秒

批次 70:
  奖励值: 139.4265
  收益率: 0.2336
  距离: 33.1455
  内存使用: 0.7327
  能量使用: 1.0686
  推理时间: 2.7966秒

批次 71:
  奖励值: 134.8048
  收益率: 0.2248
  距离: 30.4406
  内存使用: 0.7047
  能量使用: 1.0451
  推理时间: 2.8417秒

批次 72:
  奖励值: 130.8927
  收益率: 0.2162
  距离: 31.1462
  内存使用: 0.6624
  能量使用: 1.0060
  推理时间: 2.6807秒

批次 73:
  奖励值: 129.2513
  收益率: 0.2174
  距离: 31.8247
  内存使用: 0.6682
  能量使用: 1.0045
  推理时间: 2.7255秒

批次 74:
  奖励值: 148.8524
  收益率: 0.2516
  距离: 40.8592
  内存使用: 0.8564
  能量使用: 1.1796
  推理时间: 2.9682秒

批次 75:
  奖励值: 135.7498
  收益率: 0.2277
  距离: 33.2395
  内存使用: 0.6388
  能量使用: 1.0219
  推理时间: 2.7680秒

批次 76:
  奖励值: 135.3578
  收益率: 0.2222
  距离: 31.1546
  内存使用: 0.6976
  能量使用: 1.0322
  推理时间: 2.7683秒

批次 77:
  奖励值: 119.8856
  收益率: 0.2027
  距离: 32.6460
  内存使用: 0.6271
  能量使用: 0.9557
  推理时间: 2.4676秒

批次 78:
  奖励值: 136.2515
  收益率: 0.2303
  距离: 37.2706
  内存使用: 0.6987
  能量使用: 1.1276
  推理时间: 3.0296秒

批次 79:
  奖励值: 129.5096
  收益率: 0.2197
  距离: 32.2171
  内存使用: 0.7130
  能量使用: 1.0505
  推理时间: 2.8375秒

批次 80:
  奖励值: 126.1973
  收益率: 0.2177
  距离: 34.3123
  内存使用: 0.7416
  能量使用: 0.9514
  推理时间: 2.6489秒

批次 81:
  奖励值: 129.5767
  收益率: 0.2204
  距离: 33.7455
  内存使用: 0.6585
  能量使用: 1.0425
  推理时间: 2.6671秒

批次 82:
  奖励值: 131.0156
  收益率: 0.2135
  距离: 31.4348
  内存使用: 0.7192
  能量使用: 1.0149
  推理时间: 2.7735秒

批次 83:
  奖励值: 133.0016
  收益率: 0.2195
  距离: 36.4669
  内存使用: 0.6806
  能量使用: 1.0492
  推理时间: 2.9414秒

批次 84:
  奖励值: 134.9378
  收益率: 0.2239
  距离: 34.0270
  内存使用: 0.7021
  能量使用: 0.9953
  推理时间: 2.7476秒

批次 85:
  奖励值: 125.8367
  收益率: 0.2067
  距离: 31.9272
  内存使用: 0.6234
  能量使用: 0.9760
  推理时间: 2.5785秒

批次 86:
  奖励值: 148.6691
  收益率: 0.2455
  距离: 36.8306
  内存使用: 0.8608
  能量使用: 1.1703
  推理时间: 3.0469秒

批次 87:
  奖励值: 138.7903
  收益率: 0.2258
  距离: 32.3154
  内存使用: 0.7204
  能量使用: 1.1835
  推理时间: 2.9635秒

批次 88:
  奖励值: 126.3977
  收益率: 0.2163
  距离: 32.2911
  内存使用: 0.7167
  能量使用: 0.9967
  推理时间: 2.6346秒

批次 89:
  奖励值: 138.4865
  收益率: 0.2308
  距离: 34.3597
  内存使用: 0.7408
  能量使用: 1.1265
  推理时间: 2.8563秒

批次 90:
  奖励值: 141.0954
  收益率: 0.2330
  距离: 36.3638
  内存使用: 0.6831
  能量使用: 1.1622
  推理时间: 2.8545秒

批次 91:
  奖励值: 134.5527
  收益率: 0.2285
  距离: 34.3314
  内存使用: 0.7346
  能量使用: 1.0285
  推理时间: 2.7340秒

批次 92:
  奖励值: 148.4317
  收益率: 0.2484
  距离: 38.5119
  内存使用: 0.8717
  能量使用: 1.1531
  推理时间: 3.1802秒

批次 93:
  奖励值: 135.3978
  收益率: 0.2270
  距离: 33.1513
  内存使用: 0.7217
  能量使用: 1.1313
  推理时间: 2.7718秒

批次 94:
  奖励值: 126.8076
  收益率: 0.2107
  距离: 31.5492
  内存使用: 0.6580
  能量使用: 0.9775
  推理时间: 2.5011秒

批次 95:
  奖励值: 134.7733
  收益率: 0.2259
  距离: 32.7871
  内存使用: 0.7159
  能量使用: 0.9502
  推理时间: 2.9070秒

批次 96:
  奖励值: 133.4653
  收益率: 0.2254
  距离: 35.3266
  内存使用: 0.6937
  能量使用: 1.0490
  推理时间: 2.8864秒

批次 97:
  奖励值: 134.3164
  收益率: 0.2245
  距离: 33.9002
  内存使用: 0.6650
  能量使用: 1.0020
  推理时间: 2.7837秒

批次 98:
  奖励值: 132.1468
  收益率: 0.2230
  距离: 35.9804
  内存使用: 0.7356
  能量使用: 0.9903
  推理时间: 2.7094秒

批次 99:
  奖励值: 140.5870
  收益率: 0.2388
  距离: 36.4133
  内存使用: 0.7123
  能量使用: 1.1004
  推理时间: 2.9846秒

批次 100:
  奖励值: 144.7849
  收益率: 0.2413
  距离: 34.3749
  内存使用: 0.7168
  能量使用: 1.1383
  推理时间: 2.9207秒


==================== 总结 ====================
平均收益率: 0.2244
平均能量使用: 1.0480
平均推理时间: 2.8045秒
