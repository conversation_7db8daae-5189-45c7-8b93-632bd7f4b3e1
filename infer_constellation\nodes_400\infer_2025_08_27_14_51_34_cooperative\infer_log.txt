推理数据数量: 100
每个序列任务数量: 400
星座卫星数量: 3
星座模式: cooperative
模型: gpn+indrnn
种子: 12348
内存总量: 0.3
电量总量: 5
检查点路径: constellation_smp/constellation_smp100/multi_constellation_comparison_2025_08_25_17_02_29/constellation_gpnindrnn_cooperative_2025_08_25_17_02_29

批次 1:
  奖励值: 93.0740
  收益率: 0.5737
  距离: 27.3252
  内存使用: 0.5098
  能量使用: 0.8172
  推理时间: 2.1571秒

批次 2:
  奖励值: 87.8041
  收益率: 0.5745
  距离: 30.5454
  内存使用: 0.4500
  能量使用: 0.7964
  推理时间: 2.1406秒

批次 3:
  奖励值: 74.1356
  收益率: 0.4931
  距离: 21.4918
  内存使用: 0.3790
  能量使用: 0.6581
  推理时间: 1.7984秒

批次 4:
  奖励值: 86.1476
  收益率: 0.5508
  距离: 25.0046
  内存使用: 0.3945
  能量使用: 0.7057
  推理时间: 1.9595秒

批次 5:
  奖励值: 91.4546
  收益率: 0.5752
  距离: 24.5669
  内存使用: 0.5054
  能量使用: 0.8184
  推理时间: 2.1779秒

批次 6:
  奖励值: 91.8529
  收益率: 0.5450
  距离: 22.2668
  内存使用: 0.4370
  能量使用: 0.7904
  推理时间: 2.0358秒

批次 7:
  奖励值: 83.5729
  收益率: 0.5432
  距离: 25.5413
  内存使用: 0.4641
  能量使用: 0.6488
  推理时间: 1.9880秒

批次 8:
  奖励值: 92.0712
  收益率: 0.5658
  距离: 24.3580
  内存使用: 0.4882
  能量使用: 0.8114
  推理时间: 2.1257秒

批次 9:
  奖励值: 82.9715
  收益率: 0.5209
  距离: 19.9417
  内存使用: 0.4007
  能量使用: 0.7145
  推理时间: 1.8947秒

批次 10:
  奖励值: 89.5948
  收益率: 0.5768
  距离: 30.4830
  内存使用: 0.5340
  能量使用: 0.7934
  推理时间: 2.1376秒

批次 11:
  奖励值: 94.4949
  收益率: 0.5769
  距离: 28.0500
  内存使用: 0.5410
  能量使用: 0.8192
  推理时间: 2.2021秒

批次 12:
  奖励值: 88.4653
  收益率: 0.5593
  距离: 26.6453
  内存使用: 0.4291
  能量使用: 0.7950
  推理时间: 2.0760秒

批次 13:
  奖励值: 81.4595
  收益率: 0.5223
  距离: 25.2517
  内存使用: 0.7356
  能量使用: 0.6937
  推理时间: 2.0219秒

批次 14:
  奖励值: 85.4332
  收益率: 0.5285
  距离: 21.0064
  内存使用: 0.4328
  能量使用: 0.6944
  推理时间: 1.9393秒

批次 15:
  奖励值: 83.3805
  收益率: 0.5385
  距离: 25.0913
  内存使用: 0.4054
  能量使用: 0.7422
  推理时间: 1.9655秒

批次 16:
  奖励值: 83.3749
  收益率: 0.5451
  距离: 21.9674
  内存使用: 0.3977
  能量使用: 0.6851
  推理时间: 1.8970秒

批次 17:
  奖励值: 88.5905
  收益率: 0.5530
  距离: 26.2487
  内存使用: 0.4563
  能量使用: 0.7139
  推理时间: 2.0747秒

批次 18:
  奖励值: 78.4937
  收益率: 0.5269
  距离: 24.1567
  内存使用: 0.3747
  能量使用: 0.6812
  推理时间: 1.8151秒

批次 19:
  奖励值: 87.0755
  收益率: 0.5487
  距离: 28.4483
  内存使用: 0.4625
  能量使用: 0.7382
  推理时间: 2.0132秒

批次 20:
  奖励值: 84.9918
  收益率: 0.5517
  距离: 19.8292
  内存使用: 0.4227
  能量使用: 0.7644
  推理时间: 1.9317秒

批次 21:
  奖励值: 81.8288
  收益率: 0.5244
  距离: 22.0514
  内存使用: 0.3987
  能量使用: 0.6923
  推理时间: 1.8946秒

批次 22:
  奖励值: 84.0923
  收益率: 0.5182
  距离: 21.9338
  内存使用: 0.3915
  能量使用: 0.6901
  推理时间: 1.9199秒

批次 23:
  奖励值: 97.1590
  收益率: 0.5968
  距离: 26.9254
  内存使用: 0.5113
  能量使用: 0.8452
  推理时间: 2.1996秒

批次 24:
  奖励值: 88.0079
  收益率: 0.5543
  距离: 23.8988
  内存使用: 0.4835
  能量使用: 0.8019
  推理时间: 2.0555秒

批次 25:
  奖励值: 82.1217
  收益率: 0.5143
  距离: 21.5731
  内存使用: 0.3052
  能量使用: 0.6783
  推理时间: 1.9492秒

批次 26:
  奖励值: 89.4039
  收益率: 0.5682
  距离: 25.7913
  内存使用: 0.4588
  能量使用: 0.7421
  推理时间: 2.1296秒

批次 27:
  奖励值: 92.4079
  收益率: 0.5660
  距离: 25.7702
  内存使用: 0.4971
  能量使用: 0.7167
  推理时间: 2.1182秒

批次 28:
  奖励值: 80.9458
  收益率: 0.5320
  距离: 23.1564
  内存使用: 0.4115
  能量使用: 0.7405
  推理时间: 1.8899秒

批次 29:
  奖励值: 84.4701
  收益率: 0.5419
  距离: 24.1756
  内存使用: 0.3871
  能量使用: 0.6971
  推理时间: 1.9136秒

批次 30:
  奖励值: 87.6621
  收益率: 0.5389
  距离: 27.0251
  内存使用: 0.4634
  能量使用: 0.7872
  推理时间: 2.0578秒

批次 31:
  奖励值: 82.5866
  收益率: 0.5269
  距离: 23.4395
  内存使用: 0.4625
  能量使用: 0.7247
  推理时间: 1.9257秒

批次 32:
  奖励值: 86.9161
  收益率: 0.5360
  距离: 23.2166
  内存使用: 0.4243
  能量使用: 0.7270
  推理时间: 1.9886秒

批次 33:
  奖励值: 79.1153
  收益率: 0.5061
  距离: 22.0985
  内存使用: 0.3343
  能量使用: 0.7367
  推理时间: 1.8870秒

批次 34:
  奖励值: 83.9851
  收益率: 0.5348
  距离: 24.4598
  内存使用: 0.4674
  能量使用: 0.7025
  推理时间: 1.9553秒

批次 35:
  奖励值: 92.4078
  收益率: 0.5680
  距离: 21.8368
  内存使用: 0.5597
  能量使用: 0.7879
  推理时间: 2.1162秒

批次 36:
  奖励值: 92.2194
  收益率: 0.5758
  距离: 22.5224
  内存使用: 0.4474
  能量使用: 0.7913
  推理时间: 2.1061秒

批次 37:
  奖励值: 84.6851
  收益率: 0.5570
  距离: 24.0142
  内存使用: 0.4601
  能量使用: 0.7556
  推理时间: 2.0665秒

批次 38:
  奖励值: 84.9893
  收益率: 0.5364
  距离: 24.6468
  内存使用: 0.4265
  能量使用: 0.7481
  推理时间: 1.9826秒

批次 39:
  奖励值: 87.2114
  收益率: 0.5601
  距离: 23.8002
  内存使用: 0.4450
  能量使用: 0.7320
  推理时间: 2.0563秒

批次 40:
  奖励值: 81.3210
  收益率: 0.5316
  距离: 24.4472
  内存使用: 0.3988
  能量使用: 0.7120
  推理时间: 1.9263秒

批次 41:
  奖励值: 81.3644
  收益率: 0.5368
  距离: 26.0122
  内存使用: 0.3305
  能量使用: 0.7285
  推理时间: 1.9359秒

批次 42:
  奖励值: 94.0517
  收益率: 0.5717
  距离: 26.6001
  内存使用: 0.4942
  能量使用: 0.8023
  推理时间: 2.2572秒

批次 43:
  奖励值: 91.6445
  收益率: 0.5551
  距离: 23.8872
  内存使用: 0.4981
  能量使用: 0.7257
  推理时间: 2.1607秒

批次 44:
  奖励值: 84.6755
  收益率: 0.5457
  距离: 25.4397
  内存使用: 0.4367
  能量使用: 0.7726
  推理时间: 1.7087秒

批次 45:
  奖励值: 88.1423
  收益率: 0.5639
  距离: 24.6637
  内存使用: 0.4424
  能量使用: 0.8093
  推理时间: 2.0490秒

批次 46:
  奖励值: 88.3178
  收益率: 0.5579
  距离: 26.4598
  内存使用: 0.5025
  能量使用: 0.8022
  推理时间: 2.0727秒

批次 47:
  奖励值: 87.7170
  收益率: 0.5620
  距离: 25.5749
  内存使用: 0.4735
  能量使用: 0.7176
  推理时间: 2.1495秒

批次 48:
  奖励值: 92.3737
  收益率: 0.5706
  距离: 24.2137
  内存使用: 0.4603
  能量使用: 0.7786
  推理时间: 2.1351秒

批次 49:
  奖励值: 78.2462
  收益率: 0.5137
  距离: 23.0596
  内存使用: 0.4072
  能量使用: 0.6409
  推理时间: 1.8821秒

批次 50:
  奖励值: 90.5409
  收益率: 0.5537
  距离: 23.8911
  内存使用: 0.4966
  能量使用: 0.7305
  推理时间: 2.0309秒

批次 51:
  奖励值: 90.1641
  收益率: 0.5705
  距离: 25.6311
  内存使用: 0.4694
  能量使用: 0.7714
  推理时间: 2.3206秒

批次 52:
  奖励值: 88.6130
  收益率: 0.5825
  距离: 25.6019
  内存使用: 0.4175
  能量使用: 0.7545
  推理时间: 2.1541秒

批次 53:
  奖励值: 79.2667
  收益率: 0.5267
  距离: 25.7891
  内存使用: 0.3938
  能量使用: 0.6837
  推理时间: 1.8899秒

批次 54:
  奖励值: 92.6034
  收益率: 0.5673
  距离: 24.2507
  内存使用: 0.5400
  能量使用: 0.8024
  推理时间: 2.1401秒

批次 55:
  奖励值: 93.3855
  收益率: 0.5635
  距离: 25.7365
  内存使用: 0.4929
  能量使用: 0.7996
  推理时间: 2.2004秒

批次 56:
  奖励值: 88.8450
  收益率: 0.5670
  距离: 24.1035
  内存使用: 0.4419
  能量使用: 0.7645
  推理时间: 2.0562秒

批次 57:
  奖励值: 80.9750
  收益率: 0.5083
  距离: 21.7608
  内存使用: 0.3943
  能量使用: 0.6950
  推理时间: 1.8870秒

批次 58:
  奖励值: 92.8715
  收益率: 0.5987
  距离: 23.3630
  内存使用: 0.5369
  能量使用: 0.7572
  推理时间: 2.1677秒

批次 59:
  奖励值: 90.1790
  收益率: 0.5909
  距离: 29.0271
  内存使用: 0.4647
  能量使用: 0.7502
  推理时间: 2.2220秒

批次 60:
  奖励值: 83.8409
  收益率: 0.5329
  距离: 24.2454
  内存使用: 0.3690
  能量使用: 0.7038
  推理时间: 1.9764秒

批次 61:
  奖励值: 84.6968
  收益率: 0.5299
  距离: 24.5870
  内存使用: 0.4828
  能量使用: 0.7367
  推理时间: 2.0893秒

批次 62:
  奖励值: 89.7231
  收益率: 0.5498
  距离: 23.7307
  内存使用: 0.4662
  能量使用: 0.7570
  推理时间: 2.0886秒

批次 63:
  奖励值: 91.0265
  收益率: 0.5541
  距离: 25.6657
  内存使用: 0.4965
  能量使用: 0.7849
  推理时间: 2.2590秒

批次 64:
  奖励值: 88.8899
  收益率: 0.5801
  距离: 24.6599
  内存使用: 0.4482
  能量使用: 0.7121
  推理时间: 2.1122秒

批次 65:
  奖励值: 84.9025
  收益率: 0.5494
  距离: 25.8307
  内存使用: 0.4356
  能量使用: 0.7287
  推理时间: 2.0085秒

批次 66:
  奖励值: 91.1834
  收益率: 0.5580
  距离: 25.2192
  内存使用: 0.4805
  能量使用: 0.7334
  推理时间: 2.2103秒

批次 67:
  奖励值: 95.0476
  收益率: 0.5728
  距离: 28.2742
  内存使用: 0.4488
  能量使用: 0.7817
  推理时间: 2.1759秒

批次 68:
  奖励值: 90.6585
  收益率: 0.5514
  距离: 23.4515
  内存使用: 0.4827
  能量使用: 0.7488
  推理时间: 2.0245秒

批次 69:
  奖励值: 91.5043
  收益率: 0.6009
  距离: 26.4218
  内存使用: 0.4934
  能量使用: 0.8762
  推理时间: 2.2050秒

批次 70:
  奖励值: 84.8979
  收益率: 0.5379
  距离: 21.8495
  内存使用: 0.4115
  能量使用: 0.7434
  推理时间: 2.0367秒

批次 71:
  奖励值: 94.3961
  收益率: 0.5860
  距离: 25.9288
  内存使用: 0.5045
  能量使用: 0.7745
  推理时间: 2.1393秒

批次 72:
  奖励值: 90.0896
  收益率: 0.5347
  距离: 23.5878
  内存使用: 0.4401
  能量使用: 0.7929
  推理时间: 2.0357秒

批次 73:
  奖励值: 86.8972
  收益率: 0.5333
  距离: 22.2523
  内存使用: 0.4275
  能量使用: 0.7258
  推理时间: 2.0496秒

批次 74:
  奖励值: 76.2011
  收益率: 0.5078
  距离: 23.0834
  内存使用: 0.3981
  能量使用: 0.7163
  推理时间: 1.9767秒

批次 75:
  奖励值: 91.4432
  收益率: 0.5561
  距离: 23.9025
  内存使用: 0.4740
  能量使用: 0.7372
  推理时间: 2.0690秒

批次 76:
  奖励值: 94.0389
  收益率: 0.5960
  距离: 29.7999
  内存使用: 0.4953
  能量使用: 0.8269
  推理时间: 2.2359秒

批次 77:
  奖励值: 91.1183
  收益率: 0.5634
  距离: 24.3793
  内存使用: 0.4165
  能量使用: 0.7676
  推理时间: 2.0845秒

批次 78:
  奖励值: 83.6169
  收益率: 0.5290
  距离: 23.4824
  内存使用: 0.3820
  能量使用: 0.7019
  推理时间: 1.9684秒

批次 79:
  奖励值: 90.9020
  收益率: 0.5654
  距离: 27.0985
  内存使用: 0.5012
  能量使用: 0.7962
  推理时间: 2.1332秒

批次 80:
  奖励值: 90.8578
  收益率: 0.5716
  距离: 23.8129
  内存使用: 0.5087
  能量使用: 0.7629
  推理时间: 2.0367秒

批次 81:
  奖励值: 90.6015
  收益率: 0.5624
  距离: 23.1242
  内存使用: 0.5072
  能量使用: 0.7657
  推理时间: 2.0520秒

批次 82:
  奖励值: 83.1237
  收益率: 0.5373
  距离: 24.2220
  内存使用: 0.4635
  能量使用: 0.6671
  推理时间: 1.9690秒

批次 83:
  奖励值: 88.6259
  收益率: 0.5519
  距离: 23.5610
  内存使用: 0.4525
  能量使用: 0.7347
  推理时间: 2.0045秒

批次 84:
  奖励值: 77.3973
  收益率: 0.5054
  距离: 22.1384
  内存使用: 0.3553
  能量使用: 0.7118
  推理时间: 1.8750秒

批次 85:
  奖励值: 91.6308
  收益率: 0.5812
  距离: 26.6602
  内存使用: 0.4971
  能量使用: 0.7812
  推理时间: 2.1367秒

批次 86:
  奖励值: 84.4404
  收益率: 0.5399
  距离: 22.1682
  内存使用: 0.4199
  能量使用: 0.7311
  推理时间: 1.9971秒

批次 87:
  奖励值: 87.1704
  收益率: 0.5412
  距离: 22.9443
  内存使用: 0.4162
  能量使用: 0.6987
  推理时间: 1.9529秒

批次 88:
  奖励值: 87.0283
  收益率: 0.5398
  距离: 22.7494
  内存使用: 0.4122
  能量使用: 0.7818
  推理时间: 1.9901秒

批次 89:
  奖励值: 88.3997
  收益率: 0.5489
  距离: 25.0641
  内存使用: 0.4316
  能量使用: 0.7050
  推理时间: 2.0300秒

批次 90:
  奖励值: 91.4211
  收益率: 0.5692
  距离: 24.6942
  内存使用: 0.4577
  能量使用: 0.8059
  推理时间: 2.1556秒

批次 91:
  奖励值: 82.4625
  收益率: 0.5435
  距离: 26.1924
  内存使用: 0.4642
  能量使用: 0.8183
  推理时间: 2.0651秒

批次 92:
  奖励值: 86.6837
  收益率: 0.5596
  距离: 23.6499
  内存使用: 0.4394
  能量使用: 0.7432
  推理时间: 1.9684秒

批次 93:
  奖励值: 88.4115
  收益率: 0.5617
  距离: 26.8606
  内存使用: 0.4018
  能量使用: 0.7146
  推理时间: 2.0827秒

批次 94:
  奖励值: 89.2945
  收益率: 0.5418
  距离: 23.5712
  内存使用: 0.4806
  能量使用: 0.7593
  推理时间: 2.0450秒

批次 95:
  奖励值: 85.0180
  收益率: 0.5295
  距离: 24.6981
  内存使用: 0.4067
  能量使用: 0.6593
  推理时间: 1.9058秒

批次 96:
  奖励值: 80.7898
  收益率: 0.5291
  距离: 26.7861
  内存使用: 0.4167
  能量使用: 0.6176
  推理时间: 1.8799秒

批次 97:
  奖励值: 80.6182
  收益率: 0.4935
  距离: 21.3048
  内存使用: 0.4051
  能量使用: 0.6536
  推理时间: 1.7909秒

批次 98:
  奖励值: 82.4391
  收益率: 0.5269
  距离: 23.9817
  内存使用: 0.6852
  能量使用: 0.6549
  推理时间: 1.9055秒

批次 99:
  奖励值: 84.4808
  收益率: 0.5568
  距离: 24.9963
  内存使用: 0.4715
  能量使用: 0.7706
  推理时间: 2.0604秒

批次 100:
  奖励值: 95.9283
  收益率: 0.5936
  距离: 24.9585
  内存使用: 0.5264
  能量使用: 0.8587
  推理时间: 2.1755秒


==================== 总结 ====================
平均收益率: 0.5504
平均能量使用: 0.7452
平均推理时间: 2.0387秒
