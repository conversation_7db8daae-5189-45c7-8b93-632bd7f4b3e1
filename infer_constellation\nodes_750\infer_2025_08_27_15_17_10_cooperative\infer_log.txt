推理数据数量: 100
每个序列任务数量: 750
星座卫星数量: 3
星座模式: cooperative
模型: gpn+indrnn
种子: 12348
内存总量: 0.3
电量总量: 5
检查点路径: constellation_smp/constellation_smp100/multi_constellation_comparison_2025_08_25_17_02_29/constellation_gpnindrnn_cooperative_2025_08_25_17_02_29

批次 1:
  奖励值: 113.4553
  收益率: 0.3791
  距离: 29.6139
  内存使用: 0.5651
  能量使用: 0.9103
  推理时间: 2.3505秒

批次 2:
  奖励值: 114.5410
  收益率: 0.3812
  距离: 28.5211
  内存使用: 0.6369
  能量使用: 0.9601
  推理时间: 2.4988秒

批次 3:
  奖励值: 124.9717
  收益率: 0.4231
  距离: 32.8649
  内存使用: 0.7689
  能量使用: 1.0228
  推理时间: 2.7334秒

批次 4:
  奖励值: 112.5131
  收益率: 0.3882
  距离: 32.2369
  内存使用: 0.6064
  能量使用: 0.9305
  推理时间: 2.5383秒

批次 5:
  奖励值: 117.5451
  收益率: 0.4056
  距离: 31.2441
  内存使用: 0.6375
  能量使用: 1.0282
  推理时间: 2.6245秒

批次 6:
  奖励值: 112.1425
  收益率: 0.3921
  距离: 33.9273
  内存使用: 0.6583
  能量使用: 0.9294
  推理时间: 2.4975秒

批次 7:
  奖励值: 112.1005
  收益率: 0.3773
  距离: 29.7007
  内存使用: 0.6201
  能量使用: 0.8781
  推理时间: 2.4988秒

批次 8:
  奖励值: 121.9539
  收益率: 0.4106
  距离: 33.8142
  内存使用: 0.7196
  能量使用: 0.9584
  推理时间: 2.7121秒

批次 9:
  奖励值: 111.0220
  收益率: 0.3933
  距离: 30.8862
  内存使用: 0.5230
  能量使用: 0.9468
  推理时间: 2.4970秒

批次 10:
  奖励值: 115.1849
  收益率: 0.3767
  距离: 29.9666
  内存使用: 0.6713
  能量使用: 0.9922
  推理时间: 2.5924秒

批次 11:
  奖励值: 116.7882
  收益率: 0.4042
  距离: 29.6612
  内存使用: 0.6639
  能量使用: 0.8985
  推理时间: 2.7629秒

批次 12:
  奖励值: 103.9000
  收益率: 0.3499
  距离: 30.3277
  内存使用: 0.5994
  能量使用: 0.8729
  推理时间: 2.3255秒

批次 13:
  奖励值: 109.3656
  收益率: 0.3609
  距离: 29.7675
  内存使用: 0.6273
  能量使用: 0.8740
  推理时间: 2.4005秒

批次 14:
  奖励值: 115.9901
  收益率: 0.3979
  距离: 30.1233
  内存使用: 0.5625
  能量使用: 0.9339
  推理时间: 2.5362秒

批次 15:
  奖励值: 122.4952
  收益率: 0.4081
  距离: 30.0057
  内存使用: 0.6350
  能量使用: 0.9689
  推理时间: 2.6473秒

批次 16:
  奖励值: 107.0431
  收益率: 0.3530
  距离: 27.6429
  内存使用: 0.5598
  能量使用: 0.9111
  推理时间: 2.3812秒

批次 17:
  奖励值: 120.6099
  收益率: 0.3922
  距离: 33.1146
  内存使用: 0.7066
  能量使用: 0.9590
  推理时间: 2.6850秒

批次 18:
  奖励值: 114.0919
  收益率: 0.3866
  距离: 30.2763
  内存使用: 0.5803
  能量使用: 0.9596
  推理时间: 2.5236秒

批次 19:
  奖励值: 101.3320
  收益率: 0.3318
  距离: 27.4081
  内存使用: 0.5474
  能量使用: 0.7805
  推理时间: 2.2548秒

批次 20:
  奖励值: 116.9683
  收益率: 0.3952
  距离: 33.3695
  内存使用: 0.6624
  能量使用: 1.0314
  推理时间: 2.6015秒

批次 21:
  奖励值: 123.7741
  收益率: 0.4124
  距离: 39.2581
  内存使用: 0.6802
  能量使用: 1.1134
  推理时间: 2.7841秒

批次 22:
  奖励值: 114.1211
  收益率: 0.3873
  距离: 32.9314
  内存使用: 0.6265
  能量使用: 0.9997
  推理时间: 2.5875秒

批次 23:
  奖励值: 114.0606
  收益率: 0.3797
  距离: 29.1432
  内存使用: 0.6611
  能量使用: 0.8746
  推理时间: 2.4768秒

批次 24:
  奖励值: 113.5405
  收益率: 0.3893
  距离: 33.9145
  内存使用: 0.6316
  能量使用: 0.9729
  推理时间: 2.6365秒

批次 25:
  奖励值: 114.0301
  收益率: 0.3749
  距离: 32.5592
  内存使用: 0.6708
  能量使用: 0.9098
  推理时间: 2.5342秒

批次 26:
  奖励值: 113.3482
  收益率: 0.3841
  距离: 29.3385
  内存使用: 0.7129
  能量使用: 1.0522
  推理时间: 2.5730秒

批次 27:
  奖励值: 118.4585
  收益率: 0.3835
  距离: 30.2139
  内存使用: 0.6470
  能量使用: 1.0221
  推理时间: 2.6114秒

批次 28:
  奖励值: 129.9374
  收益率: 0.4344
  距离: 37.7251
  内存使用: 0.7144
  能量使用: 1.0857
  推理时间: 2.8605秒

批次 29:
  奖励值: 101.7579
  收益率: 0.3419
  距离: 26.9719
  内存使用: 0.5123
  能量使用: 0.7999
  推理时间: 2.2696秒

批次 30:
  奖励值: 115.1012
  收益率: 0.3741
  距离: 31.8102
  内存使用: 0.6158
  能量使用: 0.9715
  推理时间: 2.6076秒

批次 31:
  奖励值: 120.6881
  收益率: 0.4010
  距离: 34.5149
  内存使用: 0.7242
  能量使用: 0.9846
  推理时间: 2.7331秒

批次 32:
  奖励值: 114.7354
  收益率: 0.3750
  距离: 30.7680
  内存使用: 0.5934
  能量使用: 0.9678
  推理时间: 2.5624秒

批次 33:
  奖励值: 122.3218
  收益率: 0.4138
  距离: 35.2285
  内存使用: 0.6954
  能量使用: 1.0324
  推理时间: 2.8016秒

批次 34:
  奖励值: 114.2902
  收益率: 0.3927
  距离: 30.0716
  内存使用: 0.6026
  能量使用: 0.9454
  推理时间: 2.5225秒

批次 35:
  奖励值: 108.2681
  收益率: 0.3644
  距离: 29.9706
  内存使用: 0.5669
  能量使用: 0.9264
  推理时间: 2.4584秒

批次 36:
  奖励值: 111.6402
  收益率: 0.3802
  距离: 31.6092
  内存使用: 0.5751
  能量使用: 0.9353
  推理时间: 2.4896秒

批次 37:
  奖励值: 109.2988
  收益率: 0.3743
  距离: 30.6630
  内存使用: 0.6341
  能量使用: 0.9054
  推理时间: 2.4574秒

批次 38:
  奖励值: 111.2487
  收益率: 0.3683
  距离: 28.6577
  内存使用: 0.5438
  能量使用: 0.9011
  推理时间: 2.4105秒

批次 39:
  奖励值: 112.7511
  收益率: 0.3771
  距离: 29.8992
  内存使用: 0.6214
  能量使用: 0.9772
  推理时间: 2.6314秒

批次 40:
  奖励值: 107.9885
  收益率: 0.3629
  距离: 28.0500
  内存使用: 0.5573
  能量使用: 0.8157
  推理时间: 2.4130秒

批次 41:
  奖励值: 111.8058
  收益率: 0.3805
  距离: 29.4467
  内存使用: 0.6005
  能量使用: 0.9412
  推理时间: 2.4457秒

批次 42:
  奖励值: 112.4852
  收益率: 0.3607
  距离: 30.5630
  内存使用: 0.6924
  能量使用: 0.9741
  推理时间: 2.6250秒

批次 43:
  奖励值: 114.6951
  收益率: 0.3820
  距离: 30.1701
  内存使用: 0.5812
  能量使用: 0.8584
  推理时间: 2.5313秒

批次 44:
  奖励值: 110.1027
  收益率: 0.3671
  距离: 31.7075
  内存使用: 0.5771
  能量使用: 0.9101
  推理时间: 2.4969秒

批次 45:
  奖励值: 115.6577
  收益率: 0.3946
  距离: 31.4741
  内存使用: 0.5628
  能量使用: 1.0477
  推理时间: 2.5463秒

批次 46:
  奖励值: 113.9664
  收益率: 0.3767
  距离: 30.7524
  内存使用: 0.6342
  能量使用: 0.8804
  推理时间: 2.4999秒

批次 47:
  奖励值: 109.2726
  收益率: 0.3687
  距离: 32.7848
  内存使用: 0.5576
  能量使用: 0.9124
  推理时间: 2.4869秒

批次 48:
  奖励值: 111.9173
  收益率: 0.3729
  距离: 25.8387
  内存使用: 0.5721
  能量使用: 0.9546
  推理时间: 2.5054秒

批次 49:
  奖励值: 118.5442
  收益率: 0.4005
  距离: 34.6778
  内存使用: 0.6909
  能量使用: 0.9430
  推理时间: 2.7323秒

批次 50:
  奖励值: 127.6235
  收益率: 0.4152
  距离: 33.7817
  内存使用: 0.7088
  能量使用: 1.0623
  推理时间: 2.8162秒

批次 51:
  奖励值: 105.8382
  收益率: 0.3759
  距离: 30.9354
  内存使用: 0.8454
  能量使用: 0.9429
  推理时间: 2.4601秒

批次 52:
  奖励值: 118.0449
  收益率: 0.3907
  距离: 31.0009
  内存使用: 0.6470
  能量使用: 1.0623
  推理时间: 2.6269秒

批次 53:
  奖励值: 104.2917
  收益率: 0.3520
  距离: 26.6137
  内存使用: 0.5316
  能量使用: 0.8713
  推理时间: 2.3383秒

批次 54:
  奖励值: 112.5042
  收益率: 0.3833
  距离: 30.7968
  内存使用: 0.6138
  能量使用: 0.9810
  推理时间: 2.5514秒

批次 55:
  奖励值: 120.6209
  收益率: 0.4147
  距离: 31.9217
  内存使用: 0.6457
  能量使用: 0.9907
  推理时间: 2.7098秒

批次 56:
  奖励值: 102.5195
  收益率: 0.3540
  距离: 30.9807
  内存使用: 0.5410
  能量使用: 0.8393
  推理时间: 2.3850秒

批次 57:
  奖励值: 104.9742
  收益率: 0.3518
  距离: 29.6728
  内存使用: 0.5342
  能量使用: 0.8445
  推理时间: 2.3511秒

批次 58:
  奖励值: 100.9505
  收益率: 0.3304
  距离: 26.2721
  内存使用: 0.5788
  能量使用: 0.8481
  推理时间: 2.3476秒

批次 59:
  奖励值: 113.7596
  收益率: 0.3765
  距离: 29.8049
  内存使用: 0.6883
  能量使用: 0.9876
  推理时间: 2.6091秒

批次 60:
  奖励值: 122.7744
  收益率: 0.4266
  距离: 31.3942
  内存使用: 0.6673
  能量使用: 0.9897
  推理时间: 2.7934秒

批次 61:
  奖励值: 102.1477
  收益率: 0.3449
  距离: 28.9516
  内存使用: 0.4957
  能量使用: 0.8572
  推理时间: 2.2129秒

批次 62:
  奖励值: 114.3697
  收益率: 0.3843
  距离: 28.6506
  内存使用: 0.6009
  能量使用: 1.0092
  推理时间: 2.5490秒

批次 63:
  奖励值: 116.7845
  收益率: 0.3994
  距离: 31.2347
  内存使用: 0.6093
  能量使用: 0.9868
  推理时间: 2.6492秒

批次 64:
  奖励值: 111.8474
  收益率: 0.3734
  距离: 29.6682
  内存使用: 0.5879
  能量使用: 0.8490
  推理时间: 2.4890秒

批次 65:
  奖励值: 110.9546
  收益率: 0.3897
  距离: 32.7321
  内存使用: 0.6128
  能量使用: 0.9458
  推理时间: 2.5089秒

批次 66:
  奖励值: 107.0982
  收益率: 0.3706
  距离: 32.5401
  内存使用: 0.5665
  能量使用: 0.8971
  推理时间: 2.4275秒

批次 67:
  奖励值: 121.4113
  收益率: 0.4169
  距离: 33.9093
  内存使用: 0.6900
  能量使用: 0.9782
  推理时间: 2.7810秒

批次 68:
  奖励值: 119.4056
  收益率: 0.3944
  距离: 30.3683
  内存使用: 0.6525
  能量使用: 0.9679
  推理时间: 2.6160秒

批次 69:
  奖励值: 119.5224
  收益率: 0.3903
  距离: 29.2313
  内存使用: 0.7143
  能量使用: 1.0098
  推理时间: 2.6578秒

批次 70:
  奖励值: 125.2800
  收益率: 0.4281
  距离: 36.6090
  内存使用: 0.7360
  能量使用: 1.0949
  推理时间: 2.8191秒

批次 71:
  奖励值: 103.8208
  收益率: 0.3572
  距离: 31.1953
  内存使用: 0.5823
  能量使用: 0.9807
  推理时间: 2.3710秒

批次 72:
  奖励值: 109.6393
  收益率: 0.3617
  距离: 29.0349
  内存使用: 0.6246
  能量使用: 0.8618
  推理时间: 2.1619秒

批次 73:
  奖励值: 117.8632
  收益率: 0.3967
  距离: 33.2858
  内存使用: 0.6901
  能量使用: 0.8976
  推理时间: 2.6647秒

批次 74:
  奖励值: 102.6055
  收益率: 0.3486
  距离: 27.2207
  内存使用: 0.5183
  能量使用: 0.8501
  推理时间: 2.3039秒

批次 75:
  奖励值: 110.0495
  收益率: 0.3746
  距离: 28.7208
  内存使用: 0.6593
  能量使用: 0.9208
  推理时间: 2.5250秒

批次 76:
  奖励值: 111.0139
  收益率: 0.3784
  距离: 32.2940
  内存使用: 0.5609
  能量使用: 0.9172
  推理时间: 2.5121秒

批次 77:
  奖励值: 119.7168
  收益率: 0.3940
  距离: 32.7793
  内存使用: 0.5859
  能量使用: 1.0011
  推理时间: 2.6120秒

批次 78:
  奖励值: 115.2200
  收益率: 0.3761
  距离: 29.5488
  内存使用: 0.5864
  能量使用: 1.0248
  推理时间: 2.5926秒

批次 79:
  奖励值: 115.4863
  收益率: 0.3841
  距离: 34.0161
  内存使用: 0.6311
  能量使用: 0.9801
  推理时间: 2.5966秒

批次 80:
  奖励值: 116.2489
  收益率: 0.3966
  距离: 32.6822
  内存使用: 0.6473
  能量使用: 0.9727
  推理时间: 2.6029秒

批次 81:
  奖励值: 109.4653
  收益率: 0.3783
  距离: 27.9754
  内存使用: 0.5919
  能量使用: 0.9019
  推理时间: 2.3682秒

批次 82:
  奖励值: 104.8643
  收益率: 0.3560
  距离: 27.7340
  内存使用: 0.6510
  能量使用: 0.8863
  推理时间: 2.3738秒

批次 83:
  奖励值: 108.2716
  收益率: 0.3739
  距离: 28.9768
  内存使用: 0.5791
  能量使用: 0.9009
  推理时间: 2.5138秒

批次 84:
  奖励值: 107.9690
  收益率: 0.3577
  距离: 29.5214
  内存使用: 0.6607
  能量使用: 0.8799
  推理时间: 2.4299秒

批次 85:
  奖励值: 110.9667
  收益率: 0.3779
  距离: 30.2586
  内存使用: 0.5611
  能量使用: 0.8471
  推理时间: 2.5271秒

批次 86:
  奖励值: 114.3101
  收益率: 0.3845
  距离: 31.2732
  内存使用: 0.5841
  能量使用: 0.9225
  推理时间: 2.5994秒

批次 87:
  奖励值: 111.2061
  收益率: 0.3770
  距离: 32.8556
  内存使用: 0.6055
  能量使用: 0.9267
  推理时间: 2.4985秒

批次 88:
  奖励值: 108.9646
  收益率: 0.3626
  距离: 28.2419
  内存使用: 0.5329
  能量使用: 0.8960
  推理时间: 2.4169秒

批次 89:
  奖励值: 121.1345
  收益率: 0.4085
  距离: 29.6306
  内存使用: 0.7019
  能量使用: 0.9984
  推理时间: 2.9080秒

批次 90:
  奖励值: 109.0732
  收益率: 0.3676
  距离: 28.9908
  内存使用: 0.6086
  能量使用: 0.8542
  推理时间: 2.4284秒

批次 91:
  奖励值: 110.9755
  收益率: 0.3684
  距离: 31.5678
  内存使用: 0.6393
  能量使用: 0.8418
  推理时间: 2.5038秒

批次 92:
  奖励值: 111.5259
  收益率: 0.3556
  距离: 26.3666
  内存使用: 0.8993
  能量使用: 0.8823
  推理时间: 2.4288秒

批次 93:
  奖励值: 115.1024
  收益率: 0.3690
  距离: 28.0546
  内存使用: 0.5892
  能量使用: 0.9514
  推理时间: 2.5072秒

批次 94:
  奖励值: 106.5738
  收益率: 0.3720
  距离: 26.7814
  内存使用: 0.5943
  能量使用: 0.8766
  推理时间: 2.4194秒

批次 95:
  奖励值: 127.0593
  收益率: 0.4326
  距离: 33.0937
  内存使用: 0.7808
  能量使用: 1.0070
  推理时间: 2.7720秒

批次 96:
  奖励值: 111.5858
  收益率: 0.3778
  距离: 28.8634
  内存使用: 0.5982
  能量使用: 0.9669
  推理时间: 2.4649秒

批次 97:
  奖励值: 109.4846
  收益率: 0.3720
  距离: 27.1617
  内存使用: 0.5986
  能量使用: 0.8704
  推理时间: 2.4484秒

批次 98:
  奖励值: 105.1200
  收益率: 0.3587
  距离: 31.6808
  内存使用: 0.6353
  能量使用: 0.8439
  推理时间: 2.3502秒

批次 99:
  奖励值: 103.9666
  收益率: 0.3394
  距离: 27.2464
  内存使用: 0.5954
  能量使用: 0.8249
  推理时间: 2.3330秒

批次 100:
  奖励值: 118.4303
  收益率: 0.3877
  距离: 32.0783
  内存使用: 0.6679
  能量使用: 0.9932
  推理时间: 2.5734秒


==================== 总结 ====================
平均收益率: 0.3809
平均能量使用: 0.9386
平均推理时间: 2.5346秒
