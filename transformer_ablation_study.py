"""
Transformer配置消融实验脚本
基于消融实验设计方案.md，系统性地测试不同Transformer配置对模型性能的影响

实验设计:
1. 基线实验: 无Transformer
2. 轻量级配置: 1层, 2头, 128维
3. 默认配置: 2层, 4头, 256维  
4. 深层配置: 3层, 8头, 512维
5. 超深层配置: 4层, 8头, 256维
6. 宽模型配置: 2层, 16头, 512维
7. 高dropout配置: dropout=0.3
8. ReLU激活函数配置

评估指标:
- 任务完成率 (Task Completion Rate)
- 平均收益率 (Average Revenue Rate) 
- 能量利用效率 (Energy Utilization Efficiency)
- 模型复杂度 (Model Parameters)
- 训练时间 (Training Time)
"""

import os
import sys
import torch
import datetime
import numpy as np
import matplotlib.pyplot as plt
import json
import time
from torch.utils.data import DataLoader
from torch import optim
from torch.optim.lr_scheduler import ReduceLROnPlateau

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from constellation_smp.constellation_smp import ConstellationSMPDataset, reward, render
from constellation_smp.gpn_constellation import GPNConstellation, ConstellationStateCritic
from train_constellation import validate_constellation_smp
from pict import plot_single_smp_train_loss
from hyperparameter import args

device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

class TransformerAblationController:
    """Transformer消融实验控制器"""
    
    def __init__(self, base_config):
        self.base_config = base_config
        self.results = {}
        self.log_file = None
        
    def setup_logging(self, log_path):
        """设置日志记录"""
        self.log_file = open(log_path, 'w', encoding='utf-8')
        
    def log_message(self, message):
        """记录日志信息"""
        print(message)
        if self.log_file is not None:
            self.log_file.write(message + '\n')
            self.log_file.flush()
    
    def generate_transformer_configs(self):
        """生成Transformer配置消融实验组"""
        configs = [
            # 基线：无Transformer
            {
                'name': 'baseline_no_transformer',
                'use_transformer': False,
                'config': None,
                'description': '基线配置，不使用Transformer'
            },
            
            # 轻量级配置
            {
                'name': 'lightweight',
                'use_transformer': True,
                'config': {
                    'd_model': 128, 'num_heads': 2, 'd_ff': 256, 'num_layers': 1,
                    'max_len': 5000, 'dropout': 0.1, 'activation': 'gelu'
                },
                'description': '轻量级配置: 1层, 2头, 128维'
            },
            
            # 默认配置
            {
                'name': 'default',
                'use_transformer': True,
                'config': {
                    'd_model': 256, 'num_heads': 4, 'd_ff': 512, 'num_layers': 2,
                    'max_len': 5000, 'dropout': 0.1, 'activation': 'gelu'
                },
                'description': '默认配置: 2层, 4头, 256维'
            },
            
            # 深层配置
            {
                'name': 'deep',
                'use_transformer': True,
                'config': {
                    'd_model': 512, 'num_heads': 8, 'd_ff': 1024, 'num_layers': 3,
                    'max_len': 5000, 'dropout': 0.1, 'activation': 'gelu'
                },
                'description': '深层配置: 3层, 8头, 512维'
            },
            
            # 超深层配置
            {
                'name': 'ultra_deep',
                'use_transformer': True,
                'config': {
                    'd_model': 256, 'num_heads': 8, 'd_ff': 512, 'num_layers': 4,
                    'max_len': 5000, 'dropout': 0.1, 'activation': 'gelu'
                },
                'description': '超深层配置: 4层, 8头, 256维'
            },
            
            # 宽模型配置
            {
                'name': 'wide',
                'use_transformer': True,
                'config': {
                    'd_model': 512, 'num_heads': 16, 'd_ff': 2048, 'num_layers': 2,
                    'max_len': 5000, 'dropout': 0.1, 'activation': 'gelu'
                },
                'description': '宽模型配置: 2层, 16头, 512维'
            },
            
            # 高dropout配置（测试正则化效果）
            {
                'name': 'high_dropout',
                'use_transformer': True,
                'config': {
                    'd_model': 256, 'num_heads': 4, 'd_ff': 512, 'num_layers': 2,
                    'max_len': 5000, 'dropout': 0.3, 'activation': 'gelu'
                },
                'description': '高dropout配置: dropout=0.3'
            },
            
            # ReLU激活函数配置
            {
                'name': 'relu_activation',
                'use_transformer': True,
                'config': {
                    'd_model': 256, 'num_heads': 4, 'd_ff': 512, 'num_layers': 2,
                    'max_len': 5000, 'dropout': 0.1, 'activation': 'relu'
                },
                'description': 'ReLU激活函数配置'
            }
        ]
        
        return configs
    
    def create_model_for_config(self, constellation_mode, train_data, transformer_config, use_transformer):
        """为指定配置创建模型"""
        # 临时修改args
        original_use_transformer = args.use_transformer
        args.use_transformer = use_transformer
        
        try:
            actor = GPNConstellation(
                args.static_size,
                args.dynamic_size,
                args.hidden_size,
                args.num_satellites,
                args.rnn,
                args.num_layers,
                train_data.update_dynamic,
                train_data.update_mask,
                args.num_nodes,
                args.dropout,
                constellation_mode,
                use_transformer,
                transformer_config
            ).to(device)
            
            critic = ConstellationStateCritic(
                args.static_size,
                args.dynamic_size,
                args.hidden_size,
                args.num_satellites,
                constellation_mode
            ).to(device)
            
            return actor, critic
            
        finally:
            # 恢复原始配置
            args.use_transformer = original_use_transformer
    
    def run_single_experiment(self, config, constellation_mode, base_save_dir):
        """运行单个实验配置"""
        config_name = config['name']
        use_transformer = config['use_transformer']
        transformer_config = config['config']
        
        self.log_message(f"\n{'='*80}")
        self.log_message(f"开始运行配置: {config_name}")
        self.log_message(f"描述: {config['description']}")
        self.log_message(f"{'='*80}")
        
        if use_transformer:
            self.log_message(f"Transformer配置: {transformer_config}")
        else:
            self.log_message("基线配置: 不使用Transformer")
        
        # 创建该配置的保存目录
        now = datetime.datetime.now().strftime('%Y_%m_%d_%H_%M_%S')
        config_folder_name = f'config_{config_name}_{constellation_mode}_{now}'
        config_save_dir = os.path.join(base_save_dir, config_folder_name)
        
        if not os.path.exists(config_save_dir):
            os.makedirs(config_save_dir)
        
        # 创建数据集
        train_data = ConstellationSMPDataset(
            args.num_nodes, args.train_size, args.seed,
            args.memory_total, args.power_total, args.num_satellites
        )
        
        valid_data = ConstellationSMPDataset(
            args.num_nodes, args.valid_size, args.seed + 1,
            args.memory_total, args.power_total, args.num_satellites
        )
        
        # 创建模型
        actor, critic = self.create_model_for_config(
            constellation_mode, train_data, transformer_config, use_transformer
        )
        
        actor_params = sum(p.numel() for p in actor.parameters())
        critic_params = sum(p.numel() for p in critic.parameters())
        total_params = actor_params + critic_params
        
        self.log_message(f"模型信息:")
        self.log_message(f"  Actor参数数量: {actor_params:,}")
        self.log_message(f"  Critic参数数量: {critic_params:,}")
        self.log_message(f"  总参数数量: {total_params:,}")
        
        # 训练模型
        training_start_time = time.time()
        
        # 这里可以调用简化的训练函数
        best_reward = self.train_model_simplified(
            actor, critic, train_data, valid_data, config_save_dir, constellation_mode
        )
        
        training_end_time = time.time()
        training_duration = training_end_time - training_start_time
        
        # 测试模型
        test_data = ConstellationSMPDataset(
            args.num_nodes, args.valid_size, args.seed + 2,
            args.memory_total, args.power_total, args.num_satellites
        )
        
        test_loader = DataLoader(test_data, args.batch_size, False, num_workers=0)
        test_dir = os.path.join(config_save_dir, f'test_{config_name}')
        
        _, revenue_rate_avg, distance_avg, memory_avg, power_avg = validate_constellation_smp(
            test_loader, actor, reward, args.num_satellites, render,
            test_dir, num_plot=2, verbose=False, constellation_mode=constellation_mode
        )
        
        # 返回结果
        result = {
            'config_name': config_name,
            'description': config['description'],
            'use_transformer': use_transformer,
            'transformer_config': transformer_config,
            'constellation_mode': constellation_mode,
            'best_reward': best_reward,
            'revenue_rate_avg': revenue_rate_avg,
            'distance_avg': distance_avg,
            'memory_avg': memory_avg,
            'power_avg': power_avg,
            'actor_params': actor_params,
            'critic_params': critic_params,
            'total_params': total_params,
            'training_duration': training_duration,
            'save_dir': config_save_dir
        }
        
        self.log_message(f"\n配置 {config_name} 完整结果:")
        self.log_message(f"{'='*50}")
        self.log_message(f"  最佳验证奖励: {best_reward:.4f}")
        self.log_message(f"  平均收益率: {revenue_rate_avg:.4f}")
        self.log_message(f"  平均距离: {distance_avg:.4f}")
        self.log_message(f"  平均内存使用: {memory_avg:.4f}")
        self.log_message(f"  平均功耗: {power_avg:.4f}")
        self.log_message(f"  总参数: {total_params:,}")
        self.log_message(f"  训练时长: {training_duration:.1f}秒")
        self.log_message(f"{'='*50}")
        
        return result
    
    def train_model_simplified(self, actor, critic, train_data, valid_data, save_dir, constellation_mode):
        """简化的模型训练函数"""
        # 定义优化器
        actor_optim = optim.Adam(actor.parameters(), lr=args.actor_lr, weight_decay=args.weight_decay)
        critic_optim = optim.Adam(critic.parameters(), lr=args.critic_lr, weight_decay=args.weight_decay)
        
        # 数据加载器
        train_loader = DataLoader(train_data, args.batch_size, True, num_workers=2)
        valid_loader = DataLoader(valid_data, args.batch_size, False, num_workers=2)
        
        best_reward = float('-inf')
        
        for epoch in range(args.epochs):
            actor.train()
            critic.train()
            
            epoch_losses = []
            epoch_rewards = []
            
            for batch_idx, batch in enumerate(train_loader):
                static, dynamic, x0 = batch
                static = static.to(device)
                dynamic = dynamic.to(device)
                
                # 前向传播
                tour_indices, satellite_indices, tour_log_prob, satellite_log_prob = actor(static, dynamic)
                tour_log_prob = tour_log_prob + satellite_log_prob
                
                # 计算奖励
                R, revenue_rate, distance, memory, power = reward(static, tour_indices, satellite_indices, constellation_mode)
                
                # 评估基线
                critic_est = critic(static, dynamic).view(-1)
                advantage = (R - critic_est)
                
                # 计算损失
                critic_loss = torch.mean(advantage ** 2)
                advantage = (advantage - advantage.mean()) / (advantage.std() + 1e-8)
                actor_loss = torch.mean(-advantage.detach() * tour_log_prob.sum(dim=1))
                
                # 优化器步骤
                actor_optim.zero_grad()
                actor_loss.backward()
                torch.nn.utils.clip_grad_norm_(actor.parameters(), args.max_grad_norm)
                actor_optim.step()
                
                critic_optim.zero_grad()
                critic_loss.backward()
                torch.nn.utils.clip_grad_norm_(critic.parameters(), args.max_grad_norm)
                critic_optim.step()
                
                epoch_losses.append(critic_loss.item())
                epoch_rewards.append(torch.mean(R).item())
            
            # 验证
            if (epoch + 1) % max(1, args.epochs // 5) == 0:  # 每20%的epoch验证一次
                valid_reward, _, _, _, _ = validate_constellation_smp(
                    valid_loader, actor, reward, args.num_satellites, render,
                    os.path.join(save_dir, f'epoch_{epoch}'), num_plot=0, verbose=False,
                    constellation_mode=constellation_mode
                )
                
                if valid_reward > best_reward:
                    best_reward = valid_reward
                    torch.save(actor.state_dict(), os.path.join(save_dir, 'actor.pt'))
                    torch.save(critic.state_dict(), os.path.join(save_dir, 'critic.pt'))
                
                self.log_message(f"  Epoch {epoch+1}/{args.epochs}: "
                               f"训练奖励={np.mean(epoch_rewards):.4f}, "
                               f"验证奖励={valid_reward:.4f}, "
                               f"损失={np.mean(epoch_losses):.4f}")
        
        return best_reward
    
    def run_ablation_study(self):
        """运行完整的消融实验"""
        # 创建主保存目录
        now = datetime.datetime.now().strftime('%Y_%m_%d_%H_%M_%S')
        base_folder_name = f'transformer_ablation_study_{now}'
        
        base_save_dir = os.path.join(args.task, args.task + '%d' % args.num_nodes, base_folder_name)
        if not os.path.exists(base_save_dir):
            os.makedirs(base_save_dir)
        
        # 创建结果目录
        results_dir = os.path.join(base_save_dir, 'ablation_results')
        if not os.path.exists(results_dir):
            os.makedirs(results_dir)
        
        # 设置日志
        log_path = os.path.join(base_save_dir, 'transformer_ablation_log.txt')
        self.setup_logging(log_path)
        
        # 记录实验开始
        self.log_message("Transformer配置消融实验")
        self.log_message("=" * 80)
        self.log_message(f"实验时间: {now}")
        self.log_message(f"设备: {device}")
        self.log_message(f"问题规模: {args.num_nodes}节点, {args.num_satellites}卫星")
        self.log_message(f"训练配置: {args.epochs}轮, 批次大小{args.batch_size}")
        
        # 生成实验配置
        transformer_configs = self.generate_transformer_configs()
        constellation_mode = 'hybrid'  # 使用hybrid模式进行消融实验
        
        self.log_message(f"将运行 {len(transformer_configs)} 个配置:")
        for i, config in enumerate(transformer_configs):
            self.log_message(f"  {i+1}. {config['name']}: {config['description']}")
        
        self.log_message(f"使用星座模式: {constellation_mode}")
        
        # 运行所有配置
        all_results = []
        
        for i, config in enumerate(transformer_configs):
            try:
                self.log_message(f"\n开始运行配置 {i+1}/{len(transformer_configs)}")
                result = self.run_single_experiment(config, constellation_mode, base_save_dir)
                all_results.append(result)
                self.results[config['name']] = result
                
            except Exception as e:
                self.log_message(f"❌ 配置 {config['name']} 训练失败: {e}")
                import traceback
                self.log_message(traceback.format_exc())
        
        # 分析结果
        if len(all_results) > 0:
            self.analyze_results(all_results, results_dir)
            self.save_results(all_results, results_dir)
        
        if self.log_file:
            self.log_file.close()
        
        return all_results
    
    def analyze_results(self, all_results, results_dir):
        """分析实验结果"""
        self.log_message(f"\n{'='*80}")
        self.log_message("Transformer配置消融实验分析")
        self.log_message(f"{'='*80}")
        
        # 按性能排序
        sorted_by_reward = sorted(all_results, key=lambda x: x['best_reward'], reverse=True)
        sorted_by_efficiency = sorted(all_results, key=lambda x: x['best_reward'] / (x['total_params'] / 1000000), reverse=True)
        
        self.log_message(f"\n性能排名 (按奖励):")
        self.log_message(f"{'排名':<4} {'配置名称':<20} {'奖励':<8} {'收益率':<8} {'参数数':<12} {'效率':<8}")
        self.log_message(f"{'-'*70}")
        
        for i, result in enumerate(sorted_by_reward):
            efficiency = result['best_reward'] / (result['total_params'] / 1000000)
            self.log_message(f"{i+1:<4} {result['config_name']:<20} "
                           f"{result['best_reward']:<8.4f} "
                           f"{result['revenue_rate_avg']:<8.4f} "
                           f"{result['total_params']:<12,} "
                           f"{efficiency:<8.4f}")
        
        # 计算相对基线的提升
        baseline_result = next((r for r in all_results if r['config_name'] == 'baseline_no_transformer'), None)
        if baseline_result:
            self.log_message(f"\n相对基线的性能提升:")
            for result in sorted_by_reward:
                if result['config_name'] != 'baseline_no_transformer':
                    reward_improvement = ((result['best_reward'] - baseline_result['best_reward']) / baseline_result['best_reward']) * 100
                    param_increase = ((result['total_params'] - baseline_result['total_params']) / baseline_result['total_params']) * 100
                    
                    self.log_message(f"  {result['config_name']:<20}: "
                                   f"奖励提升{reward_improvement:+6.2f}%, "
                                   f"参数增加{param_increase:+6.2f}%")
        
        # 推荐配置
        best_config = sorted_by_reward[0]
        best_efficiency_config = sorted_by_efficiency[0]
        
        self.log_message(f"\n💡 推荐配置:")
        self.log_message(f"  性能最佳: {best_config['config_name']} (奖励: {best_config['best_reward']:.4f})")
        self.log_message(f"  效率最佳: {best_efficiency_config['config_name']} (效率: {best_efficiency_config['best_reward'] / (best_efficiency_config['total_params'] / 1000000):.4f})")
        
        # 生成可视化
        self.create_ablation_plots(all_results, results_dir)
    
    def create_ablation_plots(self, all_results, results_dir):
        """创建消融实验可视化图表"""
        try:
            plt.rcParams['font.sans-serif'] = ['SimHei']
            plt.rcParams['axes.unicode_minus'] = False
            
            # 1. 性能对比柱状图
            fig, axes = plt.subplots(2, 3, figsize=(18, 12))
            axes = axes.flatten()
            
            config_names = [result['config_name'] for result in all_results]
            metrics = ['best_reward', 'revenue_rate_avg', 'distance_avg', 'memory_avg', 'power_avg', 'total_params']
            metric_names = ['最佳奖励', '平均收益率', '平均距离', '平均内存使用', '平均功耗', '模型参数数量']
            
            for i, (metric, name) in enumerate(zip(metrics, metric_names)):
                values = [result[metric] for result in all_results]
                
                bars = axes[i].bar(range(len(config_names)), values, alpha=0.7)
                axes[i].set_title(name, fontsize=12, fontweight='bold')
                axes[i].set_ylabel('数值')
                axes[i].set_xticks(range(len(config_names)))
                axes[i].set_xticklabels(config_names, rotation=45, ha='right')
                
                # 添加数值标签
                for bar, value in zip(bars, values):
                    height = bar.get_height()
                    if metric == 'total_params':
                        label = f'{value:,}'
                    else:
                        label = f'{value:.3f}'
                    axes[i].text(bar.get_x() + bar.get_width()/2., height,
                                label, ha='center', va='bottom', fontsize=8)
            
            plt.tight_layout()
            plt.savefig(os.path.join(results_dir, 'transformer_ablation_comparison.png'), 
                       dpi=300, bbox_inches='tight')
            plt.close()
            
            # 2. 效率分析图
            fig, ax = plt.subplots(1, 1, figsize=(12, 8))
            
            param_counts = [result['total_params'] for result in all_results]
            rewards = [result['best_reward'] for result in all_results]
            
            scatter = ax.scatter(param_counts, rewards, alpha=0.7, s=100)
            for i, name in enumerate(config_names):
                ax.annotate(name, (param_counts[i], rewards[i]), 
                           xytext=(5, 5), textcoords='offset points', fontsize=9)
            
            ax.set_xlabel('模型参数数量')
            ax.set_ylabel('最佳奖励')
            ax.set_title('模型复杂度 vs 性能分析')
            ax.grid(True, alpha=0.3)
            
            plt.tight_layout()
            plt.savefig(os.path.join(results_dir, 'complexity_vs_performance.png'), 
                       dpi=300, bbox_inches='tight')
            plt.close()
            
            self.log_message(f"✓ 消融实验分析图表已保存到: {results_dir}")
            
        except Exception as e:
            self.log_message(f"⚠️ 生成分析图表时出错: {e}")
    
    def save_results(self, all_results, results_dir):
        """保存实验结果"""
        # 保存JSON结果
        results_summary = {
            'experiment_info': {
                'experiment_type': 'transformer_ablation',
                'timestamp': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'num_nodes': args.num_nodes,
                'num_satellites': args.num_satellites,
                'epochs': args.epochs,
                'constellation_mode': 'hybrid'
            },
            'results': all_results
        }
        
        json_path = os.path.join(results_dir, 'transformer_ablation_results.json')
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(results_summary, f, indent=2, ensure_ascii=False, default=str)
        
        # 保存文本报告
        report_path = os.path.join(results_dir, 'transformer_ablation_report.txt')
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("Transformer配置消融实验报告\n")
            f.write("=" * 60 + "\n\n")
            
            f.write(f"实验时间: {results_summary['experiment_info']['timestamp']}\n")
            f.write(f"问题规模: {args.num_nodes}节点, {args.num_satellites}卫星\n")
            f.write(f"训练轮数: {args.epochs}\n\n")
            
            # 按性能排序
            sorted_results = sorted(all_results, key=lambda x: x['best_reward'], reverse=True)
            
            f.write("配置性能排名:\n")
            f.write("-" * 40 + "\n")
            
            for i, result in enumerate(sorted_results):
                f.write(f"\n{i+1}. {result['config_name'].upper()}:\n")
                f.write(f"   描述: {result['description']}\n")
                f.write(f"   最佳奖励: {result['best_reward']:.4f}\n")
                f.write(f"   平均收益率: {result['revenue_rate_avg']:.4f}\n")
                f.write(f"   模型参数: {result['total_params']:,}\n")
                f.write(f"   训练时长: {result['training_duration']:.1f}秒\n")
        
        self.log_message(f"实验结果已保存到:")
        self.log_message(f"  JSON文件: {json_path}")
        self.log_message(f"  文本报告: {report_path}")


def run_transformer_ablation():
    """运行Transformer消融实验的主函数"""
    print("🧪 启动Transformer配置消融实验")
    print("=" * 60)
    
    # 创建消融实验控制器
    controller = TransformerAblationController(args)
    
    # 运行消融实验
    results = controller.run_ablation_study()
    
    if len(results) > 0:
        print(f"\n🎉 消融实验完成！成功测试了 {len(results)} 个配置")
        
        # 输出最佳配置
        best_result = max(results, key=lambda x: x['best_reward'])
        print(f"🏆 最佳配置: {best_result['config_name']}")
        print(f"   奖励: {best_result['best_reward']:.4f}")
        print(f"   参数: {best_result['total_params']:,}")
        
    else:
        print("❌ 消融实验失败")


if __name__ == '__main__':
    # 检查是否需要显示帮助
    if len(sys.argv) > 1 and sys.argv[1] in ['-h', '--help', 'help']:
        print("Transformer配置消融实验脚本")
        print("=" * 50)
        print("使用方法:")
        print("  python transformer_ablation_study.py")
        print("  python transformer_ablation_study.py --epochs 5")
        print()
        print("功能: 系统性测试8种不同的Transformer配置")
        sys.exit(0)
    
    print(f"当前配置:")
    print(f"  节点数: {args.num_nodes}")
    print(f"  卫星数: {args.num_satellites}")
    print(f"  训练轮数: {args.epochs}")
    print(f"  批次大小: {args.batch_size}")
    print()
    
    try:
        run_transformer_ablation()
    except KeyboardInterrupt:
        print("\n⚠️ 实验被用户中断")
    except Exception as e:
        print(f"\n❌ 实验过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
