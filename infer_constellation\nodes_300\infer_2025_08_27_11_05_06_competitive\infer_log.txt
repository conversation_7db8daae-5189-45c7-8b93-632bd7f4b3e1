推理数据数量: 100
每个序列任务数量: 300
星座卫星数量: 3
星座模式: competitive
模型: gpn+indrnn
种子: 12348
内存总量: 0.3
电量总量: 5
检查点路径: constellation_smp/constellation_smp100/multi_constellation_comparison_2025_08_25_17_02_29/constellation_gpnindrnn_competitive_2025_08_26_05_01_59

批次 1:
  奖励值: 73.8295
  收益率: 0.5938
  距离: 19.2789
  内存使用: 0.2588
  能量使用: 0.6034
  推理时间: 1.7165秒

批次 2:
  奖励值: 65.8020
  收益率: 0.5596
  距离: 19.7288
  内存使用: 0.2749
  能量使用: 0.5924
  推理时间: 1.5852秒

批次 3:
  奖励值: 71.0114
  收益率: 0.5904
  距离: 19.0746
  内存使用: 0.2859
  能量使用: 0.6167
  推理时间: 1.4364秒

批次 4:
  奖励值: 70.5141
  收益率: 0.5832
  距离: 17.8672
  内存使用: 0.2854
  能量使用: 0.6192
  推理时间: 1.4298秒

批次 5:
  奖励值: 76.3730
  收益率: 0.5963
  距离: 21.4693
  内存使用: 0.3454
  能量使用: 0.6392
  推理时间: 1.8239秒

批次 6:
  奖励值: 80.0529
  收益率: 0.6494
  距离: 21.4420
  内存使用: 0.3500
  能量使用: 0.7023
  推理时间: 1.9145秒

批次 7:
  奖励值: 76.5710
  收益率: 0.6312
  距离: 22.8156
  内存使用: 0.3352
  能量使用: 0.7071
  推理时间: 1.9259秒

批次 8:
  奖励值: 74.7515
  收益率: 0.6315
  距离: 20.5897
  内存使用: 0.3295
  能量使用: 0.5777
  推理时间: 1.7998秒

批次 9:
  奖励值: 72.5789
  收益率: 0.5817
  距离: 16.9920
  内存使用: 0.2646
  能量使用: 0.5855
  推理时间: 1.7341秒

批次 10:
  奖励值: 71.6915
  收益率: 0.5999
  距离: 16.7464
  内存使用: 0.3362
  能量使用: 0.5344
  推理时间: 1.7221秒

批次 11:
  奖励值: 74.2724
  收益率: 0.6018
  距离: 21.2817
  内存使用: 0.3109
  能量使用: 0.6627
  推理时间: 1.8162秒

批次 12:
  奖励值: 68.5339
  收益率: 0.5711
  距离: 18.6374
  内存使用: 0.2895
  能量使用: 0.6053
  推理时间: 1.4167秒

批次 13:
  奖励值: 68.3956
  收益率: 0.5929
  距离: 18.7530
  内存使用: 0.2690
  能量使用: 0.5868
  推理时间: 1.5206秒

批次 14:
  奖励值: 73.2112
  收益率: 0.6009
  距离: 17.6991
  内存使用: 0.3425
  能量使用: 0.5360
  推理时间: 1.5947秒

批次 15:
  奖励值: 76.4113
  收益率: 0.6147
  距离: 18.7657
  内存使用: 0.3869
  能量使用: 0.6247
  推理时间: 1.7503秒

批次 16:
  奖励值: 65.4118
  收益率: 0.5580
  距离: 20.0140
  内存使用: 0.5437
  能量使用: 0.6186
  推理时间: 1.6198秒

批次 17:
  奖励值: 73.7384
  收益率: 0.6183
  距离: 20.3364
  内存使用: 0.3536
  能量使用: 0.6331
  推理时间: 1.7524秒

批次 18:
  奖励值: 61.9253
  收益率: 0.5363
  距离: 16.8613
  内存使用: 0.5162
  能量使用: 0.5718
  推理时间: 1.5222秒

批次 19:
  奖励值: 70.0227
  收益率: 0.5716
  距离: 16.7432
  内存使用: 0.5952
  能量使用: 0.6161
  推理时间: 1.6291秒

批次 20:
  奖励值: 62.5638
  收益率: 0.5479
  距离: 17.0659
  内存使用: 0.2567
  能量使用: 0.5498
  推理时间: 1.5421秒

批次 21:
  奖励值: 73.2574
  收益率: 0.6099
  距离: 20.7098
  内存使用: 0.3321
  能量使用: 0.6434
  推理时间: 1.7254秒

批次 22:
  奖励值: 66.8088
  收益率: 0.5725
  距离: 19.2109
  内存使用: 0.5891
  能量使用: 0.5824
  推理时间: 1.6728秒

批次 23:
  奖励值: 70.9871
  收益率: 0.6161
  距离: 21.1257
  内存使用: 0.2700
  能量使用: 0.6302
  推理时间: 1.6943秒

批次 24:
  奖励值: 71.4489
  收益率: 0.5697
  距离: 19.6342
  内存使用: 0.2681
  能量使用: 0.6000
  推理时间: 1.5717秒

批次 25:
  奖励值: 59.6080
  收益率: 0.5129
  距离: 15.3412
  内存使用: 0.2846
  能量使用: 0.5581
  推理时间: 1.3741秒

批次 26:
  奖励值: 71.2661
  收益率: 0.5990
  距离: 20.4878
  内存使用: 0.3827
  能量使用: 0.5972
  推理时间: 1.6442秒

批次 27:
  奖励值: 78.2600
  收益率: 0.6264
  距离: 21.7473
  内存使用: 0.3992
  能量使用: 0.7335
  推理时间: 1.7551秒

批次 28:
  奖励值: 70.5680
  收益率: 0.5733
  距离: 19.1536
  内存使用: 0.2959
  能量使用: 0.6145
  推理时间: 1.6280秒

批次 29:
  奖励值: 83.7338
  收益率: 0.6230
  距离: 21.8208
  内存使用: 0.4105
  能量使用: 0.7011
  推理时间: 1.9754秒

批次 30:
  奖励值: 70.3930
  收益率: 0.5821
  距离: 19.3256
  内存使用: 0.3413
  能量使用: 0.5470
  推理时间: 1.6424秒

批次 31:
  奖励值: 73.1166
  收益率: 0.5994
  距离: 17.4652
  内存使用: 0.2670
  能量使用: 0.6346
  推理时间: 1.7566秒

批次 32:
  奖励值: 73.3492
  收益率: 0.5858
  距离: 19.5374
  内存使用: 0.3373
  能量使用: 0.6269
  推理时间: 1.7719秒

批次 33:
  奖励值: 70.6650
  收益率: 0.6097
  距离: 19.0456
  内存使用: 0.2661
  能量使用: 0.5583
  推理时间: 1.6990秒

批次 34:
  奖励值: 64.3084
  收益率: 0.5809
  距离: 20.4969
  内存使用: 0.2926
  能量使用: 0.5939
  推理时间: 1.5860秒

批次 35:
  奖励值: 71.9962
  收益率: 0.5956
  距离: 19.0572
  内存使用: 0.2747
  能量使用: 0.4756
  推理时间: 1.6350秒

批次 36:
  奖励值: 71.8320
  收益率: 0.6079
  距离: 21.8056
  内存使用: 0.6644
  能量使用: 0.6661
  推理时间: 1.7738秒

批次 37:
  奖励值: 71.7832
  收益率: 0.5925
  距离: 18.8737
  内存使用: 0.3588
  能量使用: 0.6010
  推理时间: 1.7236秒

批次 38:
  奖励值: 72.7274
  收益率: 0.6027
  距离: 20.7960
  内存使用: 0.3101
  能量使用: 0.6392
  推理时间: 1.7219秒

批次 39:
  奖励值: 64.5235
  收益率: 0.5678
  距离: 18.3108
  内存使用: 0.2761
  能量使用: 0.5663
  推理时间: 1.3159秒

批次 40:
  奖励值: 78.2794
  收益率: 0.6229
  距离: 19.0109
  内存使用: 0.2504
  能量使用: 0.5800
  推理时间: 1.7758秒

批次 41:
  奖励值: 68.8867
  收益率: 0.5894
  距离: 17.5711
  内存使用: 0.2340
  能量使用: 0.6215
  推理时间: 1.6296秒

批次 42:
  奖励值: 73.8656
  收益率: 0.5999
  距离: 18.3611
  内存使用: 0.3199
  能量使用: 0.5877
  推理时间: 1.7151秒

批次 43:
  奖励值: 67.7785
  收益率: 0.5601
  距离: 19.2607
  内存使用: 0.2661
  能量使用: 0.5161
  推理时间: 1.5884秒

批次 44:
  奖励值: 69.2037
  收益率: 0.5923
  距离: 19.9422
  内存使用: 0.2585
  能量使用: 0.5919
  推理时间: 1.6374秒

批次 45:
  奖励值: 69.0638
  收益率: 0.5856
  距离: 18.3800
  内存使用: 0.3255
  能量使用: 0.5669
  推理时间: 1.6682秒

批次 46:
  奖励值: 70.9831
  收益率: 0.5700
  距离: 15.8732
  内存使用: 0.3116
  能量使用: 0.6490
  推理时间: 1.6402秒

批次 47:
  奖励值: 69.9211
  收益率: 0.5696
  距离: 20.4334
  内存使用: 0.3157
  能量使用: 0.6086
  推理时间: 1.5018秒

批次 48:
  奖励值: 73.9169
  收益率: 0.6003
  距离: 18.2244
  内存使用: 0.3297
  能量使用: 0.6120
  推理时间: 1.5460秒

批次 49:
  奖励值: 71.8000
  收益率: 0.6094
  距离: 21.4365
  内存使用: 0.3275
  能量使用: 0.6361
  推理时间: 1.6399秒

批次 50:
  奖励值: 76.5739
  收益率: 0.6446
  距离: 21.0870
  内存使用: 0.3749
  能量使用: 0.5903
  推理时间: 1.6707秒

批次 51:
  奖励值: 67.2769
  收益率: 0.5632
  距离: 18.8292
  内存使用: 0.2402
  能量使用: 0.5769
  推理时间: 1.5021秒

批次 52:
  奖励值: 68.4047
  收益率: 0.5752
  距离: 18.2855
  内存使用: 0.3395
  能量使用: 0.5657
  推理时间: 1.5347秒

批次 53:
  奖励值: 68.9505
  收益率: 0.5807
  距离: 20.2517
  内存使用: 0.2766
  能量使用: 0.5707
  推理时间: 1.5582秒

批次 54:
  奖励值: 62.9922
  收益率: 0.5513
  距离: 18.2196
  内存使用: 0.2440
  能量使用: 0.5520
  推理时间: 1.5465秒

批次 55:
  奖励值: 61.8290
  收益率: 0.5289
  距离: 18.2609
  内存使用: 0.1997
  能量使用: 0.4814
  推理时间: 1.4867秒

批次 56:
  奖励值: 67.1418
  收益率: 0.5810
  距离: 19.0891
  内存使用: 0.2978
  能量使用: 0.5294
  推理时间: 1.6255秒

批次 57:
  奖励值: 73.5746
  收益率: 0.5889
  距离: 22.0827
  内存使用: 0.3625
  能量使用: 0.6056
  推理时间: 1.7875秒

批次 58:
  奖励值: 74.6292
  收益率: 0.6040
  距离: 20.0070
  内存使用: 0.3161
  能量使用: 0.6185
  推理时间: 1.7405秒

批次 59:
  奖励值: 64.4341
  收益率: 0.5422
  距离: 19.4946
  内存使用: 0.2955
  能量使用: 0.5417
  推理时间: 1.5853秒

批次 60:
  奖励值: 69.0510
  收益率: 0.5676
  距离: 16.4443
  内存使用: 0.2917
  能量使用: 0.5620
  推理时间: 1.6199秒

批次 61:
  奖励值: 67.7125
  收益率: 0.5821
  距离: 16.0848
  内存使用: 0.2737
  能量使用: 0.6166
  推理时间: 1.5912秒

批次 62:
  奖励值: 73.1705
  收益率: 0.6239
  距离: 21.8366
  内存使用: 0.3523
  能量使用: 0.6701
  推理时间: 1.7498秒

批次 63:
  奖励值: 64.5016
  收益率: 0.5355
  距离: 13.6230
  内存使用: 0.2272
  能量使用: 0.5466
  推理时间: 1.5119秒

批次 64:
  奖励值: 73.9935
  收益率: 0.6271
  距离: 22.1821
  内存使用: 0.3789
  能量使用: 0.6519
  推理时间: 1.7987秒

批次 65:
  奖励值: 79.0011
  收益率: 0.6448
  距离: 22.1545
  内存使用: 0.3826
  能量使用: 0.6608
  推理时间: 1.8139秒

批次 66:
  奖励值: 73.2513
  收益率: 0.5737
  距离: 16.0381
  内存使用: 0.3038
  能量使用: 0.5555
  推理时间: 1.6653秒

批次 67:
  奖励值: 63.1319
  收益率: 0.5523
  距离: 19.6951
  内存使用: 0.2988
  能量使用: 0.5422
  推理时间: 1.5594秒

批次 68:
  奖励值: 75.9773
  收益率: 0.6096
  距离: 20.6535
  内存使用: 0.3668
  能量使用: 0.6250
  推理时间: 1.7864秒

批次 69:
  奖励值: 73.6259
  收益率: 0.6083
  距离: 21.3327
  内存使用: 0.3694
  能量使用: 0.6461
  推理时间: 1.7605秒

批次 70:
  奖励值: 71.1120
  收益率: 0.6027
  距离: 19.4524
  内存使用: 0.3215
  能量使用: 0.6021
  推理时间: 1.7408秒

批次 71:
  奖励值: 73.7833
  收益率: 0.6135
  距离: 19.0888
  内存使用: 0.3158
  能量使用: 0.6287
  推理时间: 1.7367秒

批次 72:
  奖励值: 73.8307
  收益率: 0.5993
  距离: 16.8248
  内存使用: 0.3254
  能量使用: 0.5884
  推理时间: 1.7071秒

批次 73:
  奖励值: 70.0693
  收益率: 0.5757
  距离: 16.5097
  内存使用: 0.2060
  能量使用: 0.5457
  推理时间: 1.5351秒

批次 74:
  奖励值: 66.9429
  收益率: 0.5641
  距离: 20.0841
  内存使用: 0.2689
  能量使用: 0.5611
  推理时间: 1.5861秒

批次 75:
  奖励值: 63.7341
  收益率: 0.5520
  距离: 15.0040
  内存使用: 0.2292
  能量使用: 0.4987
  推理时间: 1.4906秒

批次 76:
  奖励值: 72.8787
  收益率: 0.5957
  距离: 18.9701
  内存使用: 0.3018
  能量使用: 0.5706
  推理时间: 1.6927秒

批次 77:
  奖励值: 72.2886
  收益率: 0.5980
  距离: 19.3035
  内存使用: 0.3094
  能量使用: 0.6099
  推理时间: 1.6581秒

批次 78:
  奖励值: 80.3353
  收益率: 0.6388
  距离: 17.2821
  内存使用: 0.4178
  能量使用: 0.6295
  推理时间: 1.8568秒

批次 79:
  奖励值: 68.8195
  收益率: 0.5869
  距离: 18.8494
  内存使用: 0.3261
  能量使用: 0.5855
  推理时间: 1.6306秒

批次 80:
  奖励值: 74.2417
  收益率: 0.5932
  距离: 18.6016
  内存使用: 0.3291
  能量使用: 0.5860
  推理时间: 1.7665秒

批次 81:
  奖励值: 62.9551
  收益率: 0.5404
  距离: 15.1235
  内存使用: 0.2158
  能量使用: 0.5506
  推理时间: 1.4386秒

批次 82:
  奖励值: 76.4452
  收益率: 0.6015
  距离: 17.9536
  内存使用: 0.3301
  能量使用: 0.6258
  推理时间: 1.7451秒

批次 83:
  奖励值: 80.0993
  收益率: 0.6318
  距离: 19.0785
  内存使用: 0.3481
  能量使用: 0.5748
  推理时间: 1.8536秒

批次 84:
  奖励值: 74.1498
  收益率: 0.6189
  距离: 19.3915
  内存使用: 0.3322
  能量使用: 0.5768
  推理时间: 1.7846秒

批次 85:
  奖励值: 66.5720
  收益率: 0.5648
  距离: 19.4510
  内存使用: 0.2541
  能量使用: 0.5508
  推理时间: 1.5906秒

批次 86:
  奖励值: 65.5098
  收益率: 0.5617
  距离: 18.3619
  内存使用: 0.2780
  能量使用: 0.5405
  推理时间: 1.5612秒

批次 87:
  奖励值: 73.8737
  收益率: 0.6084
  距离: 20.2791
  内存使用: 0.3793
  能量使用: 0.5942
  推理时间: 1.7581秒

批次 88:
  奖励值: 70.3221
  收益率: 0.5815
  距离: 20.5020
  内存使用: 0.2943
  能量使用: 0.5596
  推理时间: 1.6393秒

批次 89:
  奖励值: 69.2065
  收益率: 0.5792
  距离: 20.9049
  内存使用: 0.2898
  能量使用: 0.5923
  推理时间: 1.6003秒

批次 90:
  奖励值: 73.0275
  收益率: 0.5882
  距离: 19.8399
  内存使用: 0.3234
  能量使用: 0.5818
  推理时间: 1.6532秒

批次 91:
  奖励值: 63.6596
  收益率: 0.5259
  距离: 16.1461
  内存使用: 0.2375
  能量使用: 0.4972
  推理时间: 1.4570秒

批次 92:
  奖励值: 68.0481
  收益率: 0.5709
  距离: 17.6752
  内存使用: 0.2620
  能量使用: 0.5665
  推理时间: 1.5911秒

批次 93:
  奖励值: 70.2496
  收益率: 0.5850
  距离: 18.2866
  内存使用: 0.2808
  能量使用: 0.5334
  推理时间: 1.5943秒

批次 94:
  奖励值: 76.2984
  收益率: 0.6338
  距离: 21.2492
  内存使用: 0.3566
  能量使用: 0.5973
  推理时间: 1.8448秒

批次 95:
  奖励值: 68.2309
  收益率: 0.6017
  距离: 18.7479
  内存使用: 0.2884
  能量使用: 0.7018
  推理时间: 1.7096秒

批次 96:
  奖励值: 68.3717
  收益率: 0.5648
  距离: 20.2709
  内存使用: 0.2532
  能量使用: 0.5326
  推理时间: 1.6286秒

批次 97:
  奖励值: 71.7658
  收益率: 0.5962
  距离: 19.2001
  内存使用: 0.2793
  能量使用: 0.5889
  推理时间: 1.6829秒

批次 98:
  奖励值: 68.5748
  收益率: 0.5754
  距离: 19.9962
  内存使用: 0.2933
  能量使用: 0.5608
  推理时间: 1.6222秒

批次 99:
  奖励值: 64.7275
  收益率: 0.5547
  距离: 19.5314
  内存使用: 0.5008
  能量使用: 0.5127
  推理时间: 1.6016秒

批次 100:
  奖励值: 69.5858
  收益率: 0.5749
  距离: 16.6652
  内存使用: 0.2348
  能量使用: 0.5284
  推理时间: 1.5753秒


==================== 总结 ====================
平均收益率: 0.5882
平均能量使用: 0.5920
平均推理时间: 1.6549秒
