推理数据数量: 100
每个序列任务数量: 100
星座卫星数量: 3
星座模式: competitive
模型: gpn+indrnn
种子: 12348
内存总量: 0.3
电量总量: 5
检查点路径: constellation_smp/constellation_smp100/multi_constellation_comparison_2025_08_25_17_02_29/constellation_gpnindrnn_competitive_2025_08_26_05_01_59

批次 1:
  奖励值: 24.7756
  收益率: 0.6319
  距离: 7.2849
  内存使用: -0.0436
  能量使用: 0.2075
  推理时间: 0.9062秒

批次 2:
  奖励值: 30.0611
  收益率: 0.7478
  距离: 9.0826
  内存使用: -0.0290
  能量使用: 0.2241
  推理时间: 0.6441秒

批次 3:
  奖励值: 29.6840
  收益率: 0.7451
  距离: 8.7777
  内存使用: -0.0001
  能量使用: 0.2545
  推理时间: 0.6563秒

批次 4:
  奖励值: 28.7498
  收益率: 0.7078
  距离: 7.6601
  内存使用: -0.0414
  能量使用: 0.2817
  推理时间: 0.6991秒

批次 5:
  奖励值: 27.4439
  收益率: 0.6932
  距离: 5.9722
  内存使用: -0.0867
  能量使用: 0.2267
  推理时间: 0.6733秒

批次 6:
  奖励值: 30.1656
  收益率: 0.7026
  距离: 8.4617
  内存使用: -0.0473
  能量使用: 0.2544
  推理时间: 0.6945秒

批次 7:
  奖励值: 27.2078
  收益率: 0.7082
  距离: 8.9550
  内存使用: -0.0510
  能量使用: 0.2804
  推理时间: 0.6675秒

批次 8:
  奖励值: 30.5457
  收益率: 0.7441
  距离: 7.8923
  内存使用: -0.0592
  能量使用: 0.2755
  推理时间: 0.7315秒

批次 9:
  奖励值: 28.3677
  收益率: 0.7054
  距离: 8.6720
  内存使用: -0.0779
  能量使用: 0.2706
  推理时间: 0.6982秒

批次 10:
  奖励值: 29.4676
  收益率: 0.7185
  距离: 8.9186
  内存使用: 0.2548
  能量使用: 0.2914
  推理时间: 0.7317秒

批次 11:
  奖励值: 30.8914
  收益率: 0.7903
  距离: 9.5861
  内存使用: -0.0615
  能量使用: 0.2926
  推理时间: 0.7901秒

批次 12:
  奖励值: 32.5854
  收益率: 0.7490
  距离: 6.2088
  内存使用: -0.0485
  能量使用: 0.2689
  推理时间: 0.7423秒

批次 13:
  奖励值: 31.8278
  收益率: 0.7562
  距离: 9.2855
  内存使用: -0.0108
  能量使用: 0.2698
  推理时间: 0.7644秒

批次 14:
  奖励值: 29.2995
  收益率: 0.7428
  距离: 8.1627
  内存使用: -0.0364
  能量使用: 0.2747
  推理时间: 0.7376秒

批次 15:
  奖励值: 29.9001
  收益率: 0.7254
  距离: 9.1632
  内存使用: -0.0353
  能量使用: 0.2718
  推理时间: 0.6820秒

批次 16:
  奖励值: 30.9392
  收益率: 0.7427
  距离: 9.5023
  内存使用: -0.0277
  能量使用: 0.2472
  推理时间: 0.7011秒

批次 17:
  奖励值: 30.3739
  收益率: 0.7361
  距离: 7.5654
  内存使用: -0.0246
  能量使用: 0.3114
  推理时间: 0.7558秒

批次 18:
  奖励值: 25.8604
  收益率: 0.6786
  距离: 8.8975
  内存使用: -0.0636
  能量使用: 0.2453
  推理时间: 0.8040秒

批次 19:
  奖励值: 30.6637
  收益率: 0.7495
  距离: 10.8252
  内存使用: -0.0505
  能量使用: 0.2979
  推理时间: 0.7119秒

批次 20:
  奖励值: 27.6158
  收益率: 0.6875
  距离: 6.2030
  内存使用: -0.0532
  能量使用: 0.2185
  推理时间: 0.7167秒

批次 21:
  奖励值: 27.6560
  收益率: 0.6745
  距离: 7.9020
  内存使用: -0.0772
  能量使用: 0.2631
  推理时间: 0.6985秒

批次 22:
  奖励值: 29.8970
  收益率: 0.7473
  距离: 10.2997
  内存使用: 0.0429
  能量使用: 0.2634
  推理时间: 0.7580秒

批次 23:
  奖励值: 29.3319
  收益率: 0.7051
  距离: 7.1116
  内存使用: -0.0351
  能量使用: 0.2597
  推理时间: 0.6723秒

批次 24:
  奖励值: 27.9765
  收益率: 0.7117
  距离: 6.0417
  内存使用: -0.0482
  能量使用: 0.2534
  推理时间: 0.6425秒

批次 25:
  奖励值: 33.9172
  收益率: 0.7897
  距离: 8.5045
  内存使用: -0.0401
  能量使用: 0.3013
  推理时间: 0.8057秒

批次 26:
  奖励值: 30.2345
  收益率: 0.7727
  距离: 10.6191
  内存使用: 0.0088
  能量使用: 0.3030
  推理时间: 0.7657秒

批次 27:
  奖励值: 30.8614
  收益率: 0.7459
  距离: 10.8060
  内存使用: 0.0002
  能量使用: 0.2710
  推理时间: 0.8791秒

批次 28:
  奖励值: 28.8450
  收益率: 0.7235
  距离: 9.0825
  内存使用: -0.0062
  能量使用: 0.2336
  推理时间: 0.7608秒

批次 29:
  奖励值: 30.1326
  收益率: 0.7572
  距离: 7.5712
  内存使用: -0.0775
  能量使用: 0.2410
  推理时间: 0.6656秒

批次 30:
  奖励值: 30.4319
  收益率: 0.7424
  距离: 7.4892
  内存使用: -0.0234
  能量使用: 0.2704
  推理时间: 0.7298秒

批次 31:
  奖励值: 29.0857
  收益率: 0.6793
  距离: 9.8250
  内存使用: -0.0379
  能量使用: 0.2415
  推理时间: 0.7679秒

批次 32:
  奖励值: 30.2130
  收益率: 0.7249
  距离: 7.3146
  内存使用: -0.0151
  能量使用: 0.2746
  推理时间: 0.7942秒

批次 33:
  奖励值: 30.4696
  收益率: 0.7436
  距离: 8.4351
  内存使用: -0.0502
  能量使用: 0.2759
  推理时间: 0.7873秒

批次 34:
  奖励值: 30.0949
  收益率: 0.7355
  距离: 8.0340
  内存使用: -0.0426
  能量使用: 0.2187
  推理时间: 0.7643秒

批次 35:
  奖励值: 28.4600
  收益率: 0.6992
  距离: 8.8421
  内存使用: -0.0279
  能量使用: 0.2659
  推理时间: 0.7778秒

批次 36:
  奖励值: 37.4195
  收益率: 0.8383
  距离: 8.8682
  内存使用: -0.0371
  能量使用: 0.3299
  推理时间: 0.8178秒

批次 37:
  奖励值: 25.9018
  收益率: 0.7005
  距离: 6.0564
  内存使用: -0.0936
  能量使用: 0.2336
  推理时间: 0.7234秒

批次 38:
  奖励值: 27.7849
  收益率: 0.7373
  距离: 8.2806
  内存使用: -0.0831
  能量使用: 0.2327
  推理时间: 0.7424秒

批次 39:
  奖励值: 31.6966
  收益率: 0.7692
  距离: 8.3748
  内存使用: -0.0317
  能量使用: 0.2637
  推理时间: 0.7806秒

批次 40:
  奖励值: 28.2558
  收益率: 0.7222
  距离: 8.2797
  内存使用: -0.0589
  能量使用: 0.2676
  推理时间: 1.0875秒

批次 41:
  奖励值: 28.2865
  收益率: 0.7330
  距离: 8.0309
  内存使用: -0.0517
  能量使用: 0.2477
  推理时间: 0.7199秒

批次 42:
  奖励值: 32.0354
  收益率: 0.7516
  距离: 8.3141
  内存使用: -0.0332
  能量使用: 0.2481
  推理时间: 0.8183秒

批次 43:
  奖励值: 29.8014
  收益率: 0.7229
  距离: 6.9538
  内存使用: -0.0084
  能量使用: 0.2518
  推理时间: 0.7927秒

批次 44:
  奖励值: 31.9346
  收益率: 0.7573
  距离: 8.3022
  内存使用: -0.0605
  能量使用: 0.2892
  推理时间: 0.8475秒

批次 45:
  奖励值: 29.3588
  收益率: 0.7121
  距离: 6.9237
  内存使用: -0.0557
  能量使用: 0.2143
  推理时间: 0.7048秒

批次 46:
  奖励值: 30.1771
  收益率: 0.7338
  距离: 7.3048
  内存使用: -0.0936
  能量使用: 0.2347
  推理时间: 0.6685秒

批次 47:
  奖励值: 28.5815
  收益率: 0.7228
  距离: 7.8448
  内存使用: -0.0761
  能量使用: 0.2683
  推理时间: 0.6910秒

批次 48:
  奖励值: 30.5593
  收益率: 0.7532
  距离: 9.1560
  内存使用: 0.2570
  能量使用: 0.2683
  推理时间: 0.7693秒

批次 49:
  奖励值: 34.0112
  收益率: 0.7744
  距离: 7.9997
  内存使用: -0.0612
  能量使用: 0.2391
  推理时间: 0.7449秒

批次 50:
  奖励值: 32.0922
  收益率: 0.7474
  距离: 7.2007
  内存使用: -0.0648
  能量使用: 0.2797
  推理时间: 0.7130秒

批次 51:
  奖励值: 29.6687
  收益率: 0.7283
  距离: 7.4226
  内存使用: -0.0302
  能量使用: 0.2702
  推理时间: 0.7085秒

批次 52:
  奖励值: 29.7568
  收益率: 0.7468
  距离: 9.2714
  内存使用: -0.0228
  能量使用: 0.2378
  推理时间: 0.7187秒

批次 53:
  奖励值: 33.5656
  收益率: 0.8079
  距离: 9.9396
  内存使用: 0.0208
  能量使用: 0.3083
  推理时间: 0.9154秒

批次 54:
  奖励值: 29.3489
  收益率: 0.7271
  距离: 7.7006
  内存使用: -0.0401
  能量使用: 0.2416
  推理时间: 0.7870秒

批次 55:
  奖励值: 31.2380
  收益率: 0.7447
  距离: 7.8624
  内存使用: -0.0170
  能量使用: 0.2786
  推理时间: 0.8241秒

批次 56:
  奖励值: 29.4785
  收益率: 0.7539
  距离: 8.8284
  内存使用: -0.0334
  能量使用: 0.2480
  推理时间: 0.7810秒

批次 57:
  奖励值: 27.3469
  收益率: 0.7206
  距离: 10.0594
  内存使用: -0.0294
  能量使用: 0.2761
  推理时间: 0.7568秒

批次 58:
  奖励值: 30.0082
  收益率: 0.7884
  距离: 10.5158
  内存使用: -0.0472
  能量使用: 0.3150
  推理时间: 0.8602秒

批次 59:
  奖励值: 34.6723
  收益率: 0.7787
  距离: 9.1960
  内存使用: -0.0145
  能量使用: 0.2739
  推理时间: 0.8796秒

批次 60:
  奖励值: 29.6269
  收益率: 0.7322
  距离: 8.3001
  内存使用: -0.0093
  能量使用: 0.2554
  推理时间: 0.8155秒

批次 61:
  奖励值: 29.7187
  收益率: 0.7467
  距离: 8.7202
  内存使用: -0.0321
  能量使用: 0.2745
  推理时间: 0.8193秒

批次 62:
  奖励值: 26.3743
  收益率: 0.6993
  距离: 7.1285
  内存使用: -0.0968
  能量使用: 0.2196
  推理时间: 0.6921秒

批次 63:
  奖励值: 27.1988
  收益率: 0.7330
  距离: 10.1411
  内存使用: -0.0171
  能量使用: 0.2784
  推理时间: 0.8133秒

批次 64:
  奖励值: 25.9844
  收益率: 0.6932
  距离: 7.0258
  内存使用: -0.0492
  能量使用: 0.2545
  推理时间: 0.7675秒

批次 65:
  奖励值: 33.2159
  收益率: 0.7853
  距离: 10.1814
  内存使用: -0.0483
  能量使用: 0.2351
  推理时间: 0.9152秒

批次 66:
  奖励值: 29.6205
  收益率: 0.7321
  距离: 9.9290
  内存使用: -0.0349
  能量使用: 0.2649
  推理时间: 0.7975秒

批次 67:
  奖励值: 30.2745
  收益率: 0.7333
  距离: 7.4032
  内存使用: -0.0296
  能量使用: 0.2773
  推理时间: 0.8275秒

批次 68:
  奖励值: 28.4783
  收益率: 0.7254
  距离: 7.8572
  内存使用: -0.0533
  能量使用: 0.2396
  推理时间: 0.7646秒

批次 69:
  奖励值: 29.3965
  收益率: 0.7325
  距离: 7.8250
  内存使用: -0.0636
  能量使用: 0.2455
  推理时间: 0.7728秒

批次 70:
  奖励值: 30.2237
  收益率: 0.7181
  距离: 9.1656
  内存使用: -0.0629
  能量使用: 0.2499
  推理时间: 0.7827秒

批次 71:
  奖励值: 32.1103
  收益率: 0.7747
  距离: 7.7484
  内存使用: -0.0454
  能量使用: 0.2764
  推理时间: 0.8027秒

批次 72:
  奖励值: 27.6958
  收益率: 0.6938
  距离: 7.4428
  内存使用: -0.0803
  能量使用: 0.2460
  推理时间: 0.7531秒

批次 73:
  奖励值: 35.6936
  收益率: 0.8032
  距离: 9.0741
  内存使用: 0.0035
  能量使用: 0.3352
  推理时间: 0.9318秒

批次 74:
  奖励值: 32.9588
  收益率: 0.7582
  距离: 7.8335
  内存使用: -0.0417
  能量使用: 0.2907
  推理时间: 0.8803秒

批次 75:
  奖励值: 28.3619
  收益率: 0.7133
  距离: 8.2724
  内存使用: -0.0560
  能量使用: 0.2597
  推理时间: 0.7535秒

批次 76:
  奖励值: 27.2374
  收益率: 0.7136
  距离: 7.1361
  内存使用: -0.0622
  能量使用: 0.2513
  推理时间: 0.7498秒

批次 77:
  奖励值: 32.6560
  收益率: 0.7628
  距离: 9.0730
  内存使用: -0.0398
  能量使用: 0.2548
  推理时间: 0.8146秒

批次 78:
  奖励值: 29.2969
  收益率: 0.7357
  距离: 8.0302
  内存使用: -0.0794
  能量使用: 0.2554
  推理时间: 0.7551秒

批次 79:
  奖励值: 27.3275
  收益率: 0.7237
  距离: 6.9797
  内存使用: -0.0512
  能量使用: 0.2166
  推理时间: 0.7349秒

批次 80:
  奖励值: 28.4833
  收益率: 0.7067
  距离: 6.4615
  内存使用: -0.0660
  能量使用: 0.2379
  推理时间: 0.7251秒

批次 81:
  奖励值: 22.7187
  收益率: 0.6584
  距离: 7.2590
  内存使用: -0.1000
  能量使用: 0.2037
  推理时间: 0.6875秒

批次 82:
  奖励值: 33.4380
  收益率: 0.7510
  距离: 7.0186
  内存使用: 0.0065
  能量使用: 0.2990
  推理时间: 0.8909秒

批次 83:
  奖励值: 34.3751
  收益率: 0.8020
  距离: 10.3110
  内存使用: 0.0033
  能量使用: 0.3354
  推理时间: 0.9997秒

批次 84:
  奖励值: 29.7441
  收益率: 0.7008
  距离: 7.3429
  内存使用: -0.0476
  能量使用: 0.2166
  推理时间: 0.6814秒

批次 85:
  奖励值: 30.5897
  收益率: 0.7301
  距离: 8.4149
  内存使用: -0.0580
  能量使用: 0.2637
  推理时间: 0.7081秒

批次 86:
  奖励值: 26.6432
  收益率: 0.6836
  距离: 8.3198
  内存使用: -0.0552
  能量使用: 0.2491
  推理时间: 0.7057秒

批次 87:
  奖励值: 28.5580
  收益率: 0.7184
  距离: 7.4543
  内存使用: -0.0639
  能量使用: 0.2935
  推理时间: 0.7011秒

批次 88:
  奖励值: 32.2137
  收益率: 0.7510
  距离: 7.1885
  内存使用: -0.0293
  能量使用: 0.2134
  推理时间: 0.7499秒

批次 89:
  奖励值: 27.1081
  收益率: 0.6964
  距离: 8.0563
  内存使用: -0.0644
  能量使用: 0.1918
  推理时间: 0.7019秒

批次 90:
  奖励值: 26.9580
  收益率: 0.7225
  距离: 8.8810
  内存使用: -0.0475
  能量使用: 0.2505
  推理时间: 0.6098秒

批次 91:
  奖励值: 29.2494
  收益率: 0.7338
  距离: 9.0761
  内存使用: -0.0443
  能量使用: 0.2695
  推理时间: 0.7328秒

批次 92:
  奖励值: 32.1733
  收益率: 0.7618
  距离: 9.6037
  内存使用: -0.0123
  能量使用: 0.2620
  推理时间: 0.7906秒

批次 93:
  奖励值: 33.5096
  收益率: 0.7425
  距离: 7.1170
  内存使用: -0.0075
  能量使用: 0.2713
  推理时间: 0.7794秒

批次 94:
  奖励值: 28.4066
  收益率: 0.7444
  距离: 9.3401
  内存使用: -0.0275
  能量使用: 0.2741
  推理时间: 0.7334秒

批次 95:
  奖励值: 30.4695
  收益率: 0.7157
  距离: 8.8668
  内存使用: -0.0408
  能量使用: 0.2350
  推理时间: 0.7201秒

批次 96:
  奖励值: 29.7479
  收益率: 0.7112
  距离: 6.3783
  内存使用: -0.0830
  能量使用: 0.2088
  推理时间: 0.7175秒

批次 97:
  奖励值: 27.4418
  收益率: 0.6849
  距离: 7.4898
  内存使用: -0.0353
  能量使用: 0.2620
  推理时间: 0.6889秒

批次 98:
  奖励值: 31.8042
  收益率: 0.7300
  距离: 9.1608
  内存使用: -0.0590
  能量使用: 0.2898
  推理时间: 0.7254秒

批次 99:
  奖励值: 26.9060
  收益率: 0.7294
  距离: 8.0004
  内存使用: -0.0742
  能量使用: 0.2617
  推理时间: 0.7124秒

批次 100:
  奖励值: 33.8199
  收益率: 0.7764
  距离: 8.5644
  内存使用: -0.0613
  能量使用: 0.2571
  推理时间: 0.8609秒


==================== 总结 ====================
平均收益率: 0.7335
平均能量使用: 0.2605
平均推理时间: 0.7620秒
