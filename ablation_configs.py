"""
消融实验配置定义文件
基于消融实验设计方案.md中的实验设计

包含:
1. Transformer架构消融配置
2. 星座协同机制消融配置  
3. 在轨充电功能消融配置
4. 实验评估指标定义
"""

import numpy as np

# ============================================================================
# Transformer架构消融配置
# ============================================================================

TRANSFORMER_ABLATION_CONFIGS = [
    # 基线：无Transformer
    {
        'name': 'baseline_no_transformer',
        'use_transformer': False,
        'config': None,
        'description': '基线配置，不使用Transformer',
        'expected_params': '~2.2M',
        'complexity': 'low'
    },
    
    # 轻量级配置
    {
        'name': 'lightweight',
        'use_transformer': True,
        'config': {
            'd_model': 128,
            'num_heads': 2,
            'd_ff': 256,
            'num_layers': 1,
            'max_len': 5000,
            'dropout': 0.1,
            'activation': 'gelu'
        },
        'description': '轻量级配置: 1层, 2头, 128维',
        'expected_params': '~2.4M',
        'complexity': 'low'
    },
    
    # 默认配置（推荐配置）
    {
        'name': 'default',
        'use_transformer': True,
        'config': {
            'd_model': 256,
            'num_heads': 4,
            'd_ff': 512,
            'num_layers': 2,
            'max_len': 5000,
            'dropout': 0.1,
            'activation': 'gelu'
        },
        'description': '默认配置: 2层, 4头, 256维',
        'expected_params': '~4.2M',
        'complexity': 'medium'
    },
    
    # 深层配置
    {
        'name': 'deep',
        'use_transformer': True,
        'config': {
            'd_model': 512,
            'num_heads': 8,
            'd_ff': 1024,
            'num_layers': 3,
            'max_len': 5000,
            'dropout': 0.1,
            'activation': 'gelu'
        },
        'description': '深层配置: 3层, 8头, 512维',
        'expected_params': '~8.5M',
        'complexity': 'high'
    },
    
    # 超深层配置
    {
        'name': 'ultra_deep',
        'use_transformer': True,
        'config': {
            'd_model': 256,
            'num_heads': 8,
            'd_ff': 512,
            'num_layers': 4,
            'max_len': 5000,
            'dropout': 0.1,
            'activation': 'gelu'
        },
        'description': '超深层配置: 4层, 8头, 256维',
        'expected_params': '~6.8M',
        'complexity': 'high'
    },
    
    # 宽模型配置
    {
        'name': 'wide',
        'use_transformer': True,
        'config': {
            'd_model': 512,
            'num_heads': 16,
            'd_ff': 2048,
            'num_layers': 2,
            'max_len': 5000,
            'dropout': 0.1,
            'activation': 'gelu'
        },
        'description': '宽模型配置: 2层, 16头, 512维',
        'expected_params': '~12.5M',
        'complexity': 'very_high'
    },
    
    # 高dropout配置（测试正则化效果）
    {
        'name': 'high_dropout',
        'use_transformer': True,
        'config': {
            'd_model': 256,
            'num_heads': 4,
            'd_ff': 512,
            'num_layers': 2,
            'max_len': 5000,
            'dropout': 0.3,
            'activation': 'gelu'
        },
        'description': '高dropout配置: dropout=0.3',
        'expected_params': '~4.2M',
        'complexity': 'medium'
    },
    
    # ReLU激活函数配置
    {
        'name': 'relu_activation',
        'use_transformer': True,
        'config': {
            'd_model': 256,
            'num_heads': 4,
            'd_ff': 512,
            'num_layers': 2,
            'max_len': 5000,
            'dropout': 0.1,
            'activation': 'relu'
        },
        'description': 'ReLU激活函数配置',
        'expected_params': '~4.2M',
        'complexity': 'medium'
    }
]

# ============================================================================
# 星座协同机制消融配置
# ============================================================================

CONSTELLATION_MODE_ABLATION_CONFIGS = [
    {
        'name': 'competitive',
        'mode': 'competitive',
        'description': '竞争模式: 无信息交互',
        'communication_enabled': False,
        'info_sharing': False
    },
    {
        'name': 'cooperative',
        'mode': 'cooperative', 
        'description': '协同模式: 完全信息共享',
        'communication_enabled': True,
        'info_sharing': True
    },
    {
        'name': 'hybrid',
        'mode': 'hybrid',
        'description': '混合模式: 门控信息交互',
        'communication_enabled': True,
        'info_sharing': 'gated'
    }
]

# ============================================================================
# 在轨充电功能消融配置
# ============================================================================

CHARGING_ABLATION_CONFIGS = [
    {
        'name': 'no_charging',
        'charging_rate': 0.0,
        'description': '无充电功能基线'
    },
    {
        'name': 'low_charging',
        'charging_rate': 0.001,
        'description': '低速充电'
    },
    {
        'name': 'medium_charging',
        'charging_rate': 0.003,
        'description': '中速充电'
    },
    {
        'name': 'default_charging',
        'charging_rate': 0.005,
        'description': '默认充电速率'
    },
    {
        'name': 'high_charging',
        'charging_rate': 0.007,
        'description': '高速充电'
    },
    {
        'name': 'ultra_charging',
        'charging_rate': 0.01,
        'description': '超高速充电'
    }
]

# ============================================================================
# 实验评估指标
# ============================================================================

EVALUATION_METRICS = [
    'reward',                    # 总奖励
    'revenue_rate',             # 收益率
    'distance',                 # 总距离
    'memory_usage',             # 内存使用率
    'power_consumption',        # 功耗
    'training_time',            # 训练时间
    'inference_time',           # 推理时间
    'model_parameters',         # 模型参数数量
    'task_completion_rate',     # 任务完成率
    'energy_efficiency'         # 能量利用效率
]

# ============================================================================
# 实验场景配置
# ============================================================================

TEST_SCENARIOS = {
    'easy': {
        'task_density': 0.3,
        'time_pressure': 'low',
        'num_nodes': 50,
        'description': '简单场景: 低任务密度, 低时间压力'
    },
    'medium': {
        'task_density': 0.5,
        'time_pressure': 'medium',
        'num_nodes': 100,
        'description': '中等场景: 中任务密度, 中等时间压力'
    },
    'hard': {
        'task_density': 0.8,
        'time_pressure': 'high',
        'num_nodes': 200,
        'description': '困难场景: 高任务密度, 高时间压力'
    }
}

# ============================================================================
# 实验配置生成器
# ============================================================================

def generate_full_ablation_matrix():
    """生成完整的消融实验矩阵"""
    experiment_matrix = []
    
    # Transformer × 星座模式 × 充电配置的组合
    for transformer_config in TRANSFORMER_ABLATION_CONFIGS:
        for constellation_config in CONSTELLATION_MODE_ABLATION_CONFIGS:
            for charging_config in CHARGING_ABLATION_CONFIGS:
                experiment = {
                    'experiment_id': f"{transformer_config['name']}_{constellation_config['name']}_{charging_config['name']}",
                    'transformer': transformer_config,
                    'constellation': constellation_config,
                    'charging': charging_config,
                    'description': f"{transformer_config['description']} + {constellation_config['description']} + {charging_config['description']}"
                }
                experiment_matrix.append(experiment)
    
    return experiment_matrix

def generate_transformer_only_ablation():
    """生成仅Transformer的消融实验"""
    return TRANSFORMER_ABLATION_CONFIGS

def generate_constellation_only_ablation():
    """生成仅星座模式的消融实验"""
    return CONSTELLATION_MODE_ABLATION_CONFIGS

def generate_charging_only_ablation():
    """生成仅充电功能的消融实验"""
    return CHARGING_ABLATION_CONFIGS

# ============================================================================
# 实验统计分析工具
# ============================================================================

def calculate_statistical_significance(baseline_results, experiment_results, alpha=0.05):
    """计算统计显著性"""
    from scipy import stats
    
    # t检验
    t_stat, p_value = stats.ttest_ind(baseline_results, experiment_results)
    
    # 效应大小 (Cohen's d)
    pooled_std = np.sqrt(((len(baseline_results)-1)*np.var(baseline_results) +
                         (len(experiment_results)-1)*np.var(experiment_results)) /
                        (len(baseline_results)+len(experiment_results)-2))
    cohens_d = (np.mean(experiment_results) - np.mean(baseline_results)) / pooled_std
    
    return {
        't_statistic': t_stat,
        'p_value': p_value,
        'cohens_d': cohens_d,
        'significant': p_value < alpha,
        'effect_size': 'small' if abs(cohens_d) < 0.5 else 'medium' if abs(cohens_d) < 0.8 else 'large'
    }

def calculate_performance_improvement(baseline_value, experiment_value):
    """计算性能提升百分比"""
    if baseline_value == 0:
        return float('inf') if experiment_value > 0 else 0
    return ((experiment_value - baseline_value) / baseline_value) * 100

# ============================================================================
# 实验配置验证
# ============================================================================

def validate_experiment_config(config):
    """验证实验配置的有效性"""
    required_fields = ['name', 'use_transformer', 'description']
    
    for field in required_fields:
        if field not in config:
            raise ValueError(f"配置缺少必需字段: {field}")
    
    if config['use_transformer'] and 'config' not in config:
        raise ValueError(f"启用Transformer但缺少配置: {config['name']}")
    
    if config['use_transformer'] and config['config']:
        transformer_config = config['config']
        required_transformer_fields = ['d_model', 'num_heads', 'num_layers']
        
        for field in required_transformer_fields:
            if field not in transformer_config:
                raise ValueError(f"Transformer配置缺少必需字段: {field}")
    
    return True

def print_config_summary():
    """打印配置摘要"""
    print("📋 消融实验配置摘要")
    print("=" * 50)
    
    print(f"Transformer配置数量: {len(TRANSFORMER_ABLATION_CONFIGS)}")
    print(f"星座模式配置数量: {len(CONSTELLATION_MODE_ABLATION_CONFIGS)}")
    print(f"充电功能配置数量: {len(CHARGING_ABLATION_CONFIGS)}")
    
    total_combinations = (len(TRANSFORMER_ABLATION_CONFIGS) * 
                         len(CONSTELLATION_MODE_ABLATION_CONFIGS) * 
                         len(CHARGING_ABLATION_CONFIGS))
    print(f"总组合数量: {total_combinations}")
    
    print("\nTransformer配置:")
    for config in TRANSFORMER_ABLATION_CONFIGS:
        print(f"  • {config['name']}: {config['description']}")
    
    print("\n星座模式配置:")
    for config in CONSTELLATION_MODE_ABLATION_CONFIGS:
        print(f"  • {config['name']}: {config['description']}")
    
    print("\n充电功能配置:")
    for config in CHARGING_ABLATION_CONFIGS:
        print(f"  • {config['name']}: {config['description']}")

if __name__ == '__main__':
    print_config_summary()
    
    # 验证所有配置
    print("\n🔍 验证配置有效性...")
    try:
        for config in TRANSFORMER_ABLATION_CONFIGS:
            validate_experiment_config(config)
        print("✅ 所有Transformer配置验证通过")
        
    except Exception as e:
        print(f"❌ 配置验证失败: {e}")
